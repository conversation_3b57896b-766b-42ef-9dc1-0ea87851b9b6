<template>
  <!-- 出荷予定修正ダイアログ -->
  <DialogWindow
    :title="$t('Sog.Chr.txtSogEditShipmentPlan')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="async () => checkSogEditShipmentPlanForm()"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <CustomForm
      :formModel="sogEditShipmentPlanRef.formModel"
      :formItems="sogEditShipmentPlanRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          sogEditShipmentPlanRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 出荷予定修正の確認メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.sogEditShipmentPlanConfirm"
    :dialogProps="messageBoxSogEditShipmentPlanConfirm"
    :cancelCallback="() => closeDialog('sogEditShipmentPlanConfirm')"
    :submitCallback="apiHandler"
  />
  <!-- 出荷予定修正完了の情報表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxSogEditShipmentPlanCompletedVisible"
    :dialogProps="messageBoxSogEditShipmentPlanCompletedRef"
    :submitCallback="closeAllDialog"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import onValidateHandler from '@/utils/validateHandler';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import {
  useGetComboBoxDataStandard,
  useGetSogPlan,
  useModifySogPlan,
} from '@/hooks/useApi';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import { toNumberOrNull } from '@/utils/index';
import { closeLoading, showLoading } from '@/utils/dialog';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import {
  ExtendCommonRequestType,
  CommonRequestType,
  ComboBoxDataStandardReturnData,
} from '@/types/HookUseApi/CommonTypes';
import {
  ModifySogPlanReq,
  GetSogPlanReq,
  GetSogPlanData,
  GetSogPlanListData,
} from '@/types/HookUseApi/SogTypes';
import {
  sogEditShipmentPlanFormModel,
  getSogEditShipmentPlanFormItems,
} from './sogEditShipmentPlan';

const { t } = useI18n();
const props = defineProps<Props>();
type Props = {
  selectedRows: GetSogPlanListData[];
  isClicked: boolean;
  privilegesBtnRequestData: CommonRequestType;
};

const emit = defineEmits(['submit']);

const getSogPlanReqData: GetSogPlanReq = {
  sogPlanNo: '',
};

let comboBoxResData: ComboBoxDataStandardReturnData | undefined;

type DialogRefKey =
  | 'messageBoxApiErrorVisible'
  | 'sogEditShipmentPlanConfirm'
  | 'fragmentDialogVisible'
  | 'messageBoxSogEditShipmentPlanCompletedVisible';

const initialState: InitialDialogState<DialogRefKey> = {
  messageBoxApiErrorVisible: false,
  sogEditShipmentPlanConfirm: false,
  fragmentDialogVisible: false,
  messageBoxSogEditShipmentPlanCompletedVisible: false,
};

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

const messageBoxSogEditShipmentPlanConfirm: DialogProps = {
  title: t('Sog.Chr.txtSogEditShipmentPlan'),
  content: t('Sog.Msg.txtEditShippingConfirm'),
  type: 'question',
};
const messageBoxSogEditShipmentPlanCompletedRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const sogEditShipmentPlanRef = ref<CustomFormType>({
  formItems: getSogEditShipmentPlanFormItems(),
  formModel: sogEditShipmentPlanFormModel,
});

const getEditPlanDataByInit = ref<GetSogPlanData>({
  sogPlanNo: '', // 出荷予定番号
  sogPlanStsNm: '', // 出荷予定状態
  sogPlanYmd: '', // 出荷予定日
  bpTrfId: '', // 出荷先コード
  bpTrfNm: '', // 出荷先名
  matNo: '', // 品目コード
  matNm: '', // 品名
  lotNo: '', // 製造番号
  sogQty: '', // 出荷予定量
  unitNm: '', // 単位
  releaseRsltNm: '', // 出荷判定
  lotStsNm: '', // 品質状態
  lockFlgNm: '', // ロットロック
  expiryDts: '', // 使用期限日
  pltCnt: null, // パレット数
  baleCnt: null, // 段ボール数
  srcZoneGrpNo: '', // 出庫元ゾーングループコード
  srcZoneGrpNm: '', // 出庫元ゾーングループ名
  sogPlanUpdDts: '', // 出荷予定更新日時
});

/**
 * 出荷予定修正チェック
 */
let currentSogPlanUpdDts: string = '';
const checkSogEditShipmentPlanForm = async () => {
  const validate =
    sogEditShipmentPlanRef.value.customForm !== undefined &&
    (await sogEditShipmentPlanRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));
  if (validate) {
    openDialog('sogEditShipmentPlanConfirm');
  }
  return false;
};

const apiHandler = async () => {
  closeDialog('sogEditShipmentPlanConfirm');
  showLoading();
  const apiRequestData: ExtendCommonRequestType<ModifySogPlanReq> = {
    ...props.privilegesBtnRequestData,
    sogPlanNo:
      sogEditShipmentPlanRef.value.formItems.sogPlanNo.formModelValue.toString(),
    sogPlanYmd:
      sogEditShipmentPlanRef.value.formItems.sogPlanYmd.formModelValue.toString(),
    bpTrfId:
      sogEditShipmentPlanRef.value.formItems.bpTrfId.formModelValue.toString(),
    lotNo:
      sogEditShipmentPlanRef.value.formItems.lotNo.formModelValue.toString(),
    sogQty:
      sogEditShipmentPlanRef.value.formItems.sogQty.formModelValue.toString(),
    pltCnt: toNumberOrNull(
      sogEditShipmentPlanRef.value.formItems.pltCnt.formModelValue,
    ),
    baleCnt: toNumberOrNull(
      sogEditShipmentPlanRef.value.formItems.baleCnt.formModelValue,
    ),
    srcZoneGrpNo:
      sogEditShipmentPlanRef.value.formItems.srcZoneGrpNo.formModelValue.toString(),
    sogPlanUpdDts: currentSogPlanUpdDts.toString(),
    msgboxTitleTxt: messageBoxSogEditShipmentPlanConfirm.title,
    msgboxMsgTxt: messageBoxSogEditShipmentPlanConfirm.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
  };
  const { responseRef, errorRef } = await useModifySogPlan(apiRequestData);
  if (errorRef.value) {
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    closeLoading();
    return false;
  }
  if (responseRef.value) {
    messageBoxSogEditShipmentPlanCompletedRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxSogEditShipmentPlanCompletedRef.value.content =
      responseRef.value.data.rMsg;
    openDialog('messageBoxSogEditShipmentPlanCompletedVisible');
  }
  closeLoading();
  return true;
};

const closeAllDialog = () => {
  closeDialog('messageBoxSogEditShipmentPlanCompletedVisible');
  closeDialog('sogEditShipmentPlanConfirm');
  closeDialog('fragmentDialogVisible');
  emit('submit', props.privilegesBtnRequestData);
};

/**
 * 初期設定
 */
const SogEditShipmentPlanInit = async () => {
  showLoading();
  updateDialogChangeFlagRef(false);
  sogEditShipmentPlanRef.value.formItems = getSogEditShipmentPlanFormItems();
  comboBoxResData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'bpTrfId',
        condKey: 'm_bp_trf',
      },
      {
        cmbId: 'matNo',
        condKey: 'm_mat',
        optionCol: {
          mes_unit_nm: 'unitNmJp',
        },
      },
      {
        cmbId: 'srcZoneGrpNo',
        condKey: 'm_ic_zone_grp',
        where: {
          dsp_flg_sog1: '1',
        },
      },
    ],
  });
  if (comboBoxResData && comboBoxResData.rData.rList.length > 0) {
    setCustomFormComboBoxOptionList(
      sogEditShipmentPlanRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }
  // 出荷予定修正
  if (!props.selectedRows) return;
  sogEditShipmentPlanRef.value.formItems.matNo.tags = [];
  getSogPlanReqData.sogPlanNo = props.selectedRows.at(0)!.sogPlanNo.toString();
  if (
    sogEditShipmentPlanRef.value.formItems.matNo.formRole === 'selectComboBox'
  ) {
    sogEditShipmentPlanRef.value.formItems.matNo.props!.disabled = true;
  }
  const { responseRef, errorRef } = await useGetSogPlan({
    ...props.privilegesBtnRequestData,
    ...getSogPlanReqData,
  });
  if (errorRef.value) {
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    closeLoading();
    return;
  }
  if (responseRef.value) {
    getEditPlanDataByInit.value = responseRef.value.data.rData;
    currentSogPlanUpdDts = responseRef.value.data.rData.sogPlanUpdDts;
    setFormModelValueFromApiResponse(
      sogEditShipmentPlanRef,
      getEditPlanDataByInit.value,
    );
  }
  closeLoading();
  openDialog('fragmentDialogVisible');
};

watch(() => props.isClicked, SogEditShipmentPlanInit);
</script>

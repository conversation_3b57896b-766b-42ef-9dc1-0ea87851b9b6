<template>
  <!-- 製造指図記録詳細ダイアログ -->
  <!-- 見出し 製造指図記録詳細 -->
  <DialogWindow
    :title="$t('Sjg.Chr.txtManufacturingInstructionRecordDetails')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :leftButtons="dialogLeftButtons"
    :rightButtons="dialogRightButtons"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!-- 投入修正履歴 -->
    <BaseHeading
      level="2"
      :text="$t('Sjg.Chr.txtBomModList')"
      fontSize="24px"
      class="Util_mt-16"
    />
    <!--  投入修正履歴内容テーブル  -->
    <TabulatorTable :propsData="tablePropsDataBomModListRef" />

    <!-- 出来高修正履歴 -->
    <BaseHeading
      level="2"
      :text="$t('Sjg.Chr.txtPrdModList')"
      fontSize="24px"
      class="Util_mt-16"
    />
    <!-- 出来高修正履歴テーブル -->
    <TabulatorTable :propsData="tablePropsDataPrdModListRef" />
    <!-- 異状履歴 -->
    <BaseHeading
      level="2"
      :text="$t('Sjg.Chr.txtDevModList')"
      fontSize="24px"
      class="Util_mt-16"
    />
    <!-- 異状履歴内容テーブル -->
    <TabulatorTable :propsData="tablePropsDataDevModListRef" />
    <!-- SOP修正履歴 -->
    <BaseHeading
      level="2"
      :text="$t('Sjg.Chr.txtSopModList')"
      fontSize="24px"
      class="Util_mt-16"
    />
    <!-- SOP修正履歴内容テーブル -->
    <TabulatorTable :propsData="tablePropsDataSopModListRef" />
  </DialogWindow>
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import {
  TabulatorTableIF,
  CustomOptionsData,
} from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import useFileDownload from '@/hooks/useApi/fileDownLoad';
import { OdrRecListData, DevLogListData } from '@/types/HookUseApi/SjgTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import {
  tablePropsDataBomModList,
  tablePropsDataPrdModList,
  tablePropsDataSopModList,
  tablePropsDataDevModList,
  BUTTON_ID,
  ThValTypeMapping,
} from './sjgManufacturingInstructionRecordDetails';

/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey = 'fragmentDialogVisible' | 'messageBoxApiErrorVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
};

const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

// 親から渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  privilegesBtnRequestData: CommonRequestType;
  detailLogList: OdrRecListData | null;
};

const props = defineProps<Props>();

// 投入修正履歴テーブル設定
const tablePropsDataBomModListRef = ref<TabulatorTableIF>({
  ...tablePropsDataBomModList,
});

// 出来高修正履歴テーブル設定
const tablePropsDataPrdModListRef = ref<TabulatorTableIF>({
  ...tablePropsDataPrdModList,
});
// SOP修正履歴テーブル設定
const tablePropsDataSopModListRef = ref<TabulatorTableIF>({
  ...tablePropsDataSopModList,
});

// 異状履歴テーブル設定
const tablePropsDataDevModListRef = ref<TabulatorTableIF>({
  ...tablePropsDataDevModList,
});

// 1： 秤量記録書DL  2：製造記録書DL
const downloadPDF = async (flag: string) => {
  let sysBinNoTemp: string = '';
  if (props.detailLogList) {
    if (flag === BUTTON_ID.DL_WGT) {
      sysBinNoTemp = props.detailLogList.wgtPdfFile;
    } else {
      sysBinNoTemp = props.detailLogList.recPdfFile;
    }
  }
  await useFileDownload({
    commonRequestData: props.privilegesBtnRequestData,
    sysBinNo: sysBinNoTemp,
  });
};
// ダイアログ用ボタン設定
const dialogLeftButtons: DialogWindowProps['leftButtons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    // NOTE:クリック時は自身を閉じるのみ。これはcloseDialogで行うためclickHandlerを用意していない。
  },
];

const dialogRightButtons: DialogWindowProps['rightButtons'] = [
  {
    text: t('Sjg.Chr.txtReWeightDownload'),
    type: 'secondary',
    size: 'normal',
    clickHandler: () => {
      downloadPDF(BUTTON_ID.DL_WGT);
      return false;
    },
  },
  {
    text: t('Sjg.Chr.txtOrderDownload'),
    type: 'secondary',
    size: 'normal',
    clickHandler: () => {
      downloadPDF(BUTTON_ID.DL_PRD);
      return false;
    },
  },
];

/**
 * 作業指示コメント結合
 */
const joinCmtMain = (res: CustomOptionsData) => {
  const cmtMain = [res.cmtMain1, res.cmtMain2, res.cmtMain3].filter(Boolean);
  res.cmtMain = cmtMain.join('<br>');
};
// 初期処理で呼び出される

/**
 * 製造指図記録詳細ダイアログの初期設定
 */
const sjgInspectionResultsDetailsInit = async () => {
  if (!props.detailLogList) return;
  // 製造指図記録詳細情報レイアウト用初期値設定
  // 投入修正履歴
  tablePropsDataBomModListRef.value.tableData = props.detailLogList.bomModList;
  // SOP修正履歴
  // テーブル設定
  const tableData = props.detailLogList.sopModList;
  tableData.forEach((res) => {
    joinCmtMain(res);
  });
  tablePropsDataSopModListRef.value.tableData = tableData;
  // 異状履歴
  const newDevLogList = props.detailLogList.devLogList.map(
    (item: DevLogListData) => {
      const rectItem = { ...item };
      // 判定種別
      rectItem.thValType = ThValTypeMapping[rectItem.thValType];
      return rectItem;
    },
  );
  newDevLogList.forEach((res) => {
    joinCmtMain(res);
  });
  tablePropsDataDevModListRef.value.tableData = newDevLogList;
  // 出来高修正履歴
  // テーブル設定
  tablePropsDataPrdModListRef.value.tableData = props.detailLogList.prdModList;
  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

watch(() => props.isClicked, sjgInspectionResultsDetailsInit);
</script>

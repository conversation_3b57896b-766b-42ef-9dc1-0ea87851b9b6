<template>
  <!-- 製造指図記録確認ダイアログ -->
  <!-- 見出し 製造指図記録確認 -->
  <DialogWindow
    :title="dislogTitle"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogButtons"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!--  照査情報 -->
    <BaseHeading
      level="2"
      :text="$t('Sjg.Chr.txtInspectionInfo')"
      fontSize="24px"
      class="Util_mb-16"
    />
    <!-- 照査情報の見出し+テキスト項目表示 -->
    <div class="custom-form-container">
      <InfoShow
        :infoShowItems="processDataInfoShowRef.infoShowItems"
        :isLabelVertical="processDataInfoShowRef.isLabelVertical"
      />
    </div>

    <!-- 製造指図記録 -->
    <BaseHeading
      level="2"
      :text="$t('Sjg.Chr.txtSjgManufacturingInstructionRecord')"
      fontSize="24px"
      class="Util_mt-16"
    />
    <!--  製造指図記録 テーブル -->
    <TabulatorTable
      :propsData="tablePropsDataVerifyResultListRef"
      @clickBtnColumn="clickListItemDetail"
      @selectRows="updateSelectedRows"
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <MessageBox
    v-if="dialogVisibleRef.sjgInfoValidCheckError"
    :dialogProps="messageBoxSjgInfoValidCheckErrorRef"
    :submitCallback="() => closeSjgInfoValidCheckError()"
  />
  <!-- 照査結果詳細ダイアログ -->
  <SjgManufacturingInstructionRecordDetails
    :detailLogList="clickListItemObj"
    :isClicked="isClickedShowManufacturingInstructionRecordDetailsRef"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { ref, watch, nextTick } from 'vue';
import SCREENID from '@/constants/screenId';
import BaseHeading from '@/components/base/BaseHeading.vue';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import { closeLoading, showLoading } from '@/utils/dialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import SjgManufacturingInstructionRecordDetails from '@/components/fragment/sjg/SjgSelectInspectionItem/SjgManufacturingInstructionRecordDetails.vue';
import {
  useGetOrderRecordList,
  useModifyOrderRecordVerifyFinsh,
  useCheckValidVerify,
} from '@/hooks/useApi';
import {
  VerifyItemList,
  OdrRecListData,
  InfoShowItem,
  SelectInspectionItemData,
  OdrRecListDataWithUniqueKey,
} from '@/types/HookUseApi/SjgTypes';
import {
  CommonRequestType,
  ExtendCommonRequestType,
} from '@/types/HookUseApi/CommonTypes';
import {
  CommonListMapping,
  tablePropsDataVerifyResultList,
  getProcessDataInfoShowItems,
} from './sjgManufacturingInstructionRecordConfirmation';

/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxRequired'
  | 'sjgInfoValidCheckError';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxRequired: false,
  sjgInfoValidCheckError: false,
};
let newVerifyRsltList: InfoShowItem[] = [];
let clickListItemObj: OdrRecListDataWithUniqueKey | null = null;
const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);
const router = useRouter();

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 照査データ有効チェックエラーのメッセージ
const messageBoxSjgInfoValidCheckErrorRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});

const processDataInfoShowRef = ref<InfoShowType>({
  infoShowItems: getProcessDataInfoShowItems(),
  isLabelVertical: true,
});
const isClickedShowManufacturingInstructionRecordDetailsRef =
  ref<boolean>(false);
// 親から渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  privilegesBtnRequestData: CommonRequestType;
  selectedInspectionItemData: SelectInspectionItemData | null;
  selectedRowData: VerifyItemList | null;
  screenId: string;
  saveFlag: boolean;
};

const props = defineProps<Props>();
const emit = defineEmits(['submit']);

let dislogTitle: string = '';
const isConfirmation = () =>
  props.screenId ===
  SCREENID.SJG_MANUFACTURING_INSTRUCTION_RECORD_CONFIRMATION_CONFIRMATION;

// 照査結果一覧テーブル設定
const tablePropsDataVerifyResultListRef = ref<TabulatorTableIF>({
  ...tablePropsDataVerifyResultList,
});

const checkValidVerifyHandler = async () => {
  const apiRequestData: ExtendCommonRequestType<{ lotSid: string }> = {
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedInspectionItemData!.lotSid,
  };
  // 照査情報有効チェック
  const { errorRef } = await useCheckValidVerify(apiRequestData);
  if (errorRef.value) {
    closeLoading();
    messageBoxSjgInfoValidCheckErrorRef.value.title =
      errorRef.value.response.rTitle;
    messageBoxSjgInfoValidCheckErrorRef.value.content =
      errorRef.value.response.rMsg;
    openDialog('sjgInfoValidCheckError');
    return false;
  }
  return true;
};

/**
 * 保存ボタンの動作
 */
const clickConfirmBtn = async () => {
  // 全てにチェックがついていない場合はチェックNG
  if (
    tablePropsDataVerifyResultListRef.value.selectRowsData?.length !==
    tablePropsDataVerifyResultListRef.value.tableData.length
  ) {
    messageBoxApiErrorPropsRef.value.title = t(
      'Sjg.Msg.titleRecordConfirmationCheckBox',
    );
    messageBoxApiErrorPropsRef.value.content = t(
      'Sjg.Msg.contentRecordConfirmationCheckBox',
    );
    openDialog('messageBoxApiErrorVisible');
    return;
  }

  showLoading();
  if (!(await checkValidVerifyHandler())) {
    return;
  }
  // 製造指図記録確認完了
  const { responseRef, errorRef } = await useModifyOrderRecordVerifyFinsh({
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedInspectionItemData!.lotSid,
  });
  if (errorRef.value) {
    closeLoading();
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return;
  }
  if (responseRef.value) {
    closeDialog('fragmentDialogVisible');
    emit('submit');
  }
  closeLoading();
};

const buttonsList: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    disabled: false,
    clickHandler: () => {
      closeDialog('fragmentDialogVisible');
      if (
        props.screenId ===
        SCREENID.SJG_MANUFACTURING_INSTRUCTION_RECORD_CONFIRMATION
      )
        emit('submit', props.privilegesBtnRequestData);
    },
  },
  {
    text: t('Cm.Chr.btnTemporary'),
    type: 'primary',
    size: 'normal',
    clickHandler: () => {
      clickConfirmBtn();
      return false;
    },
  },
];
// NOTE:このダイアログはキャンセルのみ。デフォルト挙動を上書きするための定義。
// ダイアログ用ボタン設定
const dialogButtons = ref<DialogWindowProps['buttons']>(buttonsList);
// フッターの確認完了ボタンを非表示にする。
const setFootBtnDisable = () => {
  if (isConfirmation()) {
    dialogButtons.value = buttonsList.slice(0, 1);
  } else {
    // YES
    dialogButtons.value = buttonsList;
  }
};

const closeSjgInfoValidCheckError = async () => {
  closeDialog('sjgInfoValidCheckError');
  router.push({
    name: SCREENID.SJG_INSPECTION_CANDIDATE_LIST,
    state: {
      routerName: SCREENID.SJG_INSPECTION_CANDIDATE_LIST,
      searchConditionData: window.history.state.conditionData,
      tableSearchData: window.history.state.tableSearchData,
    },
  });
};

let skipRowSelectionChanged = false;
const clickListItemDetail = async (v: OdrRecListDataWithUniqueKey) => {
  clickListItemObj = v;
  // チェックボックス ON・OFFの設定
  if (props.saveFlag || !isConfirmation()) {
    const selectRowsDataMap = new Set(
      tablePropsDataVerifyResultListRef.value.selectRowsData,
    );
    if (!selectRowsDataMap.has(v.uniqueKey)) {
      tablePropsDataVerifyResultListRef.value.selectRowsData?.push(v.uniqueKey);
    }
    skipRowSelectionChanged = true;
    tablePropsDataVerifyResultListRef.value.tableData = JSON.parse(
      JSON.stringify(newVerifyRsltList),
    );
  }
  tablePropsDataVerifyResultListRef.value.tableData.forEach((item) => {
    if (item.uniqueKey === v?.uniqueKey) {
      clickListItemObj = v;
      //  製造指図記録確認(W181510)ダイアログを表示する。
      isClickedShowManufacturingInstructionRecordDetailsRef.value =
        !isClickedShowManufacturingInstructionRecordDetailsRef.value;
    }
  });
  await nextTick();
  skipRowSelectionChanged = false;
};
// 選択行情報の更新
const updateSelectedRows = (v: OdrRecListDataWithUniqueKey[]) => {
  // 選択複数行情報を保存
  if (props.saveFlag || !isConfirmation()) {
    if (skipRowSelectionChanged) return;
    const currentSelectedKeys = new Set(v.map((item) => item.uniqueKey));
    const selectRowsData =
      tablePropsDataVerifyResultListRef.value.selectRowsData ?? [];

    // 強調表示のデータを保持
    const preservedKeys = tablePropsDataVerifyResultListRef.value.tableData
      .filter((item) => {
        if (props.selectedRowData?.verifyCatSts === 'FN' || props.saveFlag) {
          return selectRowsData.includes(String(item.uniqueKey));
        }
        return (
          item.backgroundColor &&
          selectRowsData.includes(String(item.uniqueKey))
        );
      })
      .map((item) => item.uniqueKey);

    // `currentSelectedKeys` に含まれないデータを削除
    tablePropsDataVerifyResultListRef.value.selectRowsData =
      selectRowsData.filter(
        (key) => preservedKeys.includes(key) || currentSelectedKeys.has(key),
      );

    // 新しく選択された行を追加
    currentSelectedKeys.forEach((key) => {
      if (
        tablePropsDataVerifyResultListRef.value.selectRowsData &&
        !tablePropsDataVerifyResultListRef.value.selectRowsData.includes(key)
      ) {
        tablePropsDataVerifyResultListRef.value.selectRowsData.push(key);
      }
    });
  }
};
// 初期処理で呼び出される
// 製造指図記録一覧取得APIリクエストとレスポンス情報を格納
const requestApiGetVerifyResult = async () => {
  // 製造指図記録一覧取得のAPIを行う。
  const { responseRef, errorRef } = await useGetOrderRecordList({
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedInspectionItemData!.lotSid,
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return false; // エラーの場合継続処理させない
  }
  if (responseRef.value) {
    // ダイアログ表示初期値として、レスポンス情報を格納
    const initResponseData = responseRef.value.data.rData;

    // テーブル設定
    tablePropsDataVerifyResultListRef.value.tableData =
      initResponseData.odrRecList;
    newVerifyRsltList = [];
    newVerifyRsltList = initResponseData.odrRecList.map(
      (item: OdrRecListData, i) => {
        const rectItem: InfoShowItem = {
          ...item,
          bomModListShow: CommonListMapping.NOTHAVE,
          prdModListShow: CommonListMapping.NOTHAVE,
          sopModListShow: CommonListMapping.NOTHAVE,
          devListShow: CommonListMapping.NOTHAVE,
          isEnableBtn: 0,
          checkBoxDisabled: 1,
          backgroundColor: '',
          uniqueKey: `${item.odrNo}-${i}`,
        };
        // 一番目のデータを利用し、 異状レベルに応じて背景色指定チェック rectItem.devCorrLv
        if (rectItem.devLogList && rectItem.devLogList.length > 0) {
          // devCorrLvが一番高い（最大値）のデータのbackgroundColorを利用
          const maxDevCorrLvItem = rectItem.devLogList.reduce((max, curr) =>
            (curr.devCorrLv ?? 0) > (max.devCorrLv ?? 0) ? curr : max,
          );
          rectItem.backgroundColor = maxDevCorrLvItem.backgroundColor;
        } else if (!isConfirmation()) {
          rectItem.checkBoxDisabled = 0;
        }

        if (rectItem.bomModList && rectItem.bomModList.length > 0) {
          rectItem.bomModListShow = CommonListMapping.HAVE;
        }

        if (rectItem.prdModList && rectItem.prdModList.length > 0) {
          rectItem.prdModListShow = CommonListMapping.HAVE;
        }

        if (rectItem.sopModList && rectItem.sopModList.length > 0) {
          rectItem.sopModListShow = CommonListMapping.HAVE;
        }

        if (rectItem.devLogList && rectItem.devLogList.length > 0) {
          rectItem.devListShow = CommonListMapping.HAVE;
        }
        return rectItem;
      },
    );
    tablePropsDataVerifyResultListRef.value.tableData = newVerifyRsltList;
  }
  return true;
};

/**
 * 製造指図記録確認ダイアログの初期設定
 */
const sjgManufacturingInstructionRecordConfirmationInit = async () => {
  if (!props.selectedRowData) return;
  if (!props.selectedInspectionItemData) return;
  showLoading();
  processDataInfoShowRef.value.infoShowItems = getProcessDataInfoShowItems();
  if (
    props.selectedInspectionItemData &&
    props.selectedInspectionItemData.lotSid !== ''
  ) {
    Object.entries(props.selectedInspectionItemData).forEach(([key, value]) => {
      if (key in processDataInfoShowRef.value.infoShowItems) {
        processDataInfoShowRef.value.infoShowItems[key].infoShowModelValue =
          value?.toString() ?? '';
      }
    });
  }

  if (isConfirmation()) {
    dislogTitle = t(
      'Sjg.Chr.txtSjgManufacturingInstructionRecordConfirmationConfirmation',
    );
  } else {
    dislogTitle = t('Sjg.Chr.txtSjgManufacturingInstructionRecordConfirmation');
  }

  setFootBtnDisable();
  // 選択行情報初期化
  tablePropsDataVerifyResultListRef.value.tableData = [];
  tablePropsDataVerifyResultListRef.value.selectRowsData = [];
  newVerifyRsltList = [];
  // 製造指図記録一覧取得APIリクエストとレスポンス情報を格納
  const result = await requestApiGetVerifyResult();
  if (!result) {
    return;
  }

  if (
    isConfirmation() &&
    (props.selectedRowData?.verifyCatSts === 'FN' || props.saveFlag)
  ) {
    const uniqueKeyArray: string[] =
      tablePropsDataVerifyResultListRef.value.tableData
        .map((obj) => obj.uniqueKey)
        .filter(
          (key): key is string => typeof key === 'string' && key !== '',
        ) ?? [];

    tablePropsDataVerifyResultListRef.value.selectRowsData = uniqueKeyArray;
  }

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(() => props.isClicked, sjgManufacturingInstructionRecordConfirmationInit);
</script>
<style lang="scss" scoped>
.custom-form-container {
  height: 108px;
  overflow-y: auto;
}
</style>

<template>
  <!-- パレット積載情報修正ダイアログ -->
  <DialogWindow
    :title="$t('Aog.Chr.txtAogPltMod')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="() => checkEditForm()"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!-- 見出し パレット情報 -->
    <BaseHeading
      level="2"
      :text="$t('Aog.Chr.txtAogPlt')"
      fontSize="24px"
      class="table-title"
    />
    <!-- パレット情報 -->
    <div class="custom-form-container">
      <CustomForm
        class="Util_mt-16"
        :formModel="aogDetailInfoShowRef.formModel"
        :formItems="aogDetailInfoShowRef.formItems"
        @visible="
          (v: CustomFormType['customForm']) => {
            dialogInformationFormRef.customForm = v;
          }
        "
      />
    </div>
    <!-- 見出し 修正内容入力 -->
    <div class="Util_mt-32">
      <BaseHeading
        level="2"
        :text="$t('Aog.Chr.txtAogPltModInto')"
        fontSize="24px"
        class="table-title"
      />
    </div>
    <!-- 個装情報 -->
    <CustomForm
      class="Util_mt-16"
      :formModel="dialogInformationFormRef.formModel"
      :formItems="dialogInformationFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          dialogInformationFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
    <!-- 見出し パレット積載情報 -->
    <div class="Util_mt-32">
      <BaseHeading
        level="2"
        :text="$t('Aog.Chr.txtAogPltInfo')"
        fontSize="24px"
        class="table-title"
      />
    </div>
    <!-- 個装リストテーブル -->
    <TabulatorTable
      :propsData="tablePropsDataPrescriptionListRef"
      :routerName="props.routerName"
      @selectRow="updateSelectedRow"
    />
  </DialogWindow>

  <!-- 変更チェックエラー -->
  <MessageBox
    v-if="dialogVisibleRef.singleButton"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('singleButton')"
  />

  <!-- パレット満載量チェックエラー -->
  <MessageBox
    v-if="dialogVisibleRef.aogQtyError"
    :dialogProps="messageBoxAogQtyErrorRef"
    :submitCallback="() => closeDialog('aogQtyError')"
  />

  <!-- 入荷実績量チェック -->
  <MessageBox
    v-if="dialogVisibleRef.aogArrRecord"
    :dialogProps="messageBoxAogArrRecordRef"
    :cancelCallback="() => closeDialog('aogArrRecord')"
    :submitCallback="aogArrRecord"
  />

  <!-- パレット積載情報修正確認 -->
  <MessageBox
    v-if="dialogVisibleRef.aogPalletModCheck"
    :dialogProps="messageBoxAogPalletModCheckRef"
    :cancelCallback="() => closeDialog('aogPalletModCheck')"
    :submitCallback="apiHandler"
  />

  <!-- パレット積載情報修正失敗 -->
  <MessageBox
    v-if="dialogVisibleRef.aogPalletModFailure"
    :dialogProps="messageBoxaogPalletModFailureRef"
    :submitCallback="() => closeDialog('aogPalletModFailure')"
  />

  <!-- パレット積載情報修正完了 -->
  <MessageBox
    v-if="dialogVisibleRef.aogPalletModSuccess"
    :dialogProps="messageBoxaogPalletModSuccessRef"
    :submitCallback="aogPalletModSuccess"
  />

  <!-- 個装修正ダイアログ -->
  <AogEditLabel
    :selectedRowData="selectedRow!"
    :isClicked="isClickedShowAogEditLabelDialogRef"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
    @submit="reflectPrescriptionInfo"
  />
</template>
<script setup lang="ts">
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import onValidateHandler, {
  handleValidationBySingleRowSelect,
} from '@/utils/validateHandler';
import {
  // ComboBoxDataStandardReturnData,
  CommonRequestType,
} from '@/types/HookUseApi/CommonTypes';
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import {
  AogLabelIssuancePlanListData,
  AogPrintLblData,
  AogPrintLblListData,
} from '@/types/HookUseApi/AogTypes';
import {
  useSearchAogEditPaletteLoading,
  useModAogEditPaletteLoadingList,
  useGetComboBoxDataStandard,
} from '@/hooks/useApi';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import { closeLoading, showLoading } from '@/utils/dialog';
import { toBigOrNull } from '@/utils';
import createMessageBoxForm from '@/utils/commentMessageBox';
import {
  getDialogInformationFormItems,
  getDialogInformationInfoShowItems,
  dialogInformationFormModel,
  tablePropsData,
} from './AogEditPaletteLoading';

/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'singleButton'
  | 'fragmentDialogVisible'
  | 'aogQtyError'
  | 'aogArrRecord'
  | 'aogPalletModCheck'
  | 'aogPalletModSuccess'
  | 'aogPalletModFailure';
const initialState: InitialDialogState<DialogRefKey> = {
  singleButton: false,
  fragmentDialogVisible: false,
  aogQtyError: false,
  aogArrRecord: false,
  aogPalletModCheck: false,
  aogPalletModSuccess: false,
  aogPalletModFailure: false,
};
const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

let aogPaletteLoadingEditData: AogPrintLblData = {
  pltNo: '',
  aogInstNo: '',
  aogInstGrpNo: '',
  aogInstGrpPrtDts: '',
  aogYmd: '',
  poDtlNo: '',
  matNo: '',
  matNm: '',
  mBpId: '',
  bpNm: '',
  lotNo: '',
  rsltPckExpl: '',
  makerLotNo: '',
  makerLotNoModFlg: '',
  edNo: '',
  pltModExpl: '',
  pckExpl: '',
  unitNm: '',
  ioaQty: '',
  updDts: '',
  pltFullQty: '',
  arrQtyUpper: '',
  arrQtyLower: '',
  aogTotalQty: '',
  mesAogQty: '',
  aogPickSts: '',
  aogPalletLblList: [
    {
      baleUnit: '',
      unitNm: '',
      baleCnt: null,
      baleModExpl: '',
      baleNo: null,
      modFlg: null,
    },
  ],
};

const aogDetailInfoShowRef = ref<CustomFormType>({
  formItems: getDialogInformationInfoShowItems(),
  formModel: dialogInformationFormModel,
});

const messageBoxForm = createMessageBoxForm('message', 'cmtWarning');
// let comboBoxDataStandardReturnData: ComboBoxDataStandardReturnData | undefined;

// 入荷実績量警告コメント付きダイアログ
const messageBoxAogArrRecordRef = ref<DialogProps>({
  title: t('Aog.Msg.titleAogArrRecord'),
  content: '',
  isPrompt: true,
  formModel: messageBoxForm.formModel,
  formItems: messageBoxForm.formItems,
  type: 'warning',
});

// パレット満載量超過エラーダイアログ
const messageBoxAogQtyErrorRef = ref<DialogProps>({
  title: t('Aog.Msg.titleAogIoaQtyError'),
  content: t('Aog.Msg.contentAogIoaQtyErrorMessage'),
  isSingleBtn: true,
  type: 'error',
});

// 変更完了ダイアログ
const messageBoxaogPalletModSuccessRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

// 変更失敗ダイアログ
const messageBoxaogPalletModFailureRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'warning',
});

// 修正確認ダイアログ
const messageBoxAogPalletModCheckRef = ref<DialogProps>({
  title: t('Aog.Msg.titleAogPalletModCheck'),
  content: t('Aog.Msg.contentAogPalletModCheckMessage'),
  type: 'question',
});

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});

const dialogInformationFormRef = ref<CustomFormType>({
  formItems: getDialogInformationFormItems(),
  formModel: dialogInformationFormModel,
});

// 親から渡す情報群
type Props = {
  selectedRowData: AogLabelIssuancePlanListData | null;
  selectedRowsData: AogLabelIssuancePlanListData[];
  isClicked: boolean;
  privilegesBtnRequestData: CommonRequestType;
  routerName: string; // TabulatorTable権限 未選択チェック用
};

// 子に渡す情報パラメータ

const emit = defineEmits(['submit']);

// '積載数修正' クリック
const isClickedShowAogEditLabelDialogRef = ref<boolean>(false);

// 選択行データ
let selectedRow: AogPrintLblListData | null = null;

const updateSelectedRow = (v: AogPrintLblListData | null) => {
  selectedRow = v;
};

const props = defineProps<Props>();

// 個装一覧用テーブル設定
const tablePropsDataPrescriptionListRef = ref<TabulatorTableIF>({
  ...tablePropsData,
  onSelectBtns: [
    {
      // 積載数修正押下時
      text: 'Aog.Chr.btnModLabel',
      clickHandler: async () => {
        // 品目選択状態か判定
        isClickedShowAogEditLabelDialogRef.value =
          !isClickedShowAogEditLabelDialogRef.value;
      },
    },
  ],
});

// 個装修正ダイアログの 実行 押下時処理
// 個装修正の反映
const reflectPrescriptionInfo = (palletEditData: AogPrintLblListData) => {
  let bigAogTotalQty = toBigOrNull(aogPaletteLoadingEditData.aogTotalQty);
  let bigAogIoaQty = toBigOrNull(aogPaletteLoadingEditData.ioaQty);

  if (bigAogTotalQty !== null && bigAogIoaQty !== null) {
    bigAogTotalQty = bigAogTotalQty.minus(bigAogIoaQty);
  }

  bigAogIoaQty = toBigOrNull(0);

  // 表の中から、変更した情報を探して変更する
  for (
    let i = 0;
    i < tablePropsDataPrescriptionListRef.value.tableData.length;
    i++
  ) {
    if (
      aogPaletteLoadingEditData.aogPalletLblList[i].baleNo ===
      palletEditData.baleNo
    ) {
      aogPaletteLoadingEditData.aogPalletLblList[i].baleModExpl =
        palletEditData.baleModExpl;
      aogPaletteLoadingEditData.aogPalletLblList[i].baleUnit =
        palletEditData.baleUnit.toString();
      aogPaletteLoadingEditData.aogPalletLblList[i].modFlg =
        palletEditData.modFlg;
      aogPaletteLoadingEditData.aogPalletLblList[i].baleCnt =
        palletEditData.baleCnt !== null ? palletEditData.baleCnt : 0;
    }

    if (
      dialogInformationFormRef.value &&
      dialogInformationFormRef.value.formItems &&
      dialogInformationFormRef.value.formItems.ioaQty
    ) {
      const bigAogBaleUnit = toBigOrNull(
        aogPaletteLoadingEditData.aogPalletLblList[i].baleUnit,
      );
      const bigAogBaleCnt = toBigOrNull(
        aogPaletteLoadingEditData.aogPalletLblList[i].baleCnt,
      );
      if (
        bigAogBaleUnit !== null &&
        bigAogBaleCnt !== null &&
        bigAogIoaQty !== null
      ) {
        dialogInformationFormRef.value.formItems.ioaQty.formModelValue =
          bigAogIoaQty.plus(bigAogBaleUnit.times(bigAogBaleCnt)).toString();
      }
    }

    const AogDialogIoaQty = toBigOrNull(
      dialogInformationFormRef.value.formItems.ioaQty.formModelValue,
    );

    if (AogDialogIoaQty !== undefined) {
      bigAogIoaQty = AogDialogIoaQty;
    }

    tablePropsDataPrescriptionListRef.value.selectRowData =
      palletEditData.baleNo?.toString() ?? '';
    tablePropsDataPrescriptionListRef.value.tableData = [
      ...aogPaletteLoadingEditData.aogPalletLblList,
    ];
  }

  if (bigAogTotalQty !== null && bigAogIoaQty !== null) {
    bigAogTotalQty = bigAogTotalQty.plus(bigAogIoaQty);
    aogPaletteLoadingEditData.aogTotalQty = bigAogTotalQty
      .toNumber()
      .toString();
    aogPaletteLoadingEditData.ioaQty = bigAogIoaQty.toNumber().toString();
  }
};

// 自身の実行 押下時処理
const checkEditForm = async () => {
  // バリデート確認
  const validate =
    dialogInformationFormRef.value.customForm !== undefined &&
    (await dialogInformationFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));
  if (validate) {
    const bigAogPltFullQty = toBigOrNull(aogPaletteLoadingEditData.pltFullQty);
    const bigFormIoaQty = toBigOrNull(
      dialogInformationFormRef.value.formItems.ioaQty.formModelValue,
    );

    if (bigAogPltFullQty !== null && bigFormIoaQty !== null) {
      // パレット満載超過チェック
      if (bigAogPltFullQty.lt(bigFormIoaQty)) {
        // パレット満載超過エラーダイアログの表示
        openDialog('aogQtyError');
        return false;
      }
    }

    const bigArrQtyUpper = toBigOrNull(aogPaletteLoadingEditData.arrQtyUpper);
    const bigAogQtyLower = toBigOrNull(aogPaletteLoadingEditData.arrQtyLower);
    const bigAogTotalQty = toBigOrNull(aogPaletteLoadingEditData.aogTotalQty);
    // ステータスチェック
    if (aogPaletteLoadingEditData.aogPickSts === 'EN') {
      if (
        bigArrQtyUpper !== null &&
        bigAogTotalQty !== null &&
        bigAogQtyLower !== null
      ) {
        // 入荷実績量チェック
        if (
          bigArrQtyUpper.lt(bigAogTotalQty) ||
          bigAogTotalQty.lt(bigAogQtyLower)
        ) {
          // コメント付きダイアログのcontentに値を代入する
          messageBoxAogArrRecordRef.value.content = t(
            'Aog.Msg.checkAogRecordWaring',
            [
              aogPaletteLoadingEditData.mesAogQty,
              aogPaletteLoadingEditData.unitNm,
              aogPaletteLoadingEditData.aogTotalQty,
              aogPaletteLoadingEditData.unitNm,
            ],
          );
          if ('isPrompt' in messageBoxAogArrRecordRef.value) {
            messageBoxAogArrRecordRef.value.formItems.message.formModelValue =
              '';
          }
          // コメント付きエラーダイアログの表示
          openDialog('aogArrRecord');
          return false;
        }
      }
    }
    closeLoading();
    // パレット積載情報修正確認ダイアログの表示
    openDialog('aogPalletModCheck');
  }
  return false;
};

// 確認ダイアログのOK押下時
const apiHandler = async () => {
  if (!props.selectedRowData) return false;
  showLoading();
  if (
    dialogInformationFormRef.value.formItems.makerLotNo.formModelValue.toString() !==
    aogPaletteLoadingEditData.makerLotNo
  ) {
    aogPaletteLoadingEditData.makerLotNoModFlg = '1';
  }
  const aogEditPalletLoadingFormModel = {
    ...props.privilegesBtnRequestData,
    ...aogPaletteLoadingEditData,
    aogInstNo: props.selectedRowData.aogInstNo,
    seqNo: props.selectedRowData.seqNo,
    ioaQty:
      dialogInformationFormRef.value.formItems.ioaQty.formModelValue.toString(),
    makerLotNo:
      dialogInformationFormRef.value.formItems.makerLotNo.formModelValue.toString(),
    pltModExpl:
      dialogInformationFormRef.value.formItems.pltModExpl.formModelValue.toString(),
    msgboxTitleTxt: messageBoxAogPalletModCheckRef.value.title,
    msgboxMsgTxt: messageBoxAogPalletModCheckRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),

    aogPalletLblModList: aogPaletteLoadingEditData.aogPalletLblList.filter(
      (item) => item.modFlg === 1,
    ),
    warningExpl: '',
  };
  // パレット積載情報の修正
  const { responseRef, errorRef } = await useModAogEditPaletteLoadingList(
    aogEditPalletLoadingFormModel,
  );

  if (errorRef.value) {
    messageBoxaogPalletModFailureRef.value.title =
      errorRef.value.response.rTitle;
    messageBoxaogPalletModFailureRef.value.content =
      errorRef.value.response.rMsg;
    messageBoxaogPalletModFailureRef.value.type = 'error';
    closeLoading();
    closeDialog('aogPalletModCheck');
    openDialog('aogPalletModFailure');
    return false;
  }

  if (responseRef.value) {
    messageBoxaogPalletModSuccessRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxaogPalletModSuccessRef.value.content =
      responseRef.value.data.rMsg;
    messageBoxaogPalletModSuccessRef.value.type = 'info';
    closeLoading();
  }
  closeDialog('aogPalletModCheck');
  openDialog('aogPalletModSuccess');
  return true;
};

// 親に値を渡す
const aogPalletModSuccess = () => {
  emit('submit', props.privilegesBtnRequestData);
  closeDialog('aogPalletModSuccess');
  closeDialog('fragmentDialogVisible');
};

// 入荷実績量チェックダイアログの実行押下時処理
// 個装修正の反映
const aogArrRecord = () => {
  if ('isPrompt' in messageBoxAogArrRecordRef.value) {
    aogPaletteLoadingEditData.pckExpl =
      messageBoxAogArrRecordRef.value.formItems.message.formModelValue.toString();
  }
  closeDialog('aogArrRecord');
  openDialog('aogPalletModCheck');
};

/**
 * パレット積載情報修正ダイアログの初期設定
 */
const aogEditPaletteLoadingInit = async () => {
  // 単一選択されていなかった場合はエラー表示とする
  try {
    await handleValidationBySingleRowSelect(props.selectedRowsData);
  } catch (e) {
    return;
  }
  if (!props.selectedRowData) return;

  updateDialogChangeFlagRef(false);
  dialogInformationFormRef.value.formItems = getDialogInformationFormItems();
  showLoading();

  // パレット積載情報を取得
  const { responseRef, errorRef } = await useSearchAogEditPaletteLoading({
    ...props.privilegesBtnRequestData,
    aogInstNo: props.selectedRowData.aogInstNo,
    seqNo: props.selectedRowData.seqNo,
  });

  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return;
  }

  if (responseRef.value) {
    tablePropsDataPrescriptionListRef.value.selectRowData = '';
    Object.entries(responseRef.value.data.rData).forEach(([key, value]) => {
      if (key in aogDetailInfoShowRef.value.formItems) {
        aogDetailInfoShowRef.value.formItems[key].formModelValue =
          value?.toString() ?? '';
      }
    });
    aogPaletteLoadingEditData = {
      ...responseRef.value.data.rData,
      aogPalletLblList: responseRef.value.data.rData.aogPalletLblList.map(
        (lbl) => ({
          ...lbl,
        }),
      ),
    };
    Object.keys(aogPaletteLoadingEditData).forEach((key) => {
      if (responseRef.value && key === 'aogPalletLblList') {
        const resData = responseRef.value.data;
        tablePropsDataPrescriptionListRef.value.tableData =
          resData.rData.aogPalletLblList;

        // レスポンスに存在しないユニークキーを独自に設定して隠しカラムに仕込む対応
        tablePropsDataPrescriptionListRef.value.tableData.forEach((value) => {
          const tableData = value;
          tableData.modFlg = 0;
        });
      } else if (
        dialogInformationFormRef.value.formModel &&
        key in dialogInformationFormRef.value.formModel
      ) {
        if (dialogInformationFormRef.value?.formItems?.[key]) {
          dialogInformationFormRef.value.formItems[key].formModelValue =
            aogPaletteLoadingEditData[
              key as keyof AogPrintLblData
            ]?.toString() || ''; // nullの場合は空文字を設定
        }
      }
    });

    // 標準コンボ取得
    const comboBoxResData = await useGetComboBoxDataStandard({
      ...props.privilegesBtnRequestData,
      condList: [
        {
          cmbId: 'rsnCdAogMod',
          condKey: 'm_sys_cmt',
          where: { cmt_cat: 'AOG_PLT_MOD' },
        },
        {
          cmbId: 'cmtWarning',
          condKey: 'm_sys_cmt',
          where: { cmt_cat: 'AOG_RSLT_PCK' },
        },
      ],
    });
    if (comboBoxResData && comboBoxResData.rData.rList.length > 0) {
      setCustomFormComboBoxOptionList(
        dialogInformationFormRef.value.formItems,
        comboBoxResData.rData.rList,
      );
      if ('formItems' in messageBoxAogArrRecordRef.value)
        // コメントメッセージボックス選択肢
        setCustomFormComboBoxOptionList(
          messageBoxAogArrRecordRef.value.formItems,
          comboBoxResData.rData.rList,
        );
    }

    openDialog('fragmentDialogVisible');
    closeLoading();
  }
};

watch(
  () => props.isClicked,
  async () => {
    await aogEditPaletteLoadingInit();
  },
);
</script>
<style lang="scss" scoped>
$namespace: 'aog-label-issuance-plan-list';

.#{$namespace} {
  background-color: $white750;
  display: flex;
  flex-direction: column;
  flex-grow: 1;

  /** element plus */
  :deep(.el-card__body) {
    padding-top: 32px;
    padding-bottom: 0;
    padding-inline: 16px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
  }

  &_el-row {
    flex-grow: 1;
    flex-wrap: nowrap;

    &-show-condition {
      .#{$namespace}_search {
        max-width: 275px;
        opacity: 1;
        transition: max-width 0.28s ease;
        width: 100%;
      }

      .#{$namespace}_table {
        max-width: calc(100% - 290px);
        padding-left: 15px;
      }
    }
  }

  &_search {
    transition: max-width 0.28s ease-in-out;
    max-width: 0;
    opacity: 0;
    flex-shrink: 0;
  }
  &_table {
    max-width: 100%;
  }

  &_animated-col {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
  }

  .table-title {
    flex-shrink: 0;
  }

  .custom-form-container {
    height: 258px;
    overflow-y: auto;
  }
}
</style>

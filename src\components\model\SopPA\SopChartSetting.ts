import { ComposerTranslation } from 'vue-i18n';
import {
  Sop<PERSON>lowDataOption,
  Sop<PERSON>lowEdgeOption,
  SopControlPartOption,
  NeighborsOption,
  SopPartDataOption,
  SopNodeProperty,
  ImageOption,
  PartItemOption,
  PartItemChildOption,
  SopJoinDstOption,
  SelectBranchOption,
  NodeProperty,
  SelectOption,
  SopRectPartsOption,
  PartInstructionConfirmProps,
  PartSopTimerProps,
  PartButtonBranchProps,
  PartSystemBranchProps,
  PartExternalDeviceProps,
  PartWeightCalibrationProps,
  SopFlowGetDataOption,
  PartUpperLowerSetting,
} from '@/types/SopDialogInterface';
import { toNumberOrNull } from '@/utils';
import SOP_PARTS_VARIABLES from '@/constants/sopPartsVariables';
import SOP_PARTS_TYPE_VARIABLES from '@/constants/sopPartsTypeVariables';

export function getSOPSetting(t: ComposerTranslation) {
  const SOPset = {
    groups: [
      {
        name: 'group1',
        title: t('SOP.Menu.txtSOPParts'),
        graphHeight: 460,
        graphWidth: 300,
        layoutOptions: {
          rowHeight: 70,
        },
      },
      {
        name: 'group2',
        title: t('SOP.Menu.txtSOPBlock'),
        graphHeight: 90,
        graphWidth: 300,
        layoutOptions: {
          rowHeight: 70,
        },
      },
      {
        name: 'group3',
        title: t('SOP.Menu.txtSOPControlParts'),
        graphHeight: 250,
        graphWidth: 300,
        layoutOptions: {
          rowHeight: 70,
        },
      },
    ],
    rectParts: [
      {
        shape: 'part',
        sopPartsCD: SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_CD,
        imageName: [
          'partInstructionConfirm',
          'edit',
          'trash',
          'copy',
          'wCheck',
          'abnormalityLevel1',
          'abnormalityLevel2',
          'abnormalityLevel3',
          'abnormalityLevel4',
          'abnormalityLevel5',
          'write',
        ],
        attrs: {
          body: {
            rx: 6,
            ry: 6,
            stroke: '#a8b0c2',
            'stroke-width': 1,
            fill: '#E8E8E8',
          },
          text: {
            text: t('SOP.Menu.txtInstructionConfirm'),
          },
          title: {
            text: '',
          },
          nodeId: {
            text: 'ID:',
            refX: 55,
            refY: 18,
            fontSize: 12,
          },
          line: {
            width: 8,
            height: 55,
            refX: 3,
            refY: 10,
            rx: 7,
            ry: 5,
            fill: '#26D9B9',
            stroke: '#a8b0c2',
            strokeWidth: 1,
          },
          edit: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          trash: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          copy: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          wCheck: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel1: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel2: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel3: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel4: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel5: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          write: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
        },
      },
      {
        shape: 'part',
        sopPartsCD: SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD,
        imageName: [
          'partSopTimer',
          'edit',
          'trash',
          'copy',
          'wCheck',
          'abnormalityLevel1',
          'abnormalityLevel2',
          'abnormalityLevel3',
          'abnormalityLevel4',
          'abnormalityLevel5',
          'write',
        ],
        attrs: {
          body: {
            rx: 6,
            ry: 6,
            stroke: '#a8b0c2',
            'stroke-width': 1,
            fill: '#E8E8E8',
          },
          text: {
            text: t('SOP.Menu.txtSopTimer'),
          },
          title: {
            text: '',
          },
          nodeId: {
            text: 'ID:',
            refX: 55,
            refY: 18,
            fontSize: 12,
          },
          line: {
            width: 8,
            height: 55,
            refX: 3,
            refY: 10,
            rx: 7,
            ry: 5,
            fill: '#D9A426',
            stroke: '#a8b0c2',
            strokeWidth: 1,
          },
          edit: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          trash: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          copy: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          wCheck: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel1: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel2: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel3: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel4: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel5: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          write: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
        },
      },
      {
        shape: 'part',
        sopPartsCD: SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD,
        imageName: [
          'partNumericTextInput',
          'edit',
          'trash',
          'copy',
          'wCheck',
          'abnormalityLevel1',
          'abnormalityLevel2',
          'abnormalityLevel3',
          'abnormalityLevel4',
          'abnormalityLevel5',
          'write',
        ],
        attrs: {
          body: {
            rx: 6,
            ry: 6,
            stroke: '#a8b0c2',
            'stroke-width': 1,
            fill: '#E8E8E8',
          },
          image: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          text: {
            text: t('SOP.Menu.txtNumericTextInput'),
          },
          title: {
            text: '',
          },
          nodeId: {
            text: 'ID:',
            refX: 55,
            refY: 18,
            fontSize: 12,
          },
          line: {
            width: 8,
            height: 55,
            refX: 3,
            refY: 10,
            rx: 7,
            ry: 5,
            fill: '#D92626',
            stroke: '#a8b0c2',
            strokeWidth: 1,
          },
          edit: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          trash: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          copy: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          wCheck: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel1: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel2: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel3: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel4: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel5: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          write: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
        },
      },
      {
        shape: 'part',
        sopPartsCD: SOP_PARTS_VARIABLES.PART_DATE_RECORD_CD,
        imageName: [
          'partDateRecord',
          'edit',
          'trash',
          'copy',
          'wCheck',
          'abnormalityLevel1',
          'abnormalityLevel2',
          'abnormalityLevel3',
          'abnormalityLevel4',
          'abnormalityLevel5',
          'write',
        ],
        attrs: {
          body: {
            rx: 6,
            ry: 6,
            stroke: '#a8b0c2',
            'stroke-width': 1,
            fill: '#E8E8E8',
          },
          text: {
            text: t('SOP.Menu.txtDateRecord'),
          },
          title: {
            text: '',
          },
          nodeId: {
            text: 'ID:',
            refX: 55,
            refY: 18,
            fontSize: 12,
          },
          line: {
            width: 8,
            height: 55,
            refX: 3,
            refY: 10,
            rx: 7,
            ry: 5,
            fill: '#D92666',
            stroke: '#a8b0c2',
            strokeWidth: 1,
          },
          edit: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          trash: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          copy: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          wCheck: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel1: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel2: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel3: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel4: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel5: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          write: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
        },
      },
      {
        shape: 'part',
        sopPartsCD: SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD,
        imageName: [
          'partReceiveConsumption',
          'edit',
          'trash',
          'copy',
          'wCheck',
          'abnormalityLevel1',
          'abnormalityLevel2',
          'abnormalityLevel3',
          'abnormalityLevel4',
          'abnormalityLevel5',
          'write',
        ],
        attrs: {
          body: {
            rx: 6,
            ry: 6,
            stroke: '#a8b0c2',
            'stroke-width': 1,
            fill: '#E8E8E8',
          },
          text: {
            text: t('SOP.Menu.txtReceiveConsumption'),
          },
          title: {
            text: '',
          },
          nodeId: {
            text: 'ID:',
            refX: 55,
            refY: 18,
            fontSize: 12,
          },
          line: {
            width: 8,
            height: 55,
            refX: 3,
            refY: 10,
            rx: 7,
            ry: 5,
            fill: '#8FD926',
            stroke: '#a8b0c2',
            strokeWidth: 1,
          },
          edit: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          trash: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          copy: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          wCheck: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel1: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel2: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel3: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel4: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel5: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          write: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
        },
      },
      {
        shape: 'part',
        sopPartsCD: SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD,
        imageName: [
          'partResultConfirm',
          'edit',
          'trash',
          'copy',
          'wCheck',
          'abnormalityLevel1',
          'abnormalityLevel2',
          'abnormalityLevel3',
          'abnormalityLevel4',
          'abnormalityLevel5',
          'write',
        ],
        attrs: {
          body: {
            rx: 6,
            ry: 6,
            stroke: '#a8b0c2',
            'stroke-width': 1,
            fill: '#E8E8E8',
          },
          text: {
            text: t('SOP.Menu.txtResultConfirm'),
          },
          title: {
            text: '',
          },
          nodeId: {
            text: 'ID:',
            refX: 55,
            refY: 18,
            fontSize: 12,
          },
          line: {
            width: 8,
            height: 55,
            refX: 3,
            refY: 10,
            rx: 7,
            ry: 5,
            fill: '#8FD926',
            stroke: '#a8b0c2',
            strokeWidth: 1,
          },
          edit: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          trash: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          copy: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          wCheck: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel1: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel2: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel3: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel4: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel5: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          write: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
        },
      },
      {
        shape: 'part',
        sopPartsCD: SOP_PARTS_VARIABLES.PART_ELECTRONIC_FILE_CD,
        imageName: [
          'partElectronicFile',
          'edit',
          'trash',
          'copy',
          'wCheck',
          'abnormalityLevel1',
          'abnormalityLevel2',
          'abnormalityLevel3',
          'abnormalityLevel4',
          'abnormalityLevel5',
          'write',
        ],
        attrs: {
          body: {
            rx: 6,
            ry: 6,
            stroke: '#a8b0c2',
            'stroke-width': 1,
            fill: '#E8E8E8',
          },
          text: {
            text: t('SOP.Menu.txtElectronicFile'),
          },
          title: {
            text: '',
          },
          nodeId: {
            text: 'ID:',
            refX: 55,
            refY: 18,
            fontSize: 12,
          },
          line: {
            width: 8,
            height: 55,
            refX: 3,
            refY: 10,
            rx: 7,
            ry: 5,
            fill: '#267BD9',
            stroke: '#a8b0c2',
            strokeWidth: 1,
          },
          edit: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          trash: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          copy: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          wCheck: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel1: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel2: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel3: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel4: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel5: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          write: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
        },
      },
      {
        shape: 'part',
        sopPartsCD: SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD,
        imageName: [
          'testpartButtonBranch',
          'edit',
          'trash',
          'copy',
          'wCheck',
          'abnormalityLevel1',
          'abnormalityLevel2',
          'abnormalityLevel3',
          'abnormalityLevel4',
          'abnormalityLevel5',
          'write',
        ],
        attrs: {
          body: {
            rx: 6,
            ry: 6,
            stroke: '#a8b0c2',
            'stroke-width': 1,
            fill: '#E8E8E8',
          },
          text: {
            text: t('SOP.Menu.txtButtonBranch'),
          },
          title: {
            text: '',
          },
          nodeId: {
            text: 'ID:',
            refX: 55,
            refY: 18,
            fontSize: 12,
          },
          line: {
            width: 8,
            height: 55,
            refX: 3,
            refY: 10,
            rx: 7,
            ry: 5,
            fill: '#26D93B',
            stroke: '#a8b0c2',
            strokeWidth: 1,
          },
          edit: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          trash: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          copy: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          wCheck: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel1: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel2: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel3: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel4: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel5: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          write: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
        },
      },
      {
        shape: 'part',
        sopPartsCD: SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD,
        imageName: [
          'testpartSystemBranch',
          'edit',
          'trash',
          'copy',
          'wCheck',
          'abnormalityLevel1',
          'abnormalityLevel2',
          'abnormalityLevel3',
          'abnormalityLevel4',
          'abnormalityLevel5',
          'write',
        ],
        attrs: {
          body: {
            rx: 6,
            ry: 6,
            stroke: '#a8b0c2',
            'stroke-width': 1,
            fill: '#E8E8E8',
          },
          text: {
            text: t('SOP.Menu.txtSystemBranch'),
          },
          title: {
            text: '',
          },
          nodeId: {
            text: 'ID:',
            refX: 55,
            refY: 18,
            fontSize: 12,
          },
          line: {
            width: 8,
            height: 55,
            refX: 3,
            refY: 10,
            rx: 7,
            ry: 5,
            fill: '#26D93B',
            stroke: '#a8b0c2',
            strokeWidth: 1,
          },
          edit: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          trash: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          copy: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          wCheck: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel1: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel2: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel3: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel4: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel5: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          write: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
        },
      },
      {
        shape: 'part',
        sopPartsCD: SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD,
        imageName: [
          'testpartCommDevice',
          'edit',
          'trash',
          'copy',
          'wCheck',
          'abnormalityLevel1',
          'abnormalityLevel2',
          'abnormalityLevel3',
          'abnormalityLevel4',
          'abnormalityLevel5',
          'write',
        ],
        attrs: {
          body: {
            rx: 6,
            ry: 6,
            stroke: '#a8b0c2',
            'stroke-width': 1,
            fill: '#E8E8E8',
          },
          text: {
            text: t('SOP.Menu.txtExternalDevice'),
          },
          title: {
            text: '',
          },
          nodeId: {
            text: 'ID:',
            refX: 55,
            refY: 18,
            fontSize: 12,
          },
          line: {
            width: 8,
            height: 55,
            refX: 3,
            refY: 10,
            rx: 7,
            ry: 5,
            fill: '#D926A5',
            stroke: '#a8b0c2',
            strokeWidth: 1,
          },
          edit: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          trash: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          copy: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          wCheck: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel1: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel2: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel3: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel4: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel5: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          write: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
        },
      },
      {
        shape: 'part',
        sopPartsCD: SOP_PARTS_VARIABLES.PART_ELECTRONIC_SHELF_LABEL_CD,
        imageName: [
          'partElectronicShelfLabel',
          'edit',
          'trash',
          'copy',
          'wCheck',
          'abnormalityLevel1',
          'abnormalityLevel2',
          'abnormalityLevel3',
          'abnormalityLevel4',
          'abnormalityLevel5',
          'write',
        ],
        attrs: {
          body: {
            rx: 6,
            ry: 6,
            stroke: '#a8b0c2',
            'stroke-width': 1,
            fill: '#E8E8E8',
          },
          text: {
            text: t('SOP.Menu.txtElectronicShelfLabel'),
          },
          title: {
            text: '',
          },
          nodeId: {
            text: 'ID:',
            refX: 55,
            refY: 18,
            fontSize: 12,
          },
          line: {
            width: 8,
            height: 55,
            refX: 3,
            refY: 10,
            rx: 7,
            ry: 5,
            fill: '#9D26D9',
            stroke: '#a8b0c2',
            strokeWidth: 1,
          },
          edit: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          trash: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          copy: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          wCheck: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel1: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel2: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel3: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel4: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel5: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          write: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
        },
      },
      {
        shape: 'part',
        sopPartsCD: SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD,
        imageName: [
          'partInventoryConsumption',
          'edit',
          'trash',
          'copy',
          'wCheck',
          'abnormalityLevel1',
          'abnormalityLevel2',
          'abnormalityLevel3',
          'abnormalityLevel4',
          'abnormalityLevel5',
          'write',
        ],
        attrs: {
          body: {
            rx: 6,
            ry: 6,
            stroke: '#a8b0c2',
            'stroke-width': 1,
            fill: '#E8E8E8',
          },
          text: {
            text: t('SOP.Menu.txtInventoryConsumption'),
          },
          title: {
            text: '',
          },
          nodeId: {
            text: 'ID:',
            refX: 55,
            refY: 18,
            fontSize: 12,
          },
          line: {
            width: 8,
            height: 55,
            refX: 3,
            refY: 10,
            rx: 7,
            ry: 5,
            fill: '#CED926',
            stroke: '#a8b0c2',
            strokeWidth: 1,
          },
          edit: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          trash: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          copy: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          wCheck: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel1: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel2: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel3: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel4: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel5: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          write: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
        },
      },
      {
        shape: 'part',
        sopPartsCD: SOP_PARTS_VARIABLES.PART_EQUIPMENT_CONTAINER_CD,
        imageName: [
          'testpartUpdDevice',
          'edit',
          'trash',
          'copy',
          'wCheck',
          'abnormalityLevel1',
          'abnormalityLevel2',
          'abnormalityLevel3',
          'abnormalityLevel4',
          'abnormalityLevel5',
          'write',
        ],
        attrs: {
          body: {
            rx: 6,
            ry: 6,
            stroke: '#a8b0c2',
            'stroke-width': 1,
            fill: '#E8E8E8',
          },
          text: {
            text: t('SOP.Menu.txtEquipmentContainer'),
          },
          title: {
            text: '',
          },
          nodeId: {
            text: 'ID:',
            refX: 55,
            refY: 18,
            fontSize: 12,
          },
          line: {
            width: 8,
            height: 55,
            refX: 3,
            refY: 10,
            rx: 7,
            ry: 5,
            fill: '#CE26D9',
            stroke: '#a8b0c2',
            strokeWidth: 1,
          },
          edit: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          trash: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          copy: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          wCheck: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel1: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel2: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel3: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel4: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel5: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          write: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
        },
      },
      {
        shape: 'part',
        sopPartsCD: SOP_PARTS_VARIABLES.PART_LABEL_OUTPUT_CD,
        imageName: [
          'testpartLabelOutput',
          'edit',
          'trash',
          'copy',
          'wCheck',
          'abnormalityLevel1',
          'abnormalityLevel2',
          'abnormalityLevel3',
          'abnormalityLevel4',
          'abnormalityLevel5',
          'write',
        ],
        attrs: {
          body: {
            rx: 6,
            ry: 6,
            stroke: '#a8b0c2',
            'stroke-width': 1,
            fill: '#E8E8E8',
          },
          text: {
            text: t('SOP.Menu.txtLabelOutput'),
          },
          title: {
            text: '',
          },
          nodeId: {
            text: 'ID:',
            refX: 55,
            refY: 18,
            fontSize: 12,
          },
          line: {
            width: 8,
            height: 55,
            refX: 3,
            refY: 10,
            rx: 7,
            ry: 5,
            fill: '#26D97A',
            stroke: '#a8b0c2',
            strokeWidth: 1,
          },
          edit: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          trash: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          copy: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          wCheck: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel1: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel2: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel3: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel4: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel5: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          write: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
        },
      },
      {
        shape: 'part',
        sopPartsCD: SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_CD,
        imageName: [
          'testpartWeighingCalib',
          'edit',
          'trash',
          'copy',
          'wCheck',
          'abnormalityLevel1',
          'abnormalityLevel2',
          'abnormalityLevel3',
          'abnormalityLevel4',
          'abnormalityLevel5',
          'write',
        ],
        attrs: {
          body: {
            rx: 6,
            ry: 6,
            stroke: '#a8b0c2',
            'stroke-width': 1,
            fill: '#E8E8E8',
          },
          text: {
            text: t('SOP.Menu.txtWeightCalibration'),
          },
          title: {
            text: '',
          },
          nodeId: {
            text: 'ID:',
            refX: 55,
            refY: 18,
            fontSize: 12,
          },
          line: {
            width: 8,
            height: 55,
            refX: 3,
            refY: 10,
            rx: 7,
            ry: 5,
            fill: '#26B9D9',
            stroke: '#a8b0c2',
            strokeWidth: 1,
          },
          edit: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          trash: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          copy: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          wCheck: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel1: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel2: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel3: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel4: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel5: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          write: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
        },
      },
      {
        shape: 'part',
        sopPartsCD: SOP_PARTS_VARIABLES.PART_PALLET_CARGO_CD,
        imageName: [
          'testpartPalletCargo',
          'edit',
          'trash',
          'copy',
          'wCheck',
          'abnormalityLevel1',
          'abnormalityLevel2',
          'abnormalityLevel3',
          'abnormalityLevel4',
          'abnormalityLevel5',
          'write',
        ],
        attrs: {
          body: {
            rx: 6,
            ry: 6,
            stroke: '#a8b0c2',
            'stroke-width': 1,
            fill: '#E8E8E8',
          },
          text: {
            text: t('SOP.Menu.txtPalletCargo'),
          },
          title: {
            text: '',
          },
          nodeId: {
            text: 'ID:',
            refX: 55,
            refY: 18,
            fontSize: 12,
          },
          line: {
            width: 8,
            height: 55,
            refX: 3,
            refY: 10,
            rx: 7,
            ry: 5,
            fill: '#26B9D9',
            stroke: '#a8b0c2',
            strokeWidth: 1,
          },
          edit: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          trash: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          copy: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          wCheck: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel1: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel2: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel3: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel4: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel5: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          write: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
        },
      },
      {
        shape: 'part',
        sopPartsCD: SOP_PARTS_VARIABLES.PART_SPEC_COPY_CD,
        imageName: [
          'partCopyNode',
          'edit',
          'trash',
          'copy',
          'wCheck',
          'abnormalityLevel1',
          'abnormalityLevel2',
          'abnormalityLevel3',
          'abnormalityLevel4',
          'abnormalityLevel5',
          'write',
        ],
        attrs: {
          body: {
            rx: 6,
            ry: 6,
            stroke: '#a8b0c2',
            'stroke-width': 1,
            fill: '#E8E8E8',
          },
          image: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          text: {
            text: t('SOP.Menu.txtCopyNode'),
          },
          title: {
            text: '',
          },
          nodeId: {
            text: 'ID:',
            refX: 55,
            refY: 18,
            fontSize: 12,
          },
          line: {
            width: 8,
            height: 55,
            refX: 3,
            refY: 10,
            rx: 7,
            ry: 5,
            fill: '#9D26D9',
            stroke: '#a8b0c2',
            strokeWidth: 1,
          },
          edit: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          trash: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          copy: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          wCheck: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel1: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel2: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel3: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel4: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          abnormalityLevel5: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          write: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
        },
      },
    ],
    controlParts: [],
    blockParts: [
      {
        shape: 'templateNode',
        sopPartsCD: SOP_PARTS_VARIABLES.PART_SPEC_TEMPLATE_CD,
        imageName: 'sopTemplate',
        width: 128,
        height: 34,
        attrs: {
          body: {
            rx: 6,
            ry: 6,
            stroke: '#03AF7A',
            'stroke-width': 1,
            fill: '#FFFFFF',
          },
          image: {
            'xlink:href': '',
            width: 0,
            height: 0,
            x: 0,
            y: 0,
          },
          title: {
            text: '',
            refX: 55,
            refY: 40,
            fill: '#000000',
            fontSize: 16,
            fontWeight: 'bold',
            textAnchor: 'left',
          },
          line: {
            width: 8,
            height: 55,
            refX: 3,
            refY: 10,
            rx: 7,
            ry: 5,
            fill: '#26D9B9',
            stroke: '#a8b0c2',
            strokeWidth: 10,
          },
          line2: {
            width: 8,
            height: 55,
            refX: 50,
            refY: 10,
            rx: 50,
            ry: 5,
            fill: '#26D9B9',
            stroke: '#a8b0c2',
            strokeWidth: 10,
          },
        },
        label: t('SOP.Chr.txtSopTempSelect'),
      },
      {
        shape: 'blockNode',
        sopPartsCD: SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_CD,
        imageName: '',
        width: 130,
        height: 36,
        attrs: {
          body: {
            rx: 6,
            ry: 6,
            stroke: '#a8b0c2',
            'stroke-width': 1,
            fill: '#E8E8E8',
          },
          title: {
            text: '',
            refX: 55,
            refY: 40,
            fill: '#000000',
            fontSize: 16,
            fontWeight: 'bold',
            textAnchor: 'left',
          },
          line: {
            width: 8,
            height: 55,
            refX: 3,
            refY: 10,
            rx: 7,
            ry: 5,
            fill: '#26D9B9',
            stroke: '#a8b0c2',
            strokeWidth: 10,
          },
          line2: {
            width: 8,
            height: 55,
            refX: 50,
            refY: 10,
            rx: 50,
            ry: 5,
            fill: '#26D9B9',
            stroke: '#a8b0c2',
            strokeWidth: 10,
          },
        },
        label: t('SOP.Chr.txtSopBlock'),
      },
    ],
    customNode: [
      {
        name: 'part',
        data: {
          inherit: 'rect',
          width: 130,
          height: 36,
          markup: [
            {
              tagName: 'rect',
              selector: 'body',
            },
            {
              tagName: 'image',
              selector: 'image',
            },
            {
              tagName: 'text',
              selector: 'label',
            },
            {
              tagName: 'text',
              selector: 'title',
            },
            {
              tagName: 'text',
              selector: 'text',
            },
            {
              tagName: 'rect',
              selector: 'line',
            },
            {
              tagName: 'text',
              selector: 'nodeId',
            },
            {
              tagName: 'image',
              selector: 'wCheck',
            },
            {
              tagName: 'image',
              selector: 'abnormalityLevel1',
            },
            {
              tagName: 'image',
              selector: 'abnormalityLevel2',
            },
            {
              tagName: 'image',
              selector: 'abnormalityLevel3',
            },
            {
              tagName: 'image',
              selector: 'abnormalityLevel4',
            },
            {
              tagName: 'image',
              selector: 'abnormalityLevel5',
            },
            {
              tagName: 'image',
              selector: 'write',
            },
          ],
          attrs: {
            body: {
              strokeWidth: 1,
              stroke: '#5F95FF',
              fill: '#42c39b',
            },
            text: {
              refX: 75,
              refY: 18,
              fontSize: 0,
              fill: '#FFFFFF',
              text: '',
            },
          },
          ports: {},
        },
      },
      {
        name: 'addPart',
        data: {
          inherit: 'rect',
          width: 130,
          height: 20,
          markup: [
            {
              tagName: 'rect',
              selector: 'body',
            },
            {
              tagName: 'text',
              selector: 'label',
            },
            {
              tagName: 'g',
              selector: 'buttonNodeAdd',
              children: [
                {
                  tagName: 'rect',
                  selector: 'button',
                  attrs: {
                    'pointer-events': 'visiblePainted',
                  },
                },
                {
                  tagName: 'image',
                  selector: 'buttonAdd',
                  attrs: {
                    fill: 'none',
                    'pointer-events': 'none',
                  },
                },
              ],
            },
          ],
          attrs: {
            body: {
              strokeWidth: 1,
              stroke: '#5F95FF',
              fill: '#EFF4FF',
            },
            text: {
              fontSize: 12,
              fill: '#262626',
            },
            label: {
              refX: 25,
              refY: 4,
              textAnchor: 'center',
              textVerticalAnchor: 'top',
              fontSize: 12,
              fill: '#262626',
            },
          },
          ports: {},
        },
      },
      {
        name: 'control',
        data: {
          inherit: 'rect',
          width: 130,
          height: 36,
          attrs: {
            body: {
              strokeWidth: 1,
              stroke: '#5F95FF',
              fill: '#EFF4FF',
            },
            text: {
              fontSize: 12,
              fill: '#262626',
            },
          },
          ports: {},
        },
      },
      {
        name: 'start',
        data: {
          inherit: 'circle',
          width: 46,
          height: 46,
          attrs: {
            body: {
              strokeWidth: 1,
              stroke: '#5F95FF',
              fill: '#EFF4FF',
            },
            text: {
              fontSize: 12,
              fill: '#262626',
            },
          },
          ports: {},
        },
      },
      {
        name: 'end',
        data: {
          inherit: 'circle',
          width: 46,
          height: 46,
          attrs: {
            body: {
              strokeWidth: 1,
              stroke: '#5F95FF',
              fill: '#EFF4FF',
            },
            text: {
              fontSize: 12,
              fill: '#262626',
            },
          },
          ports: {},
        },
      },
      {
        name: SOP_PARTS_TYPE_VARIABLES.TYPE_JOIN,
        data: {
          inherit: 'polygon',
          width: 108,
          height: 46,
          attrs: {
            body: {
              strokeWidth: 1,
              stroke: '#5F95FF',
              fill: '#EFF4FF',
            },
            text: {
              fontSize: 12,
              fill: '#262626',
            },
          },
          ports: {},
        },
      },
      {
        name: 'blockNode',
        data: {
          inherit: 'rect',
          width: 130,
          height: 36,
          markup: [
            {
              tagName: 'rect',
              selector: 'body',
            },
            {
              tagName: 'image',
              selector: 'image',
            },
            {
              tagName: 'text',
              selector: 'label',
            },
            {
              tagName: 'text',
              selector: 'title',
            },
            {
              tagName: 'rect',
              selector: 'line',
            },
            {
              tagName: 'rect',
              selector: 'line2',
            },
            {
              tagName: 'g',
              selector: 'buttonBlock',
              children: [
                {
                  tagName: 'rect',
                  selector: 'button',
                  attrs: {
                    'pointer-events': 'visiblePainted',
                  },
                },
                {
                  tagName: 'image',
                  selector: 'buttonSign',
                  attrs: {
                    fill: 'none',
                    'pointer-events': 'none',
                  },
                },
              ],
            },
          ],
          attrs: {
            body: {
              stroke: '#FFFFFF',
              strokeWidth: 0,
              fill: 'transparent',
            },
            image: {
              width: 130,
              height: 36,
            },
            title: {
              text: '',
              refX: 55,
              refY: 40,
              fill: '#000000',
              fontSize: 16,
              fontWeight: 'bold',
              textAnchor: 'left',
            },
            label: {
              refX: 35,
              refY: 12,
              textAnchor: 'center',
              textVerticalAnchor: 'top',
              fontSize: 12,
              fill: '#FFFFFF',
            },
          },
          ports: {},
        },
      },
      {
        name: 'templateNode',
        data: {
          inherit: 'rect',
          width: 130,
          height: 33,
          markup: [
            {
              tagName: 'rect',
              selector: 'body',
            },
            {
              tagName: 'image',
            },
            {
              tagName: 'text',
              selector: 'label',
            },
          ],
          attrs: {
            body: {
              stroke: '#03AF7A',
              strokeWidth: 1,
              fill: '#FFFFFF',
            },
            image: {
              width: 20,
              height: 25,
              x: 5,
              y: 5,
            },
            label: {
              refX: 30,
              refY: 12,
              textAnchor: 'center',
              textVerticalAnchor: 'top',
              fontSize: 12,
              fill: '#262626',
            },
          },
          ports: {},
        },
      },
    ],
    ports: {
      groups: {
        top: {
          position: 'top',
          attrs: {
            circle: {
              r: 8,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
              style: {
                visibility: 'hidden',
              },
            },
          },
        },
        bottom: {
          position: 'bottom',
          attrs: {
            circle: {
              r: 8,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
              style: {
                visibility: 'hidden',
              },
            },
          },
        },
      },
      items: [
        {
          group: 'top',
        },
        {
          group: 'bottom',
        },
      ],
    },
    addPartPorts: {
      groups: {
        top: {
          position: 'top',
          attrs: {
            circle: {
              r: 2,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
              style: {
                visibility: 'hidden',
              },
            },
          },
        },
        bottom: {
          position: 'bottom',
          attrs: {
            circle: {
              r: 2,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
              style: {
                visibility: 'hidden',
              },
            },
          },
        },
      },
      items: [
        {
          group: 'top',
          args: {
            x: 66,
            y: 12,
          },
        },
        {
          group: 'bottom',
          args: {
            x: 66,
            y: 26,
          },
        },
      ],
    },
    partCds: [
      SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_CD,
      SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD,
      SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD,
      SOP_PARTS_VARIABLES.PART_DATE_RECORD_CD,
      SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD,
      SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD,
      SOP_PARTS_VARIABLES.PART_ELECTRONIC_FILE_CD,
      SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD,
      SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD,
      SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD,
      SOP_PARTS_VARIABLES.PART_ELECTRONIC_SHELF_LABEL_CD,
      SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD,
      SOP_PARTS_VARIABLES.PART_EQUIPMENT_CONTAINER_CD,
      SOP_PARTS_VARIABLES.PART_LABEL_OUTPUT_CD,
      SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_CD,
      SOP_PARTS_VARIABLES.PART_PALLET_CARGO_CD,
    ],
    controlPartCds: [],
    blockPartCds: [
      SOP_PARTS_VARIABLES.PART_SPEC_TEMPLATE_CD,
      SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_CD,
    ],
    conditionPartCds: [
      SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD,
      SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD,
      SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD,
      SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD,
    ],
    sopPartConst: {
      partWidth: 376,
      // partWidth: 426,
      partHeight: 75,
      blockWidth: 152,
      blockHeight: 36,
      addPartWidth: 24, // [課題1]保存したデータを開くと連結線は直ではない対応 他の[課題1]箇所の定義と一致する必須
      addPartHeight: 24, // [課題1]保存したデータを開くと連結線は直ではない対応 他の[課題1]箇所の定義と一致する必須
      startPartWidth: 46,
      startPartHeight: 46,
      marginLeft: 20,
      marginTop: 50,
      defaultWidth: 376,
      blockSpace: 79,
      tempConfluenceLinkYDistance: 41,
    },
    addPartCds: [
      SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD,
      SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD,
      SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD,
    ],
    blockTypes: [
      {
        label: SOP_PARTS_VARIABLES.PART_SPEC_TEMPLATE_NAME,
        value: '0',
      },
      {
        label: SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_NAME,
        value: '1',
      },
    ],
    operators: [
      {
        type: 'zoomIn',
        icon: 'zoomIn',
        name: t('SOP.Chr.txtSopZoomIn'),
      },
      {
        type: 'zoomOut',
        icon: 'ZoomOut',
        name: t('SOP.Chr.txtSopZoomOut'),
      },
      {
        type: 'actualSize',
        icon: 'Postcard',
        name: t('SOP.Chr.txtSopActualSize'),
      },
      {
        type: 'minimap',
        icon: 'Picture',
        name: t('SOP.Chr.txtSopPicture'),
      },
    ],
    commonSetting: {
      sopNodeNmJp: '',
      cmtMain1: '',
      cmtMain2: '',
      cmtMain3: '',
      cmtEm: '',
      cmtSub1: '',
      cmtSub2: '',
      cmtSub3: '',
      dcheckFlg: '0',
      dcheckPrivGrpCd: '',
      devCorrLv: '1',
      deviationInputFlg: '',
      deviationPrivGrpCd: '',
      scnShowFlg: '1',
      runLoopFlg: '1',
      workBreakFlg: '1',
      skippableFlg: '1',
      helpShowFlg: '0',
      confShowFlg: '1',
      recFillFlg: '1',
      recReType: 'L',
      recConfFlg: '0',
      helpFileType: 'N',
      helpBinPath1: '',
      helpBinPath2: '',
      helpBinPath3: '',
      helpBinPath4: '',
      helpBinPath5: '',
      helpTxt1: '',
      helpTxt2: '',
      helpTxt3: '',
    },
    individualPara: {
      PartInstructionConfirm: {
        confirmRecordOptions: '',
        conditionBranch: '',
        deviationBranchNodeId: '',
        deviationMessageText: `${t('SOP.Msg.errorDisplayMessage')}`,
      },
      PartSopTimer: {
        instructionTime: '',
        instructionTimeFixedValue: '',
        instructionTimeNodeId: '',
        startMethod: '',
        judgeValueShowFlg: '',
        conditionBranch: '',
        deviationBranchNodeId: '',
        deviationMessageText: `${t('SOP.Msg.errorDisplayMessage')}`,
      },
      PartNumericTextInput: {
        inputSelection: '',
        instructionValueSetting: '',
        instructionFixedValue: '',
        instructionNodeId: '',
        instructionGCode: '',
        inputMethod: '',
        inputMethodDecimalPlaces: 0,
        inputMethodFixedValue: '',
        inputMethodNodeId: '',
        inputMethodGCode: '',
        inputMethodFormula: '',
        inputMethodMatNo: '',
        inputMethodOrderItem: '',
        outputSetting: '',
        outputVolumeContainerSetting: '',
        outputVolumePaletteSetting: '',
        volumePaletteNodeId: '',
        volumeContainerNodeId: '',
        volumeZoneCd: '',
        outputSettingGCode: '',
        outputSettingWorkItem: '',
      },
      PartDateRecord: {
        recordMethod: '',
      },
      PartReceiveConsumption: {
        consumptionMatNo: '',
        consumptionSequence: '',
        instructionValueSetting: '',
        instructionNodeId: '',
        instructionGCode: '',
        destinationZone: '',
        destinationZoneCd: '',
        destinationNodeId: '',
        inventoryConsumption: '',
        consumptionReceiveCheckSetting: '',
        fifoZoneGroupCd: '',
      },
      PartResultConfirm: {
        preReceive: '',
        preReceiveNodeId: '',
        nonPreReceiveType: '',
        nonPreReceiveMatNo: '',
        nonPreReceiveNodeId: '',
        nonPreReceiveConsumptionSeq: '',
        fifoZoneGroupCd: '',
        instructionValueSetting: '',
        instructionNodeId: '',
        instructionGCode: '',
        destinationZone: '',
        destinationZoneCd: '',
        destinationNodeId: '',
        consumptionReceiveCheck: '',
        inventoryDrawdown: '',
        returnInventory: '',
        returnInventoryZoneCd: '',
        returnInventoryLabel: '',
        disposalAmount: '',
        disposalAmountSelect: '',
        disposalAmount1: '',
        disposalAmountReasonCode1: '',
        disposalAmount2: '',
        disposalAmountReasonCode2: '',
        disposalAmount3: '',
        disposalAmountReasonCode3: '',
        inputMethodDecimalPlaces: 0,
      },
      PartElectronicFile: {
        processingOriginalData: '',
        attachment: '',
        dataFileDefine: '',
        dataFileFolder: '',
        dataFileNodeId: '',
        dataFileGCode: '',
      },
      PartButtonBranch: {
        branchNumSetting: 2,
        branchMenu1: '',
        branchNodeId1: '',
        branchMenu2: '',
        branchNodeId2: '',
        branchMenu3: '',
        branchNodeId3: '',
        branchMenu4: '',
        branchNodeId4: '',
        branchMenu5: '',
        branchNodeId5: '',
        branchMenu6: '',
        branchNodeId6: '',
        branchMenu7: '',
        branchNodeId7: '',
        branchMenu8: '',
        branchNodeId8: '',
      },
      PartSystemBranch: {
        branchNumSetting: 2,
        branchNodeIdDefault: '',
        branchMessageDefault: '',
        branchNodeId1: '',
        branchMessage1: '',
        branchNodeId2: '',
        branchMessage2: '',
        branchNodeId3: '',
        branchMessage3: '',
        branchNodeId4: '',
        branchMessage4: '',
        branchNodeId5: '',
        branchMessage5: '',
        branchNodeId6: '',
        branchMessage6: '',
      },
      PartExternalDevice: {
        connectedDeviceSetting: '',
        connectedDeviceReadValue: '',
        connectedDeviceWriteValue: '',
        connectedDeviceSettingDataNum: 1,
        connectedDeviceTimeOut: '',
        upperLowerLimitCheck: '',
        conditionBranch: '',
        deviationBranchNodeId: '',
        deviationMessageText: `${t('SOP.Msg.errorDisplayMessage')}`,
        numericTextInput: [
          {
            itemText: '',
            instructionValueSetting: '',
            instructionValue: '',
            sourceItemNode: '',
            sourceItem: '',
            sourceItemTag: '',
            outputSetting: '',
            outputValue: '',
            judgeType: '',
            numericSOPListRangeValueType: '',
            deviationLowerLimit: '',
            deviationUpperLimit: '',
            instructionValueType: '',
            sourceItemId: '',
          },
        ],
      },
      PartElectronicShelfLabel: {
        shelfGateWayIP: '',
        shelfIdSetting: '',
        shelfIdFixedValue: '',
        shelfIdNodeId: '',
        shelfIdGCode: '',
        shelfJP: '',
        sendTextSetting1: '',
        sendTextFixedValue1: '',
        sendTextNodeId1: '',
        sendTextSetting2: '',
        sendTextFixedValue2: '',
        sendTextNodeId2: '',
        sendTextSetting3: '',
        sendTextFixedValue3: '',
        sendTextNodeId3: '',
        sendTextSetting4: '',
        sendTextFixedValue4: '',
        sendTextNodeId4: '',
        sendTextSetting5: '',
        sendTextFixedValue5: '',
        sendTextNodeId5: '',
        sendTextSetting6: '',
        sendTextFixedValue6: '',
        sendTextNodeId6: '',
        sendTextSetting7: '',
        sendTextFixedValue7: '',
        sendTextNodeId7: '',
        sendTextSetting8: '',
        sendTextFixedValue8: '',
        sendTextNodeId8: '',
        sendTextSetting9: '',
        sendTextFixedValue9: '',
        sendTextNodeId9: '',
        sendTextSetting10: '',
        sendTextFixedValue10: '',
        sendTextNodeId10: '',
      },
      PartInventoryConsumption: {
        consumptionType: '',
        consumptionTypeMatNo: '',
        consumptionTypeNodeId: '',
        consumptionTypeItemClass: '',
        zoneType: '',
        zoneTypeZoneCd: '',
        inputMethodDecimalPlaces: 0,
        reasonCode: '',
        instructionValueSetting: '',
        instructionFixedValue: '',
        instructionNodeId: '',
        instructionGCode: '',
        fifoCheckSetting: '',
      },
      PartEquipmentContainer: {
        deviceContainerType: '',
        deviceAndVesselSetting: '',
        deviceContainerNo: '',
        deviceContainerNodeId: '',
        instructionSelection: '',
      },
      PartLabelOutput: {
        labelType: '',
        labelContainerSetting: '',
        labelContainerNodeId: '',
        labelPaletteSetting: '',
        labelPaletteNodeId: '',
        outputMethod: '',
        numberOfCopies: '',
        labelPrinterIP: '',
        labelPrinterMasterSelect: '',
        labelPrinterIPNodeId: '',
        isOutputPending: '',
        timeoutSeconds: '',
        labelItemSetting1: '',
        labelItemFixedValue1: '',
        labelItemNodeId1: '',
        labelItemSetting2: '',
        labelItemFixedValue2: '',
        labelItemNodeId2: '',
        labelItemSetting3: '',
        labelItemFixedValue3: '',
        labelItemNodeId3: '',
        labelItemSetting4: '',
        labelItemFixedValue4: '',
        labelItemNodeId4: '',
        labelItemSetting5: '',
        labelItemFixedValue5: '',
        labelItemNodeId5: '',
        labelItemSetting6: '',
        labelItemFixedValue6: '',
        labelItemNodeId6: '',
        labelItemSetting7: '',
        labelItemFixedValue7: '',
        labelItemNodeId7: '',
        labelItemSetting8: '',
        labelItemFixedValue8: '',
        labelItemNodeId8: '',
        labelItemSetting9: '',
        labelItemFixedValue9: '',
        labelItemNodeId9: '',
        labelItemSetting10: '',
        labelItemFixedValue10: '',
        labelItemNodeId10: '',
      },
      PartWeightCalibration: {
        deviceSetting: '',
        calibSetting: '',
        deviationMessageText: `${t('SOP.Msg.errorDisplayMessage')}`,
        wgtLogDIFlg: '',
        conditionBranch: '',
        deviationBranchNodeId: '',
      },
      PartPalletCargo: {
        palletMode: '',
        palletIdSetting: '',
        palletIdFixedValue: '',
        palletIdNodeId: '',
        palletIdGCode: '',
        mixedCargo: '',
      },
    },
    conditionProps: {
      judgeValueShowFlg: '',
      deviationMessageText: `${t('SOP.Msg.errorDisplayMessage')}`,
      conditionBranch: '',
      deviationBranchNodeId: '',
    },
    sopCondition: {
      condition1: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition2: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition3: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition4: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition5: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition6: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
    },
    upperLowerSetting: {
      thJudgeFlg: '',
      thRangeType: '',
      thValType: '',
      thValLlmt: '',
      thValUlmt: '',
    },
    startParts: [
      {
        shape: 'start',
        sopPartsCD: SOP_PARTS_VARIABLES.PART_SPEC_START_CD,
        imageName: 'start',
        width: 46,
        height: 46,
        attrs: {
          body: {
            rx: 6,
            ry: 6,
            stroke: '#9bb3e2',
            strokeWidth: 2,
            fill: '#f1f3fe',
          },
          button: {
            width: 24,
            height: 24,
            refX: 1,
            refY: 1,
            rx: 2,
            ry: 2,
            fill: 'transparent',
            stroke: '#000000',
            strokeWidth: 0,
            cursor: '',
            event: '',
          },
        },
        label: t('SOP.Menu.txtBegin'),
      },
      {
        shape: 'addPart',
        sopPartsCD: SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD,
        imageName: 'add',
        width: 24, // [課題1]保存したデータを開くと連結線は直ではない対応 他の[課題1]箇所の定義と一致する必須
        height: 24, // [課題1]保存したデータを開くと連結線は直ではない対応 他の[課題1]箇所の定義と一致する必須
        attrs: {
          body: {
            stroke: '#FFFFFF',
            strokeWidth: 0,
            fill: 'transparent',
          },
          button: {
            width: 24,
            height: 24,
            refX: 1,
            refY: 1,
            rx: 2,
            ry: 2,
            fill: 'transparent',
            stroke: '#000000',
            strokeWidth: 0,
            cursor: '',
            event: '',
          },
        },
      },
      {
        shape: 'end',
        sopPartsCD: SOP_PARTS_VARIABLES.PART_SPEC_END_CD,
        width: 46,
        height: 46,
        attrs: {
          body: {
            rx: 6,
            ry: 6,
            stroke: '#9bb3e2',
            strokeWidth: 2,
            fill: '#f1f3fe',
          },
          button: {
            width: 24,
            height: 24,
            refX: 1,
            refY: 1,
            rx: 2,
            ry: 2,
            fill: 'transparent',
            stroke: '#000000',
            strokeWidth: 0,
            cursor: '',
            event: '',
          },
        },
        label: t('SOP.Menu.txtEnd'),
      },
    ],
  };
  return SOPset;
}
export function hasKey<T extends object, K extends PropertyKey>(
  item: T,
  key: K,
): item is T & Record<K, string> {
  return key in item;
}
export const hasValue = (values: SelectOption[], value: string) => {
  let hasVal = false;
  if (values.length > 0) {
    for (let i = 0; i < values.length; i++) {
      if (values[i].value === value) {
        hasVal = true;
        break;
      }
    }
  }
  return hasVal;
};
export const gCodeFormatSetting = () =>
  /^(?:[FCHLBPTZ]{1,3})-(?:[\u0020-\u007E\u3000-\uFFEF]){1,8}$/u;

export const requiredTag = (t: ComposerTranslation) => {
  const tag = document.createElement('span');
  tag.textContent = t('Cm.Chr.txtTagOfRequired');
  tag.className = `Util_ml-8 custom-form_tag el-tag el-tag--danger el-tag--dark is-round`;
  tag.style.marginLeft = `8px`;
  return tag;
};

/**
 * childCells の取得
 * @param {*} itemCell - flow Chart Data item
 */
export function getSOPFlowRowList(itemCell: SopFlowDataOption) {
  const childCells = [];
  if (itemCell.sopJoin.nextNodeNo !== '') {
    const nextNode: SopJoinDstOption = JSON.parse(itemCell.sopJoin.nextNodeNo);
    if (nextNode.condBrDst1 !== '') {
      childCells.push(nextNode.condBrDst1);
    }
    if (nextNode.condBrDst2 !== '') {
      childCells.push(nextNode.condBrDst2);
    }
    if (nextNode.condBrDst3 !== '') {
      childCells.push(nextNode.condBrDst3);
    }
    if (nextNode.condBrDst4 !== '') {
      childCells.push(nextNode.condBrDst4);
    }
    if (nextNode.condBrDst5 !== '') {
      childCells.push(nextNode.condBrDst5);
    }
    if (nextNode.condBrDst6 !== '') {
      childCells.push(nextNode.condBrDst6);
    }
    if (nextNode.condBrDst7 !== '') {
      childCells.push(nextNode.condBrDst7);
    }
    if (nextNode.condBrDst8 !== '') {
      childCells.push(nextNode.condBrDst8);
    }
  }
  return childCells;
}
/**
 * flow Chart Data の書式設定
 * @param {*} startCell - flow Chart start Cell Data
 * @param {*} otherCell - flow Chart other Cell Data
 */
export function formatSOPFlowEdges(
  startCell: SopFlowDataOption[],
  otherCell: SopFlowDataOption[],
  endCell: SopFlowDataOption[],
) {
  const cells: SopFlowDataOption[] = [...startCell, ...otherCell, ...endCell];
  const sopEdges: SopFlowEdgeOption[] = [];
  cells.forEach((item) => {
    const childs = getSOPFlowRowList(item);
    childs.forEach((cItem) => {
      const edgeObj = {
        fromId: item.sopNodeNo,
        toId: cItem,
      };
      sopEdges.push(edgeObj);
    });
  });
  return {
    edges: sopEdges,
  };
}
/**
 * Childs の取得
 * @param {*} itemId - node ID
 * @param {*} flowData - flow Chart Data
 */
export function getItemChildCells(itemId: string, flowData: PartItemOption[]) {
  let allChilds: string[] = [];
  flowData.forEach((item) => {
    if (item.itemId === itemId) {
      allChilds = item.itemChilds;
    }
  });
  return allChilds;
}
export function deDuplicateFlowRowData(rowChilds: string[], keepItem: string) {
  const nonKeepers: string[] = [];
  const result: string[] = [];
  for (let count = 0; count < rowChilds.length; count++) {
    const item = rowChilds[count];
    if (item === keepItem) {
      result.push(item);
    } else {
      const includeFlag = nonKeepers.includes(item);
      if (!includeFlag) {
        nonKeepers.push(item);
        result.push(item);
      } else {
        result.push('');
      }
    }
  }
  return result;
}
export function formatSopChartData(
  flowData: PartItemOption[],
  nodeIds: string[],
) {
  const flowRowOption: PartItemChildOption = {};
  let index = 1;
  let rowLength = 1;
  const firstRow = [];
  firstRow.push(flowData[0].itemId);
  flowRowOption.row0 = firstRow;
  flowRowOption.row1 = flowData[0].itemChilds;
  let childCells = flowData[0].itemChilds;
  for (let count = 0; count < nodeIds.length; count++) {
    let rowChilds: string[] = [];
    if (childCells && childCells.length > 0) {
      for (let childCount = 0; childCount < childCells.length; childCount++) {
        let flowItem: string[] = [''];
        if (childCells[childCount] !== '') {
          const itemChilds = getItemChildCells(
            childCells[childCount],
            flowData,
          );
          if (itemChilds.length > 0) {
            flowItem = itemChilds;
          }
        }
        rowChilds = rowChilds.concat(flowItem);
        const cellItem = childCells[childCount];
        nodeIds.forEach((nodeItem, nodeIndex) => {
          if (nodeItem === cellItem) {
            nodeIds.splice(nodeIndex, 1);
          }
        });
      }
      const resultChilds: string[] = deDuplicateFlowRowData(rowChilds, '');
      const reduceCount = resultChilds.reduce(
        (num, value) => (value === '' ? num + 1 : num),
        0,
      );
      if (resultChilds.length > 0 && reduceCount !== resultChilds.length) {
        index++;
        for (let rowCount = 0; rowCount < index; rowCount++) {
          const rowName = `row${rowCount}`;
          const levelUp = flowRowOption[rowName];
          resultChilds.forEach((resultItem, resultIndex) => {
            if (resultItem !== '') {
              const resultFlag = levelUp.includes(resultItem);
              if (resultFlag) {
                resultChilds.splice(resultIndex, 1);
              }
            }
          });
        }
        const everyFlag = resultChilds.every((element) => element === '');
        if (!everyFlag) {
          childCells = resultChilds;
          if (resultChilds.length >= rowLength) {
            rowLength = resultChilds.length;
          }
          const rowIndex = `row${index}`;
          flowRowOption[rowIndex] = resultChilds;
        }
      }
    }
    count--;
  }

  return {
    flowRowData: flowRowOption,
    flowRowNum: rowLength,
  };
}
/**
 * part node code Check
 * @param {*} partCds - node Code
 * @param {*} sopPartsCD - SOP Setting Data
 */
export function checkPartCode(partCds: string[], sopPartsCD: string) {
  const sameCd = partCds.filter((tem) => tem === sopPartsCD);
  if (sameCd.length > 0) {
    return true;
  }
  return false;
}
/**
 * part node code Check
 * @param {*} sopPartsCD - node Code
 * @param {*} SOPSetData - SOP Setting Data
 */
export function checkPartNodeCode(
  SOPSetData: SopNodeProperty,
  sopPartsCD: string,
) {
  const { partCds } = SOPSetData;
  const sameCd = partCds.filter((item) => item === sopPartsCD);
  return sameCd.length > 0;
}
/**
 * パーツ種別の取得
 * @param {*} sopPartsCD - parts code
 * @param {*} SOPSetData - SOP Setting Data
 */
export function getPartsType(SOPSetData: SopNodeProperty, sopPartsCD: string) {
  let partsTypeVal = '';
  const sameCdFlag = checkPartCode(SOPSetData.partCds, sopPartsCD);
  if (sameCdFlag) {
    partsTypeVal = SOP_PARTS_TYPE_VARIABLES.TYPE_PART;
  } else if (sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_START_CD) {
    partsTypeVal = SOP_PARTS_TYPE_VARIABLES.TYPE_START;
  } else if (sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_END_CD) {
    partsTypeVal = SOP_PARTS_TYPE_VARIABLES.TYPE_END;
  } else if (
    sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD ||
    sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD ||
    sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD
  ) {
    partsTypeVal = SOP_PARTS_TYPE_VARIABLES.TYPE_JOIN;
  } else {
    partsTypeVal = SOP_PARTS_TYPE_VARIABLES.TYPE_BLOCK;
  }
  return {
    partsType: partsTypeVal,
  };
}
/**
 * Control part node code Check
 * @param {*} sopPartsCD - node Code
 * @param {*} SOPSetData - SOP Setting Data
 */
export function checkControlPartCode(
  SOPSetData: SopNodeProperty,
  sopPartsCD: string,
) {
  const partCds = SOPSetData.controlPartCds;
  const sameCd = partCds.filter((tem) => tem === sopPartsCD);
  if (sameCd.length > 0) {
    return true;
  }
  return false;
}
/**
 * 時間形式から数値に変換
 * @param {*} valLimit - 時間
 */
export function convertToSeconds(valLimit: string) {
  // if (valLimit === '') {
  //   return NO_SETTING_LIMIT_VALUE;
  // }
  const [minutes, seconds] = valLimit.split(':');
  const intMinutes = parseInt(minutes, 10);
  const intSeconds = parseInt(seconds, 10);

  return intMinutes * 60 + intSeconds;
}
/**
 * 数値から時間形式に変換
 * @param {*} valLimit - 時間
 */
export function convertToTimeString(valLimit: string) {
  if (valLimit === '') {
    return '';
  }
  const intValLimit = parseInt(valLimit, 10);
  const intMinutes = Math.floor(intValLimit / 60);
  const intSeconds = intValLimit % 60;
  const formatMinutes = intMinutes.toString().padStart(2, '0');
  const formatSeconds = intSeconds.toString().padStart(2, '0');

  return `${formatMinutes}:${formatSeconds}`;
}
/**
 * SOPパーツロード json 取得
 * @param {*} part - ブロック
 * @param {*} SOPSetData - SOP Setting Data
 * @param {*} imageMap - image Map
 */
export function setPartNodeJson(
  part: SopFlowDataOption,
  SOPSetData: SopNodeProperty,
  imageMap: Map<string, string>,
) {
  const { rectParts } = SOPSetData;
  const { commonSetting, sopCondition, upperLowerSetting } = part;
  let individualPara = { IndividualSetting: '', ConditionSetting: '' };
  // [課題2] NULLガード
  if (part.individualPara && part.individualPara !== '') {
    individualPara = JSON.parse(part.individualPara);
  }
  let rectIndex = -1;
  let imageName!: string[];
  rectParts.forEach((item, index: number) => {
    if (item.sopPartsCD === part.sopPartsCd) {
      rectIndex = index;
      imageName = item.imageName;
    }
  });
  const rectItem = {
    ...rectParts[rectIndex],
    attrs: {
      image: {
        'xlink:href': imageMap.get(imageName[0]),
        width: 25,
        height: 25,
        x: 8,
        y: 20,
      },
      title: {
        text: commonSetting.sopNodeNmJp,
        refX: 55,
        refY: 15,
        fill: '#000000',
        fontSize: 15,
        fontWeight: 'bold',
        textAnchor: 'left',
      },
      wCheck: {
        'xlink:href': imageMap.get(imageName[4]),
      },
      abnormalityLevel1: {
        'xlink:href': imageMap.get(imageName[5]),
      },
      abnormalityLevel2: {
        'xlink:href': imageMap.get(imageName[6]),
      },
      abnormalityLevel3: {
        'xlink:href': imageMap.get(imageName[7]),
      },
      write: {
        'xlink:href': imageMap.get(imageName[10]),
      },
    },
    width: SOPSetData.sopPartConst?.partWidth,
    height: SOPSetData.sopPartConst?.partHeight,
    zIndex: 0,
    id: part.nodeId,
    commonSetting,
    individualPara: individualPara.IndividualSetting,
    conditionProps: individualPara.ConditionSetting,
    sopCondition,
    upperLowerSetting: {
      thJudgeFlg: upperLowerSetting.thJudgeFlg,
      thRangeType: upperLowerSetting.thRangeType,
      thValType: upperLowerSetting.thValType,
      thValLlmt:
        upperLowerSetting.thValLlmt === null
          ? ''
          : String(upperLowerSetting.thValLlmt),
      thValUlmt:
        upperLowerSetting.thValUlmt === null
          ? ''
          : String(upperLowerSetting.thValUlmt),
    },
    instUnitTxt: part.instUnitTxt,
    markup: [
      {
        tagName: 'rect',
        selector: 'body',
      },
      {
        tagName: 'image',
        selector: 'image',
      },
      {
        tagName: 'image',
        selector: 'wCheck',
      },
      {
        tagName: 'image',
        selector: 'abnormalityLevel1',
      },
      {
        tagName: 'image',
        selector: 'abnormalityLevel2',
      },
      {
        tagName: 'image',
        selector: 'abnormalityLevel3',
      },
      {
        tagName: 'image',
        selector: 'write',
      },
      {
        tagName: 'text',
        selector: 'label',
      },
      {
        tagName: 'text',
        selector: 'title',
      },
      {
        tagName: 'text',
        selector: 'text',
      },
      {
        tagName: 'text',
        selector: 'nodeId',
      },
      {
        tagName: 'rect',
        selector: 'line',
      },
    ],
    dispNodeId: String(part.dspSeq).padStart(4, '0'),
  };
  if (part.sopPartsCd === SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD) {
    rectItem.upperLowerSetting.thValLlmt = convertToTimeString(
      rectItem.upperLowerSetting.thValLlmt,
    );
    rectItem.upperLowerSetting.thValUlmt = convertToTimeString(
      rectItem.upperLowerSetting.thValUlmt,
    );
  }

  return rectItem;
}
/**
 * ブロック ノード データの取得
 * @param {*} part - ブロック
 * @param {*} imageMap - image Map
 * @param {*} t - マルチリンガル
 */
export function setBlockNodeJson(
  part: SopFlowDataOption,
  SOPSetData: SopNodeProperty,
  imageMap: Map<string, string>,
) {
  const { commonSetting } = part;
  // const individualPara = JSON.parse(part.individualPara);
  let individualPara = '';
  // [課題2] NULLガード
  if (part.individualPara && part.individualPara !== '') {
    individualPara = JSON.parse(part.individualPara);
  }
  const blockData = {
    shape: 'blockNode',
    sopPartsCD: part.sopPartsCd,
    label: '',
    commonSetting,
    individualPara,
    id: part.nodeId,
    zIndex: 0,
    width: SOPSetData.sopPartConst?.partWidth,
    height: SOPSetData.sopPartConst?.partHeight,
    attrs: {
      image: '',
      body: {
        stroke: '#a8b0c2',
        fill: '#f0ffff',
      },
      title: {
        text: commonSetting.sopNodeNmJp,
        refX: 55,
        refY: 40,
        fill: '#000000',
        fontSize: 16,
        fontWeight: 'bold',
        textAnchor: 'left',
      },
      line: {
        width: 8,
        height: 75,
        refX: 10,
        refY: 0,
        rx: 0,
        ry: 0,
        fill: '#ff0000',
        stroke: '#a8b0c2',
        strokeWidth: 0,
      },
      line2: {
        width: 8,
        height: 75,
        refX: 358,
        refY: 0,
        rx: 0,
        ry: 0,
        fill: '#ff0000',
        stroke: '#a8b0c2',
        strokeWidth: 0,
      },
      buttonBlock: {
        refX: 342,
        refY: 1,
      },
      button: {
        width: 15,
        height: 15,
        refX: -20,
        refY: 10,
        rx: 2,
        ry: 2,
        fill: '#f5f5f5',
        stroke: '#000000',
        cursor: 'pointer',
        event: 'node:blockDetailCollapse',
      },
      buttonSign: {
        'xlink:href': imageMap.get('question'),
        width: 10,
        height: 10,
        refX: -17,
        refY: 12,
        rx: 2,
        ry: 2,
      },
    },
    markup: [
      {
        tagName: 'rect',
        selector: 'body',
      },
      {
        tagName: 'image',
        selector: 'image',
      },
      {
        tagName: 'text',
        selector: 'label',
      },
      {
        tagName: 'text',
        selector: 'title',
      },
      {
        tagName: 'rect',
        selector: 'line',
      },
      {
        tagName: 'rect',
        selector: 'line2',
      },
      {
        tagName: 'g',
        selector: 'buttonBlock',
        children: [
          {
            tagName: 'rect',
            selector: 'button',
            attrs: {
              'pointer-events': 'visiblePainted',
            },
          },
          {
            tagName: 'image',
            selector: 'buttonSign',
            attrs: {
              fill: 'none',
              'pointer-events': 'none',
            },
          },
        ],
      },
    ],
  };
  return blockData;
}
/**
 * コントロール ノード データの取得
 * @param {*} part - ブロック
 * @param {*} partsType - parts type
 * @param {*} SOPSetData - SOP Setting Data
 */
export function setControlPartJson(
  part: SopFlowDataOption,
  partsType: string,
  SOPSetData: SopNodeProperty,
  t: ComposerTranslation,
) {
  const controls = SOPSetData.controlParts;
  const { commonSetting } = part;
  // const individualPara = JSON.parse(part.individualPara);
  let individualPara = null;
  // [課題2] NULLガード
  if (part.individualPara && part.individualPara !== '') {
    individualPara = JSON.parse(part.individualPara);
  }
  let controlIndex = -1;
  controls.forEach((item, index: number) => {
    if (item.sopPartsCD === part.sopPartsCd) {
      controlIndex = index;
    }
  });
  const controlData: SopControlPartOption = {
    ...controls[controlIndex],
    id: part.nodeId,
    zIndex: 0,
    commonSetting,
    individualPara,
  };
  if (partsType === 'start') {
    controlData.width = 46;
    controlData.height = 46;
    controlData.shape = 'start';
    controlData.sopPartsCD = SOP_PARTS_VARIABLES.PART_SPEC_START_CD;
    controlData.attrs = {
      body: {
        rx: 6,
        ry: 6,
        refPoints: '',
        stroke: '#9bb3e2',
        strokeWidth: 2,
        fill: '#f1f3fe',
      },
      text: {
        text: t('SOP.Menu.txtBegin'),
      },
      button: {
        width: 24,
        height: 24,
        refX: 1,
        refY: 1,
        rx: 2,
        ry: 2,
        fill: 'transparent',
        stroke: '#000000',
        strokeWidth: 0,
        cursor: 'pointer',
        event: 'node:sopPartAdd',
      },
    };
  } else if (partsType === 'end') {
    controlData.width = 46;
    controlData.height = 46;
    controlData.shape = 'end';
    controlData.sopPartsCD = SOP_PARTS_VARIABLES.PART_SPEC_END_CD;
    controlData.attrs = {
      body: {
        rx: 6,
        ry: 6,
        refPoints: '',
        stroke: '#9bb3e2',
        strokeWidth: 2,
        fill: '#f1f3fe',
      },
      text: {
        text: t('SOP.Menu.txtEnd'),
      },
      button: {
        width: 24,
        height: 24,
        refX: 1,
        refY: 1,
        rx: 2,
        ry: 2,
        fill: 'transparent',
        stroke: '#000000',
        strokeWidth: 0,
        cursor: 'pointer',
        event: 'node:sopPartAdd',
      },
    };
  } else if (partsType === SOP_PARTS_TYPE_VARIABLES.TYPE_JOIN) {
    controlData.width = 108;
    controlData.height = 46;
  } else {
    controlData.width = 130;
    controlData.height = 36;
  }
  return controlData;
}
/**
 *  ノード データの取得
 * @param {*} part - ブロック
 * @param {*} partsType - parts type
 * @param {*} SOPSetData - SOP Setting Data
 */
export function setAddPartJson(
  part: SopFlowDataOption,
  partsType: string,
  SOPSetData: SopNodeProperty,
  imageMap: Map<string, string>,
  parentNode: SopFlowDataOption,
  t: ComposerTranslation,
) {
  const controls = SOPSetData.controlParts;
  const { commonSetting } = part;
  let individualPara = null;
  // [課題2] NULLガード
  if (part.individualPara && part.individualPara !== '') {
    individualPara = JSON.parse(part.individualPara);
  }
  let controlIndex = -1;
  controls.forEach((item, index: number) => {
    if (item.sopPartsCD === part.sopPartsCd) {
      controlIndex = index;
    }
  });
  const controlData: SopControlPartOption = {
    ...controls[controlIndex],
    id: part.nodeId,
    zIndex: 0,
    commonSetting,
    individualPara,
  };
  if (partsType !== SOP_PARTS_TYPE_VARIABLES.TYPE_JOIN) {
    return false;
  }
  if (part.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD) {
    controlData.width = 24; // [課題1]保存したデータを開くと連結線は直ではない対応 他の[課題1]箇所の定義と一致する必須
    controlData.height = 24; // [課題1]保存したデータを開くと連結線は直ではない対応 他の[課題1]箇所の定義と一致する必須
    controlData.shape = 'addPart';
    controlData.sopPartsCD = part.sopPartsCd;
    controlData.attrs = {
      body: {
        stroke: '#FFFFFF',
        strokeWidth: 0,
        fill: 'transparent',
      },
      image: {
        'xlink:href': imageMap.get('add'),
      },
      button: {
        width: 24,
        height: 24,
        refX: 1,
        refY: 1,
        rx: 2,
        ry: 2,
        fill: 'transparent',
        stroke: '#000000',
        strokeWidth: 0,
        cursor: 'pointer',
        event: 'node:sopPartAdd',
      },
    };
  } else if (part.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD) {
    controlData.width = 24;
    controlData.height = 24;
    controlData.shape = 'addPart';
    controlData.sopPartsCD = part.sopPartsCd;
    // controlData.label = part.commonSetting.sopNodeNmJp;
    controlData.attrs = {
      body: {
        stroke: '#FFFFFF',
        strokeWidth: 0,
        fill: 'transparent',
      },
      image: {
        'xlink:href': imageMap.get('add'),
      },
      button: {
        width: 24,
        height: 24,
        refX: 1,
        refY: 1,
        rx: 2,
        ry: 2,
        fill: 'transparent',
        stroke: '#000000',
        strokeWidth: 0,
        cursor: 'pointer',
        event: 'node:sopPartAdd',
      },
    };
    // TODO: 分岐パーツに分岐情報(SelectBranchOption)セット
    // controlData.branchOption = {
    // label: 'aaa',       // 分岐方法「条件分岐」→分岐1:条件分岐1 分岐2:条件分岐2 メニュー選択」分岐1:メニュー選択1 分岐2:メニュー選択2
    // value: '',          // 分岐方法「条件分岐」→条件式をセット「メニュー選択」→ 空
    // type: 'conditional', // 分岐方法「条件分岐」→"conditional" メニュー選択」→ "partId"
    // }
    // [課題2] NULLガード
    if (parentNode.individualPara && parentNode.individualPara !== '') {
      const parentNodeIndividualPara = JSON.parse(parentNode.individualPara);
      let branchOption: SelectBranchOption;
      if (parentNodeIndividualPara.branchMethod === '0') {
        const method = t('SOP.Chr.SOPDetailSetting.txtMenuSelection');
        branchOption = {
          label: method, // `${method}${count + 1}`,
          value: '',
          type: 'partId',
        };
      } else {
        const branch = t('SOP.Menu.txtConditionBranch');
        branchOption = {
          label: branch, // `${branch}${count + 1}`,
          value: '', // parentNodeIndividualPara.branchOption.value,
          type: 'conditional',
        };
      }
      controlData.branchOption = branchOption;
    }
  } else if (part.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD) {
    controlData.width = 24;
    controlData.height = 24;
    controlData.shape = 'addPart';
    controlData.sopPartsCD = part.sopPartsCd;
    // controlData.label = part.commonSetting.sopNodeNmJp;
    controlData.attrs = {
      body: {
        stroke: '#FFFFFF',
        strokeWidth: 0,
        fill: 'transparent',
      },
      image: {
        'xlink:href': imageMap.get('add'),
      },
      button: {
        width: 24,
        height: 24,
        refX: 1,
        refY: 1,
        rx: 2,
        ry: 2,
        fill: 'transparent',
        stroke: '#000000',
        strokeWidth: 0,
        cursor: 'pointer',
        event: 'node:sopPartAdd',
      },
    };

    // TODO: 分岐パーツに分岐情報(SelectBranchOption)セット
    // controlData.branchOption = {
    // label: 'aaa',       // 分岐方法「条件分岐」→ 分岐1:条件分岐1 分岐2:条件分岐2「メニュー選択」分岐1:メニュー選択1 分岐2:メニュー選択2
    // value: '',          // 分岐方法「条件分岐」→ 条件式をセット「メニュー選択」→ 空
    // type: 'conditional', // 分岐方法「条件分岐」→ "conditional"「メニュー選択」→ "partId"
    // }
    let branchOption: SelectBranchOption;
    // [課題2] NULLガード
    if (individualPara && individualPara.branchMethod === '0') {
      const method = t('SOP.Chr.SOPDetailSetting.txtMenuSelection');
      branchOption = {
        label: method, // `${method}${count + 1}`,
        value: '',
        type: 'partId',
      };
    } else {
      const branch = t('SOP.Menu.txtConditionBranch');
      branchOption = {
        label: branch, // `${branch}${count + 1}`,
        value: '', // individualPara.branchOption.value,
        type: 'conditional',
      };
    }
    controlData.branchOption = branchOption;
  } else {
    controlData.width = 130;
    controlData.height = 36;
  }
  return controlData;
}
const checkBranchPartCode = (item: SopRectPartsOption) => {
  const partsCD = item.sopPartsCD;
  let sameCd = false;
  if (item.individualPara === null || item.individualPara === undefined) {
    return false;
  }
  // メニュー分岐(システム分岐)
  if (partsCD === SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD) {
    const individualPara = <PartSystemBranchProps>item.individualPara;
    if (individualPara.branchNumSetting > 0) {
      sameCd = true;
    }
  }
  // メニュー分岐(ボタン分岐)
  else if (partsCD === SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD) {
    const individualPara = <PartButtonBranchProps>item.individualPara;
    if (individualPara.branchNumSetting > 0) {
      sameCd = true;
    }
  }
  // 指示内容確認
  else if (partsCD === SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_CD) {
    const individualPara = <PartInstructionConfirmProps>item.individualPara;
    if (individualPara.conditionBranch === '1') {
      sameCd = true;
    }
  }
  // SOPタイマー
  else if (partsCD === SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD) {
    const individualPara = <PartSopTimerProps>item.individualPara;
    const upperLowerSetting = <PartUpperLowerSetting>item.upperLowerSetting;
    if (upperLowerSetting?.thJudgeFlg === '1') {
      if (individualPara.conditionBranch === '1') {
        sameCd = true;
      }
    }
  }
  // 外部機器通信
  else if (partsCD === SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD) {
    const individualPara = <PartExternalDeviceProps>item.individualPara;
    if (individualPara.connectedDeviceSetting === '0') {
      if (individualPara.conditionBranch === '1') {
        sameCd = true;
      }
    }
  }
  // 計量器点検
  else if (partsCD === SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_CD) {
    const individualPara = <PartWeightCalibrationProps>item.individualPara;
    if (individualPara.conditionBranch === '1') {
      sameCd = true;
    }
  }
  // 数値文字入力、受入投入、実績確定、在庫消費
  else if (
    partsCD === SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD ||
    partsCD === SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD ||
    partsCD === SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD ||
    partsCD === SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD
  ) {
    const conditionPara = item.conditionProps;
    if (conditionPara !== undefined && conditionPara.conditionBranch === '1') {
      sameCd = true;
    }
  }
  return sameCd;
};
/**
 * flow partsの作成
 * @param {*} NodeData - part Data
 * @param {*} SOPSetData - SOP Setting Data
 * @param {*} imageMap - image Map
 * @param {*} t - マルチリンガル
 */
export function createSOPFlowItem(
  NodeData: SopFlowDataOption,
  SOPSetData: SopNodeProperty,
  imageMap: Map<string, string>,
  t: ComposerTranslation,
  parentNode: SopFlowDataOption,
) {
  const { partsType } = getPartsType(SOPSetData, NodeData.sopPartsCd);
  let partItem = {};
  switch (partsType) {
    case SOP_PARTS_TYPE_VARIABLES.TYPE_PART:
      partItem = setPartNodeJson(NodeData, SOPSetData, imageMap);
      break;
    case SOP_PARTS_TYPE_VARIABLES.TYPE_BLOCK:
      partItem = setBlockNodeJson(NodeData, SOPSetData, imageMap);
      break;
    case SOP_PARTS_TYPE_VARIABLES.TYPE_JOIN:
      partItem = setAddPartJson(
        NodeData,
        partsType,
        SOPSetData,
        imageMap,
        parentNode,
        t,
      );
      break;
    default:
      partItem = setControlPartJson(NodeData, partsType, SOPSetData, t);
      break;
  }
  return partItem;
}
/**
 * Flow Chartの作成
 * @param {*} flows - Flowのデータ
 * @param {*} SOPSetData - SOP Setting Data
 * @param {*} imageMap - image Map
 * @param {*} t - マルチリンガル
 */
export function getSOPFlowChartData(
  flows: SopFlowDataOption[],
  SOPSetData: SopNodeProperty,
  imageMap: Map<string, string>,
  t: ComposerTranslation,
) {
  const nodeData = [];
  let parentNode: SopFlowDataOption = {
    parentSopNodeNo: '',
    nodeId: '',
    helpSetting: {
      helpFileType: '',
      helpBinPath1: '',
      helpBinPath2: '',
      helpBinPath3: '',
      helpBinPath4: '',
      helpBinPath5: '',
      helpTxt1: '',
      helpTxt2: '',
      helpTxt3: '',
    },
    commonSetting: {
      sopNodeNmJp: '',
      cmtMain1: '',
      cmtMain2: '',
      cmtMain3: '',
      cmtEm: '',
      cmtSub1: '',
      cmtSub2: '',
      cmtSub3: '',
      dcheckFlg: '',
      dcheckPrivGrpCd: '',
      devCorrLv: '0',
      deviationInputFlg: '',
      deviationPrivGrpCd: '',
      scnShowFlg: '',
      runLoopFlg: '',
      workBreakFlg: '',
      skippableFlg: '',
      helpShowFlg: '',
      confShowFlg: '',
      recFillFlg: '',
      recReType: '',
      recConfFlg: '',
      helpFileType: '',
      helpBinPath1: '',
      helpBinPath2: '',
      helpBinPath3: '',
      helpBinPath4: '',
      helpBinPath5: '',
      helpTxt1: '',
      helpTxt2: '',
      helpTxt3: '',
    },
    individualPara: '',
    sopJoin: {
      nextCondSeq: 0,
      nextCondLeft: '',
      nextCondOpe: '',
      nextCondRight: '',
      nextNodeNo: '',
    },
    sopCondition: {
      condition1: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition2: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition3: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition4: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition5: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition6: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
    },
    sopCieX: 0,
    sopCieY: 0,
    sopFlowNo: '',
    sopNodeNo: '',
    sopPartsCd: '',
    blkFlowNo: '',
    // [課題369] DEL ST ブロック/テンプレート登録のリファクタリング
    // blkSopFlowVer: 0,
    // [課題369] DEL ED
    blkSopSeqNo: 0,
    dspSeq: 0,
    upperLowerSetting: {
      thJudgeFlg: '',
      thRangeType: '',
      thValType: '',
      thValLlmt: 0,
      thValUlmt: 0,
    },
    instUnitTxt: '',
  };
  for (let count = 0; count < flows.length; count++) {
    if (flows[count].blkFlowNo !== '_') {
      // TODO: ブロック詳細パーツは描画しない
      // break;
    }
    const rowPartItem = createSOPFlowItem(
      flows[count],
      SOPSetData,
      imageMap,
      t,
      parentNode,
    );
    const partItem = {
      ...rowPartItem,
      x: flows[count].sopCieX,
      y: flows[count].sopCieY,
    };
    nodeData.push(partItem);

    // 条件分岐が設定されている場合、パーツ情報をprentNodeに設定する
    const item = <SopRectPartsOption>rowPartItem;
    const sameCdFlg = checkBranchPartCode(item);
    if (sameCdFlg) {
      parentNode = flows[count];
    }
  }
  return nodeData;
}
/**
 * SOP work flow item data
 * @param {*} itemNber - itemNber
 * @param {*} sopFlowNoVal - sopFlowNo
 * @param {*} nodeData - nodeData
 */
export function setSOPFlowItemModel(
  itemNber: NeighborsOption,
  sopFlowNoVal: string,
  nodeData: SopPartDataOption,
  nextNodeId: string,
  SOPSetData: NodeProperty,
  graphWidth: number,
) {
  const { outgoing } = itemNber;
  const { sopPartConst } = SOPSetData;
  const individualParaVal = JSON.stringify(nodeData.individualPara);
  let positionX = 0;
  if (nodeData.sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD) {
    // プラスボタンのX座標の微調整
    const posX = graphWidth / 2 - sopPartConst!.startPartWidth / 2;
    const controls: SopControlPartOption[] = SOPSetData.startParts;
    const rectXs = controls.map((item) => {
      const rectItem = { ...item };
      if (item.sopPartsCD === undefined) {
        return 0;
      }
      let rectX = posX;
      if (!rectItem.width) {
        return 0;
      }
      rectX = posX - (rectItem.width - sopPartConst!.startPartWidth) / 2;
      return rectX;
    });
    const rectX: number[] = [];
    rectXs.forEach((item) => {
      if (item === undefined) {
        return;
      }
      const numX = Math.floor(item);
      if (numX === 490 || numX === 691) {
        rectX.push(numX - sopPartConst!.addPartWidth / 2 - 2);
      } else if (numX === 503 || numX === 704) {
        rectX.push(numX - 2);
      }
    });
    const nodePositionX = Math.floor(nodeData.position.x);
    rectX.forEach((item) => {
      if (item === nodePositionX || item === nodePositionX - 2) {
        positionX = item;
        // return;
      }
    });
    if (positionX === 0) {
      positionX = nodePositionX;
    }
  } else {
    positionX = Math.floor(nodeData.position.x);
  }
  let setUpperLowerSetting;
  if (nodeData.upperLowerSetting === undefined) {
    setUpperLowerSetting = {
      thJudgeFlg: '',
      thRangeType: '',
      thValType: '',
      thValLlmt: 0,
      thValUlmt: 0,
    };
  } else {
    const llmt =
      nodeData.upperLowerSetting.thValLlmt === ''
        ? null
        : nodeData.upperLowerSetting.thValLlmt;
    const ulmt =
      nodeData.upperLowerSetting.thValUlmt === ''
        ? null
        : nodeData.upperLowerSetting.thValUlmt;

    setUpperLowerSetting = {
      thJudgeFlg: nodeData.upperLowerSetting.thJudgeFlg,
      thRangeType: nodeData.upperLowerSetting.thRangeType,
      thValType: nodeData.upperLowerSetting.thValType,
      thValLlmt: toNumberOrNull(llmt),
      thValUlmt: toNumberOrNull(ulmt),
    };
  }
  const flowData: SopFlowGetDataOption = {
    nodeId: itemNber.id,
    blkFlowNo: '_',
    // [課題369] DEL ST ブロック/テンプレート登録のリファクタリング
    // blkSopFlowVer: 0,
    // [課題369] DEL ED
    blkSopSeqNo: 0,
    sopJoin: {
      nextCondSeq: outgoing.length,
      nextCondLeft: '',
      nextCondOpe: '',
      nextCondRight: '',
      nextNodeNo: nextNodeId,
    },
    sopCondition: nodeData.sopCondition,
    sopCieX: positionX, // Math.floor(nodeData.position.x),
    sopCieY: Math.floor(nodeData.position.y),
    sopFlowNo: sopFlowNoVal,
    sopNodeNo: itemNber.id,
    sopPartsCd: nodeData.sopPartsCD,
    commonSetting: nodeData.commonSetting,
    helpSetting: nodeData.helpSetting,
    individualPara: individualParaVal,
    dspSeq: nodeData.dspSeq,
    // [課題399] ADD ST
    childNodeFlg: '',
    parentSopNodeNo: '',
    // [課題399] ADD ED
    upperLowerSetting: setUpperLowerSetting,
    instUnitTxt: nodeData.instUnitTxt,
  };
  // TODO: 条件分岐対応
  // const branchObj = nodeData.individualPara;
  // for (let count = 1; count < 9; count++) {
  //   let condBrCondVal = '';
  //   let condBrDstVal = '';
  //   if (outgoing.length > 0 && count <= outgoing.length) {
  //     condBrCondVal = outgoing[count - 1].value;
  //     if (nodeData.sopPartsCD === SOP_PARTS_VARIABLES.CONTROL_PART_COND_BRANCH_NAME) {
  //       const conditionalNode = `conditionalNode${count}`;
  //       if (hasKey(branchObj, conditionalNode)) {
  //         if (branchObj[conditionalNode] !== '') {
  //           condBrCondVal = branchObj[conditionalNode];
  //         }
  //       }
  //       const conditional = `conditional${count}`;
  //       if (hasKey(branchObj, conditional)) {
  //         condBrDstVal = branchObj[conditional];
  //       }
  //     }
  //   }
  //   const condBrCond = `condBrCond${count}`;
  //   if (hasKey(flowData, condBrCond)) {
  //     flowData[condBrCond] = condBrCondVal;
  //   }
  //   const condBrDst = `condBrDst${count}`;
  //   if (hasKey(flowData, condBrDst)) {
  //     flowData[condBrDst] = condBrDstVal;
  //   }
  // }
  if (nodeData.sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD) {
    flowData.commonSetting.sopNodeNmJp = nodeData.label;
  }
  return flowData;
}

/**
 * 画像取得
 */
export function getImageMap(images: ImageOption[]) {
  const imageMap = new Map();
  images.forEach((v) =>
    imageMap.set(v.fileName, new URL(v.path, import.meta.url).href),
  );
  return imageMap;
}

/**
 * Block part node code Check
 * @param {*} sopPartsCD - node Code
 * @param {*} SOPSetData - SOP Setting Data
 */
export function checkBlockPartCode(
  SOPSetData: SopNodeProperty,
  sopPartsCD: string,
) {
  const partCds = SOPSetData.blockPartCds;
  const sameCd = partCds.filter((item) => item === sopPartsCD);
  return sameCd.length > 0;
}

/**
 * Condition part node code Check
 * @param {*} sopPartsCD - node Code
 * @param {*} SOPSetData - SOP Setting Data
 */
export function checkConditionPartCode(
  SOPSetData: SopNodeProperty,
  sopPartsCD: string,
) {
  const partCds = SOPSetData.conditionPartCds;
  const sameCd = partCds.filter((item) => item === sopPartsCD);
  return sameCd.length > 0;
}
/**
 * パーツコード名称変換
 * @param {*} sopPartsCD
 */
export function convertSopPartsName(sopPartsCD: string) {
  // TODO:パーツ種のマスタ連携
  let sopPartsName = '';
  switch (sopPartsCD) {
    case SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_SOP_TIMER_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_DATE_RECORD_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_DATE_RECORD_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_ELECTRONIC_FILE_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_ELECTRONIC_FILE_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_ELECTRONIC_SHELF_LABEL_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_ELECTRONIC_SHELF_LABEL_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_EQUIPMENT_CONTAINER_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_EQUIPMENT_CONTAINER_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_LABEL_OUTPUT_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_LABEL_OUTPUT_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_PALLET_CARGO_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_PALLET_CARGO_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_SPEC_START_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_SPEC_START_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_SPEC_END_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_SPEC_END_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_SPEC_JOIN_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_SPEC_TEMPLATE_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_SPEC_TEMPLATE_NAME;
      break;
    case SOP_PARTS_VARIABLES.PART_SPEC_COPY_CD:
      sopPartsName = SOP_PARTS_VARIABLES.PART_SPEC_COPY_NAME;
      break;
    default:
  }
  return sopPartsName;
}

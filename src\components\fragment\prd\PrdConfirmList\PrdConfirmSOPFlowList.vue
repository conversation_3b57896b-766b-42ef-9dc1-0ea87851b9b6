<template>
  <!-- 製造記録確認_SOP記録ダイアログ -->
  <DialogWindow
    :title="$t('Prd.Chr.txtConfirmSopFlowList')"
    :dialogVisible="dialogVisibleRef.sopRecDialog"
    :buttons="dialogButtons"
    @closeDialog="() => closeDialog('sopRecDialog')"
  >
    <!-- 見出し 製造指図情報 -->
    <BaseHeading
      level="2"
      :text="$t('Prd.Chr.txtOrderInformation')"
      fontSize="24px"
      class="title-box"
    />
    <!-- 製造指図情報の見出し+テキスト項目表示 -->
    <InfoShow
      :infoShowItems="orderDetailInfoShowRef.infoShowItems"
      :isLabelVertical="orderDetailInfoShowRef.isLabelVertical"
    />
    <!-- 見出し -->
    <BaseHeading
      level="2"
      :text="$t('Prd.Chr.txtSopFlowList')"
      fontSize="24px"
      class="table-title"
    />
    <!-- 共通のテーブル -->
    <TabulatorTable
      :propsData="tablePropsDataRef"
      :routerName="props.routerName"
      @selectRow="movedSelectedRowRef"
    />
  </DialogWindow>
  <!-- SOP作業詳細ダイアログ -->
  <PrdConfirmSOPFlowInfoList
    :odrNo="props.odrNo"
    :infoData="props.infoData"
    :prcNo="props.prcNo"
    :prcSeq="props.prcSeq"
    :selectedSopRecData="selectedRow"
    :isClicked="isClickedPrdConfirmSOPFlowInfoListDialogRef"
    :routerName="props.routerName"
    :privilegesBtnRequestData="props.privilegesBtnRequestData"
    @submit="prdConfirmSOPFlowListInit()"
  />
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import { closeLoading, showLoading } from '@/utils/dialog';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import {
  GetConfirmSOPFlowListRequestData,
  GetConfirmSOPFlowListData,
  GetConfirmInfoListData,
} from '@/types/HookUseApi/PrdTypes';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import { useGetSopFlowListInit } from '@/hooks/useApi';
import PrdConfirmSOPFlowInfoList from '@/components/fragment/prd/PrdConfirmList/PrdConfirmSOPFlowInfoList.vue';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import { DialogProps } from '@/types/MessageBoxTypes';
import BaseHeading from '@/components/base/BaseHeading.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import { InfoShowType } from '@/types/InfoShowTypes';
import getOrderDetailInfoShowItems from './prdConfirmSOPFlowList';

const { t } = useI18n();
const emit = defineEmits(['clickCancel']);

const orderDetailInfoShowRef = ref<InfoShowType>({
  infoShowItems: getOrderDetailInfoShowItems(),
  isLabelVertical: true,
});

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// ダイアログの表示切替用定義
const initialState: InitialDialogState<DialogRefKey> = {
  sopRecDialog: false,
  messageBoxApiErrorVisible: false,
};

const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

type DialogRefKey = 'sopRecDialog' | 'messageBoxApiErrorVisible';

// 作業詳細ボタン クリック
const isClickedPrdConfirmSOPFlowInfoListDialogRef = ref<boolean>(false);

// 選択行データ
let selectedRow: GetConfirmSOPFlowListData | null = null;

const movedSelectedRowRef = (v: GetConfirmSOPFlowListData | null) => {
  selectedRow = v;
};

// NOTE:このダイアログはキャンセルのみ。デフォルト挙動を上書きするための定義。
// ダイアログ用ボタン設定
const dialogButtons: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    clickHandler: () => {
      // NOTE:特殊対応:子以下の階層で更新の可能性があるため、排他チェックエラーの対象とならないよう親に再更新を促す。
      // NOTE:再検索の意図で呼び出すため、本来は必要な時だけ発行したいが工数削減のため常時通知
      // ダイアログを閉じたことを親に通知する
      emit('clickCancel');
    },
  },
];

/**
 * 作業詳細ボタン押下時
 */
const clickSopFlowDetailBtn = () => {
  isClickedPrdConfirmSOPFlowInfoListDialogRef.value =
    !isClickedPrdConfirmSOPFlowInfoListDialogRef.value;
};

type Props = {
  odrNo?: string; // 遷移元から引き継ぐ製造指図番号
  prcSeq?: number; // 遷移元から引き継ぐ製造工程順
  prcNo?: string; // 遷移元から引き継ぐ製造工程番号
  odrSts?: string; // 遷移元から引き継ぐ指図状態
  infoData: GetConfirmInfoListData | null; // 遷移元から引き継ぐ画面表示項目
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  routerName: string; // TabulatorTable権限
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();

const tablePropsDataRef = ref<TabulatorTableIF>({
  pageName: 'PrdConfirmSOPFlowList',
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'uniqueKey',
  showRadio: true,
  tableBtns: [],
  onSelectBtns: [
    {
      text: 'Prd.Chr.btnPrcFlowDetail',
      tabulatorActionId: props.privilegesBtnRequestData.btnId,
      type: 'primary',
      clickHandler: clickSopFlowDetailBtn,
    },
  ],
  column: [
    // 確認状態
    {
      title: 'Prd.Chr.txtConfirmState',
      field: 'recConfirmFlgDsp',
      width: COLUMN_WIDTHS.PRD.REC_CONFIRM_FLG,
    },
    // フロー状態
    {
      title: 'Prd.Chr.txtFlowState',
      field: 'sopFlowSts',
      width: COLUMN_WIDTHS.PRD.SOP_FLOW_STS,
    },
    // 異状レベル
    {
      title: 'Prd.Chr.txtDeviationLevel',
      field: 'devCorrLv',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.DEV_LV,
    },
    // SOPフロー名
    {
      title: 'Prd.Chr.txtSopFlowName',
      field: 'sopFlowNmJp',
      width: COLUMN_WIDTHS.SOP_FLOW_NM,
    },
    // バッチ番号
    {
      title: 'Prd.Chr.txtBatchNo',
      field: 'batchNo',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.BAT_NO,
    },
    // SOPフロー開始日時
    {
      title: 'Prd.Chr.txtSOPFlowStartDate',
      field: 'stDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDD,
    },
    // SOPフロー終了日時
    {
      title: 'Prd.Chr.txtSOPFlowEndDate',
      field: 'edDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDD,
    },
    // SOPフロー記録検印日
    {
      title: 'Prd.Chr.txtSOPFlowRecordStampDate',
      field: 'recConfirmDts',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDD,
    },
    // 異状コメント
    {
      title: 'Prd.Chr.txtDeviationComment',
      field: 'devExpl',
      width: COLUMN_WIDTHS.PRD.DEV_EXPL_EXIST,
    },
    // 作業コメント
    {
      title: 'Prd.Chr.txtWorkComment',
      field: 'msgExpl',
      width: COLUMN_WIDTHS.PRD.MSG_EXPL_EXIST,
    },
    // 修正コメント
    {
      title: 'Prd.Chr.txtModifyComment',
      field: 'modExpl',
      width: COLUMN_WIDTHS.PRD.MOD_EXPL_EXIST,
    },
    // SOP記録確認コメント
    {
      title: 'Prd.Chr.txtSopRecordConfirmComment',
      field: 'recConfirmExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    // 異状確認コメント
    {
      title: 'Prd.Chr.txtDifferentConfirmComment',
      field: 'recConfirmDevExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    // 背景色指定隠しカラムとして運用
    {
      title: '',
      field: 'backgroundColor',
      hidden: true,
    },
    // ラジオボタン用key隠しカラムとして運用
    {
      title: '',
      field: 'uniqueKey',
      hidden: true,
    },
  ],
  rowColor: {
    useColor: true,
    colorColumn: 'backgroundColor',
  },
  tableData: [],
  showConditionSearch: false,
  noUseConditionSearch: true,
});

/**
 * ラジオボタン用key追加
 * @param {CustomOptionsData[]} tableData - テーブル表示データ
 */

/**
 * SOP記録ダイアログの初期設定
 * NOTE:子ダイアログ実行後の再検索でも呼び出される。
 * 本来は処理を分けるべきだが工数削減のためInit関数を使いまわす。
 * 問題が出たら正しく実装してください。
 */
const prdConfirmSOPFlowListInit = async () => {
  // 選択行情報を初期化
  selectedRow = null;

  if (!props.odrNo || !props.prcSeq) {
    return;
  }

  // ローディング表示
  showLoading();

  // SOP記録初期表示APIを呼び出す
  const apiRequestData: GetConfirmSOPFlowListRequestData = {
    odrNo: props.odrNo,
    prcSeq: props.prcSeq,
  };
  const { responseRef, errorRef } = await useGetSopFlowListInit({
    ...props.privilegesBtnRequestData,
    ...apiRequestData,
  });
  if (errorRef.value) {
    closeLoading();
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }
  if (responseRef.value) {
    const resData = responseRef.value?.data;
    tablePropsDataRef.value.tableData = resData.rData.sopFlowList;
    // レスポンスに存在しないユニークキーを独自に設定して隠しカラムに仕込む対応
    tablePropsDataRef.value.tableData.forEach((value) => {
      const tableData = value;
      tableData.uniqueKey = `${value.sopFlowNo}-${value.sopFlowLnum}`;
    });
  }

  if (props.infoData) {
    // 指図詳細情報レイアウト用初期値設定
    Object.entries(props.infoData).forEach(([key, value]) => {
      if (key in orderDetailInfoShowRef.value.infoShowItems) {
        orderDetailInfoShowRef.value.infoShowItems[key].infoShowModelValue =
          value?.toString() ?? '';
      }
    });
  }

  orderDetailInfoShowRef.value.infoShowItems.odrNo.infoShowModelValue =
    props.odrNo;

  if (props.odrSts) {
    orderDetailInfoShowRef.value.infoShowItems.odrSts.infoShowModelValue =
      props.odrSts;
  }

  closeLoading();
  // 問題なければダイアログ起動する
  openDialog('sopRecDialog');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    prdConfirmSOPFlowListInit();
  },
);
</script>
<style lang="scss" scoped>
.title-box {
  margin-bottom: 15px;
}
.table-title {
  margin-top: 15px;
}
</style>

// @ts-nocheck
import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import eslintPlugin from 'vite-plugin-eslint';
import path from 'path-browserify';
import { resolve } from 'path';
import Components from 'unplugin-vue-components/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
const CONST = require('./src/constants/utils.ts');
const port = 8013;
const uri = {
  'dev:backend': '10.194.222.50:8001', // npm run dev:be の接続先
  production: '10.187.97.35:8002', // npm run build:prod の接続先
  'prod:develop': '10.187.97.35:8003', // npm run build:prod:dev の接続先
  'prod:verification': '10.187.97.35:8004', // npm run build:prod:verif の接続先
};

// Viteのモードフラグを取得
const viteMode = process.argv.at(-1);

// ローカル動作用
const baseUrlHttps = `https://${uri[viteMode]}`;
const baseUrlHttp = `http://${uri[viteMode]}`;

// Docker、Nginx作成用
// const BASE_URL_HTTPS = ''
// const BASE_URL_HTTP = ''

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const envMode = loadEnv(mode, process.cwd(), '');
  return {
    plugins: [
      createSvgIconsPlugin({
        iconDirs: [path.resolve('', 'src/assets/icons/svg')],
        symbolId: 'icon-[name]',
      }),
      // eslintPlugin({
      //   include: ['src/**/*.js', 'src/**/*.vue', 'src/*.js', 'src/*.vue'],
      // }),
      vue(),
      Components({
        dts: false,
        resolvers: [
          (componentName: string) => {
            if (componentName.startsWith('El')) {
              return {
                name: componentName,
                as: componentName,
                from: 'element-plus',
              };
            }
          },
        ],
      }),
    ],
    define: {
      'process.env': {
        BASE_URL_HTTPS: baseUrlHttps,
        BASE_URL_HTTP: baseUrlHttp,
        BASE_VITE_MODE: viteMode,
        NPM_PACKAGE_VERSION: envMode.npm_package_version,
      },
    },
    build: {
      outDir: 'dist',
      assetsDir: 'assets',
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use '@/assets/scss/global.scss' as *;`,
        },
      },
    },
    base: '/',
    publicDir: 'public',
    server: {
      port: port,
      open: false,
      https: CONST.IS_HTTPS,
      proxy: {
        '/mpc': {
          target: CONST.IS_HTTPS ? baseUrlHttps : baseUrlHttp,
          changeOrigin: true,
          selfHandleResponse: true,
          configure: (proxy, _options) => {
            proxy.on('proxyReq', (proxyReq, req, res) => {
              let addr = req.socket.remoteAddress;
              let xff = req.headers['X-Forwarded-For'];
              xff = (xff || '') + (xff ? ',' : '') + addr;
              proxyReq.setHeader('X-Forwarded-For', xff);
            });
            proxy.on('proxyRes', (proxyRes, req, res) => {
              proxyRes.pipe(res);
            });
          },
        },
      },
    },
    resolve: {
      alias: [
        {
          find: '@',
          replacement: resolve(__dirname, 'src'),
        },
        {
          find: 'vue-i18n',
          replacement: 'vue-i18n/dist/vue-i18n.cjs.js',
        },
      ],
    },
    test: {
      environment: 'jsdom',
      includeSource: ['src/**/*.{vue,ts}'],
      coverage: {
        provider: 'v8',
        include: ['src/**/*.{vue,ts}'],
        exclude: ['src/stories/**', 'src/store/**', 'src/tests/**'],
      },
      testTimeout: 15000,
      hookTimeout: 30000,
    },
  };
});

<template>
  <!-- 出庫指示詳細 -->
  <DialogWindow
    :title="$t('Trf.Chr.txtInstructionDetails')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
    :buttons="[
      {
        text: $t('Cm.Chr.btnCancel'),
        type: 'secondary',
        size: 'normal',
        clickHandler: () => {
          closeDialog('fragmentDialogVisible');
        },
      },
    ]"
  >
    <div class="custom-form-container">
      <CustomForm
        :formModel="trfShipmentInstructionDetailsFormRef.formModel"
        :formItems="trfShipmentInstructionDetailsFormRef.formItems"
        @visible="
          (v: CustomFormType['customForm']) => {
            trfShipmentInstructionDetailsFormRef.customForm = v;
          }
        "
      />
    </div>
    <div class="Util_mt-32">
      <BaseHeading
        level="2"
        fontSize="24px"
        :text="$t('Trf.Chr.txtInstructionDetailsList')"
      />
      <TabulatorTable :propsData="tablePropsData" />
    </div>
  </DialogWindow>
  <MessageBox
    v-if="dialogVisibleRef.messageBoxSingleButtonRef"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('messageBoxSingleButtonRef')"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import { TrfInstListData } from '@/types/HookUseApi/TrfTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import CustomForm from '@/components/parts/CustomForm.vue';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { useGetTransferShipmentInstructionDetails } from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import {
  trfShipmentInstructionDetailsFormModel,
  getTrfShipmentInstructionDetailsFormItems,
  tablePropsData,
} from './trfShipmentInstructionDetails';

const trfShipmentInstructionDetailsFormRef = ref<CustomFormType>({
  formItems: getTrfShipmentInstructionDetailsFormItems(),
  formModel: trfShipmentInstructionDetailsFormModel,
});

type Props = {
  selectedRows: TrfInstListData[];
  isClicked: boolean;
  commonActionRequestData: CommonRequestType;
};
/**
 * 多言語
 */
const { t } = useI18n();
const props = defineProps<Props>();

type DialogRefKey = 'messageBoxSingleButtonRef' | 'fragmentDialogVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  messageBoxSingleButtonRef: false,
  fragmentDialogVisible: false,
};
const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

/**
 * 初期設定
 */
const trfShipmentInstructionDetailsInit = async () => {
  if (props.selectedRows.length === 0) {
    messageBoxSingleButtonRef.value.type = 'error';
    messageBoxSingleButtonRef.value.title = t('Cm.Chr.txtUnselectedData');
    messageBoxSingleButtonRef.value.content = t('Cm.Msg.unselectedData');
    openDialog('messageBoxSingleButtonRef');
    return;
  }
  showLoading();

  const { responseRef, errorRef } =
    await useGetTransferShipmentInstructionDetails({
      ...props.commonActionRequestData,
      trfInstGrpNo: props.selectedRows.at(0)!.trfInstGrpNo,
    });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('messageBoxSingleButtonRef');
    closeLoading();
    return;
  }
  if (responseRef.value) {
    tablePropsData.tableData = responseRef.value.data.rData.trfInstDetailList;
    setFormModelValueFromApiResponse(
      trfShipmentInstructionDetailsFormRef,
      responseRef.value.data.rData.trfInstDetailList[0],
    );
  }

  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(() => props.isClicked, trfShipmentInstructionDetailsInit);
</script>
<style lang="scss" scoped></style>

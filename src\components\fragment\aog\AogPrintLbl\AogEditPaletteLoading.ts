import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import CONST from '@/constants/utils';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';

const { t } = i18n.global;

// 個装在庫修正ダイアログのアイテム定義
export const getDialogInformationInfoShowItems: () => CustomFormType['formItems'] =
  () => ({
    pltNo: {
      label: { text: t('Aog.Chr.txtPltNo') },
      formModelValue: '',
      formRole: 'textBox',
      span: 24,
      props: { disabled: true, size: 'small' },
    },
    aogInstNo: {
      label: { text: t('Aog.Chr.txtAogInstNo') },
      formModelValue: '',
      formRole: 'textBox',
      span: 12,
      props: { disabled: true, size: 'small' },
    },
    aogInstGrpNo: {
      label: { text: t('Aog.Chr.txtAogInstGrpNo') },
      formModelValue: '',
      formRole: 'textBox',
      span: 12,
      props: { disabled: true, size: 'small' },
    },
    aogInstGrpPrtDts: {
      label: { text: t('Aog.Chr.txtAogInstGrpPrtDts') },
      formModelValue: '',
      formRole: 'textBox',
      span: 12,
      props: { disabled: true, size: 'small' },
    },
    aogYmd: {
      label: { text: t('Aog.Chr.txtAogYmd') },
      formModelValue: '',
      formRole: 'textBox',
      span: 12,
      props: { disabled: true, size: 'small' },
    },
    poDtlNo: {
      label: { text: t('Aog.Chr.txtPurchaseDetailOrderNumber') },
      formModelValue: '',
      formRole: 'textBox',
      span: 12,
      props: { disabled: true, size: 'small' },
    },
    matNo: {
      label: { text: t('Aog.Chr.txtItemCode') },
      formModelValue: '',
      formRole: 'textBox',
      span: 24,
      props: { disabled: true, size: 'small' },
    },
    matNm: {
      label: { text: t('Aog.Chr.txtItemName') },
      formModelValue: '',
      formRole: 'textBox',
      span: 24,
      props: { disabled: true, size: 'large' },
    },
    mBpId: {
      label: { text: t('Aog.Chr.txtBusinessPartnerCode') },
      formModelValue: '',
      formRole: 'textBox',
      span: 24,
      props: { disabled: true, size: 'small' },
    },
    bpNm: {
      label: { text: t('Aog.Chr.txtBusinessPartnerName') },
      formModelValue: '',
      formRole: 'textBox',
      span: 24,
      props: { disabled: true, size: 'large' },
    },
    edNo: {
      label: { text: t('Aog.Chr.txtEditionNumber') },
      formModelValue: '',
      formRole: 'textBox',
      span: 24,
      props: { disabled: true, size: 'small' },
    },
    lotNo: {
      label: { text: t('Aog.Chr.txtLotNo') },
      formModelValue: '',
      formRole: 'textBox',
      span: 24,
      props: { disabled: true, size: 'small' },
    },
    rsltPckExpl: {
      label: { text: t('Aog.Chr.txtRsltPckExpl') },
      formModelValue: '',
      formRole: 'textBox',
      span: 24,
      props: { disabled: true, size: 'large' },
    },
  });

export const getDialogInformationFormItems: () => CustomFormType['formItems'] =
  () => ({
    unitNm: {
      formModelValue: '',
      formRole: 'suffix',
    },
    makerLotNo: {
      formModelValue: '',
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Aog.Chr.txtBusinessPartnerLotNumber') },
      formRole: 'textBox',
      props: { size: 'small' },
      rules: [
        rules.required('textBox'),
        rules.length(64, t('Cm.Chr.txtLength', [64])),
        rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
      ],
    },
    pltModExpl: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Aog.Chr.txtComment') },
      formModelValue: '',
      rules: [
        rules.required('textComboBox'),
        rules.length(64, t('Cm.Chr.txtLength', [64])),
        rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
      ],
      formRole: 'textComboBox',
      props: {
        clearable: true,
        size: 'large',
      },
      selectOptions: [],
      cmbId: 'rsnCdAogMod',
    },
    ioaQty: {
      formModelValue: '',
      label: { text: t('Aog.Chr.txtAogPltIoaQty') },
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      suffix: { formModelProp: 'unitNm' },
    },
  });

// 個装一覧用テーブル設定
export const tablePropsData: TabulatorTableIF = {
  pageName: 'aogEditPaletteLoading',
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'baleNo', // 主キー。ユニークになるものを設定。
  height: '',
  showRadio: true, // ラジオボタンとして使用。
  column: [
    {
      title: 'Aog.Chr.txtBaleUnit',
      field: 'baleUnit',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.AOG.BALE_UNIT,
    },
    {
      title: 'Aog.Chr.txtMesUnit',
      field: 'unitNm',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
    {
      title: 'Aog.Chr.txtBaleCnt',
      field: 'baleCnt',
      formatter: 'number',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.AOG.BALE_CNT,
    },
    {
      title: 'Aog.Chr.txtComment',
      field: 'baleModExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    // レスポンスにユニークなデータが存在しないため、自前で隠しカラムでユニーク情報生成
    { title: '', field: 'baleNo', hidden: true },
    { title: '', field: 'modFlg', hidden: true },
  ],
  tableData: [],
  showConditionSearch: false,
  noUseConditionSearch: true, // ConditionSearch不要
};

export const searchInventoryListData = {
  aogInstNo: '',
  seqNo: '',
};

// 個装在庫修正ダイアログのモデル定義
export const dialogInformationFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getDialogInformationFormItems());

export default getDialogInformationInfoShowItems;

import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';

const { t } = i18n.global;

// 標準コンボボックスとカスタムフォームを紐づけるID
export const COMBINE_ID = {
  MOD_EXPL: 'prdConfWorkModExpl',
} as const;

// ダイアログのカスタムフォーム定義
export const getDialogFormItems: () => CustomFormType['formItems'] = () => ({
  // H01 人・清掃 時間
  h01Times: {
    formModelValue: '',
    label: { text: t('Prd.Chr.txtPeopleCleanTime') },
    formRole: 'textBox',
    props: { disabled: true },
    span: 12,
  },
  // 修正 H01 人・清掃 時間
  h01TimesModify: {
    formModelValue: '',
    label: { text: t('Prd.Chr.txtFixPeopleCleanTime') },
    rules: [
      rules.nonNegativeRealNumericOnly(),
      rules.placesOfNumeric({ int: 11, decimal: 12 }),
    ],
    formRole: 'textBox',
    span: 12,
  },
  // H02 人・稼働 時間
  h02Times: {
    formModelValue: '',
    label: { text: t('Prd.Chr.txtPeopleOperationTime') },
    formRole: 'textBox',
    props: { disabled: true },
    span: 12,
  },
  // 修正 H02 人・稼働 時間
  h02TimesModify: {
    formModelValue: '',
    label: { text: t('Prd.Chr.txtFixPeopleOperationTime') },
    rules: [
      rules.nonNegativeRealNumericOnly(),
      rules.placesOfNumeric({ int: 11, decimal: 12 }),
    ],
    formRole: 'textBox',
    span: 12,
  },
  // H03 人・切替準備 時間
  h03Times: {
    formModelValue: '',
    label: { text: t('Prd.Chr.txtPeopleSwitchTime') },
    formRole: 'textBox',
    props: { disabled: true },
    span: 12,
  },
  // 修正 H03 人・切替準備 時間
  h03TimesModify: {
    formModelValue: '',
    label: { text: t('Prd.Chr.txtFixPeopleSwitchTime') },
    rules: [
      rules.nonNegativeRealNumericOnly(),
      rules.placesOfNumeric({ int: 11, decimal: 12 }),
    ],
    formRole: 'textBox',
    span: 12,
  },
  // M01 機械・稼働 時間
  m01Times: {
    formModelValue: '',
    label: { text: t('Prd.Chr.txtMachineOperationTime') },
    formRole: 'textBox',
    props: { disabled: true },
    span: 12,
  },
  // 修正 M01 機械・稼働 時間
  m01TimesModify: {
    formModelValue: '',
    label: { text: t('Prd.Chr.txtFixMachineOperationTime') },
    rules: [
      rules.nonNegativeRealNumericOnly(),
      rules.placesOfNumeric({ int: 11, decimal: 12 }),
    ],
    formRole: 'textBox',
    span: 12,
  },
  // 修正コメント入力
  modExpl: {
    formModelValue: '',
    label: { text: t('Prd.Chr.txtModifyCommentInput') },
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    rules: [rules.required('textBox'), rules.length(64)],
    formRole: 'textComboBox',
    selectOptions: [],
    cmbId: COMBINE_ID.MOD_EXPL,
    span: 24,
  },
});

// ダイアログのカスタムフォームモデル定義
export const dialogFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getDialogFormItems());

<template>
  <!-- 出庫依頼明細追加・出庫依頼明細修正ダイアログ -->
  <DialogWindow
    :title="dialogConfigTitle"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="async () => checkAddAndEditForm()"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
    @visible="updateDialogChangeFlagRef"
  >
    <CustomForm
      :triggerRendering="customFormRenderingTriggerRef"
      :formModel="trfShipmentRequestAddAndEditFormRef.formModel"
      :formItems="trfShipmentRequestAddAndEditFormRef.formItems"
      @selectedItem="updateFormItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          trfShipmentRequestAddAndEditFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <MessageBox
    v-if="dialogVisibleRef.singleButton"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('singleButton')"
  />
  <!-- 出庫依頼明細追加前チェックのワーニングメッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.checkTrfShipmentRequestBeforeAddWarning"
    :dialogProps="messageBoxCheckTrfShipmentRequestBeforeAddPropsRef"
    :cancelCallback="
      () => closeDialog('checkTrfShipmentRequestBeforeAddWarning')
    "
    :submitCallback="warningConfirmClickHandler"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import CustomForm from '@/components/parts/CustomForm.vue';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { CustomOptionsData } from '@/components/model/common/TabulatorTable/TabulatorTable';
import CONST from '@/constants/utils';
import SCREENID from '@/constants/screenId';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import {
  useGetComboBoxDataStandard,
  useCheckTransferPlanBeforeAdd,
} from '@/hooks/useApi';
import {
  ComboBoxDataStandardReturnData,
  CommonRequestType,
  ExtendCommonRequestType,
} from '@/types/HookUseApi/CommonTypes';
import { DialogProps } from '@/types/MessageBoxTypes';
import { CustomFormType } from '@/types/CustomFormTypes';
import {
  SelectZoneGrpNoAndZoneNo,
  ShipmentRequestData,
  CheckTransferPlanBeforeAddReq,
} from '@/types/HookUseApi/TrfTypes';
import onValidateHandler from '@/utils/validateHandler';
import {
  getComboBoxOptionList,
  setCustomFormComboBoxOptionList,
  getComboBoxSelectOptionsLabel,
} from '@/utils/comboBoxOptionList';
import { closeLoading, showLoading } from '@/utils/dialog';
import { rules } from '@/utils/validator';
import createMessageBoxForm from '@/utils/commentMessageBox';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import {
  getTrfAddAndEditShipmentRequestFormItems,
  getTrfAddAndEditShipmentRequestFormModel,
  CondList,
} from './trfAddAndEditShipmentRequestDetails';

type Props = {
  isClicked: boolean;
  dspNarrowType: string;
  screenId: string;
  selectZoneData: SelectZoneGrpNoAndZoneNo;
  selectedRowData: ShipmentRequestData | null;
  selectedRowsData: CustomOptionsData[];
  privilegesBtnRequestData: CommonRequestType;
};
const { t } = useI18n();
const props = defineProps<Props>();
const emit = defineEmits(['submit']);

const trfShipmentRequestAddAndEditFormRef = ref<CustomFormType>({
  formItems: getTrfAddAndEditShipmentRequestFormItems(props.screenId),
  formModel: getTrfAddAndEditShipmentRequestFormModel(props.screenId),
});
const constantData = {
  matNo: 'matNo',
  lotNo: 'lotNo',
  edNo: 'edNo',
};
const dialogConfigTitle = ref<string>(t('Trf.Chr.txtAddShipmentRequest'));

let shipmentRequestData: ShipmentRequestData = {
  matNo: '',
  matNm: '',
  lotNo: '',
  lotSid: '',
  edNo: '',
  trfQty: '',
  invQty: '',
  invQtyOnSite: '',
  unitNm: '',
  detailExpl: '',
  cmtWarning: '',
  shipmentRequestDataID: '',
};

const customFormRenderingTriggerRef = ref(false);
type DialogRefKey =
  | 'singleButton'
  | 'fragmentDialogVisible'
  | 'checkTrfShipmentRequestBeforeAddWarning';

const initialState: InitialDialogState<DialogRefKey> = {
  singleButton: false,
  fragmentDialogVisible: false,
  checkTrfShipmentRequestBeforeAddWarning: false,
};
const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const messageBoxForm = createMessageBoxForm('message', 'trfPlanModWar');
let comboBoxDataStandardReturnData: ComboBoxDataStandardReturnData | undefined;
const messageBoxCheckTrfShipmentRequestBeforeAddPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isPrompt: true,
  formModel: messageBoxForm.formModel,
  formItems: messageBoxForm.formItems,
  type: 'warning',
});

/**
 * 明細重複チェック
 */
const checkDuplicationItem = (
  matNoVal: string,
  lotNoVal: string,
  edNoVal: string,
) => {
  if (props.selectedRowsData.length === 0) return false;
  let rowsData: CustomOptionsData[] = props.selectedRowsData;
  if (props.screenId === SCREENID.TRF_SHIPMENT_REQUEST_DETAILS_EDIT) {
    rowsData = rowsData.filter(
      (item) =>
        item.shipmentRequestDataID !==
        props.selectedRowData!.shipmentRequestDataID,
    );
  }
  const rowItem = rowsData.find(
    (v: CustomOptionsData) =>
      v.matNo === matNoVal && v.lotNo === lotNoVal && v.edNo === edNoVal,
  );
  if (rowItem) return true;
  return false;
};

/**
 * 出庫依頼明細追加
 */
const setTrfAddShipmentRequest = (messageBoxProps?: DialogProps) => {
  let { privilegesBtnRequestData } = props;
  if (props.screenId === SCREENID.TRF_SHIPMENT_REQUEST_DETAILS_ADD) {
    shipmentRequestData.matNo =
      trfShipmentRequestAddAndEditFormRef.value.formModel.matNo.toString();
    shipmentRequestData.matNm = getComboBoxSelectOptionsLabel(
      trfShipmentRequestAddAndEditFormRef.value.formModel.matNo.toString(),
      trfShipmentRequestAddAndEditFormRef.value.formItems.matNo,
    )!.toString();
    shipmentRequestData.unitNm =
      trfShipmentRequestAddAndEditFormRef.value.formModel.unitNm.toString();
  }
  shipmentRequestData.lotNo =
    trfShipmentRequestAddAndEditFormRef.value.formModel.lotNo.toString();
  shipmentRequestData.edNo =
    trfShipmentRequestAddAndEditFormRef.value.formModel.edNo.toString();
  shipmentRequestData.trfQty =
    trfShipmentRequestAddAndEditFormRef.value.formModel.trfQty.toString();
  shipmentRequestData.detailExpl =
    trfShipmentRequestAddAndEditFormRef.value.formModel.detailExpl.toString();
  shipmentRequestData.shipmentRequestDataID = `${trfShipmentRequestAddAndEditFormRef.value.formModel.matNo.toString()}${trfShipmentRequestAddAndEditFormRef.value.formModel.edNo.toString()}${trfShipmentRequestAddAndEditFormRef.value.formModel.lotNo.toString()}`;

  if (
    messageBoxProps &&
    'isPrompt' in messageBoxProps &&
    comboBoxDataStandardReturnData
  ) {
    shipmentRequestData.cmtWarning =
      messageBoxProps.formItems.message.formModelValue.toString();
    privilegesBtnRequestData = {
      ...props.privilegesBtnRequestData,
      msgboxTitleTxt: messageBoxProps.title,
      msgboxMsgTxt: messageBoxProps.content,
      msgboxBtnTxt: t('Cm.Chr.btnOk'),
      msgboxInputCmt: messageBoxProps.formModel.message.toString(),
    };
  }
  closeDialog('fragmentDialogVisible');
  emit('submit', shipmentRequestData, privilegesBtnRequestData);
};

/**
 * 確認メッセージ
 */
const checkTransferPlanBeforeAdd = async () => {
  showLoading();
  // ２．バックエンド側チェック
  // 出庫依頼明細作成可否チェック
  const apiRequestData: ExtendCommonRequestType<CheckTransferPlanBeforeAddReq> =
    {
      ...props.privilegesBtnRequestData,
      zoneGrpNo: props.selectZoneData.srcZoneGrpNo,
      matNo:
        trfShipmentRequestAddAndEditFormRef.value.formModel.matNo.toString(),
      lotNo:
        trfShipmentRequestAddAndEditFormRef.value.formModel.lotNo.toString(),
      edNo: trfShipmentRequestAddAndEditFormRef.value.formModel.edNo.toString(),
    };

  const { responseRef, errorRef } =
    await useCheckTransferPlanBeforeAdd(apiRequestData);
  if (errorRef.value) {
    if (errorRef.value.response.rCode === CONST.API_STATUS_CODE.COMMON_1001) {
      shipmentRequestData.lotSid = errorRef.value.response.rData.lotSid;
      shipmentRequestData.invQty = errorRef.value.response.rData.invQtyOnSite;
      messageBoxCheckTrfShipmentRequestBeforeAddPropsRef.value.title =
        errorRef.value.response.rTitle;
      messageBoxCheckTrfShipmentRequestBeforeAddPropsRef.value.content =
        errorRef.value.response.rMsg;
      const resetData = createMessageBoxForm('message', 'trfPlanModWar');
      if (
        'isPrompt' in
          messageBoxCheckTrfShipmentRequestBeforeAddPropsRef.value &&
        comboBoxDataStandardReturnData
      ) {
        messageBoxCheckTrfShipmentRequestBeforeAddPropsRef.value.formItems =
          resetData.formItems;
        setCustomFormComboBoxOptionList(
          messageBoxCheckTrfShipmentRequestBeforeAddPropsRef.value.formItems,
          comboBoxDataStandardReturnData.rData.rList,
        );
      }
      openDialog('checkTrfShipmentRequestBeforeAddWarning');
    } else {
      messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
      messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
      messageBoxSingleButtonRef.value.type = 'error';
      openDialog('singleButton');
    }
    closeLoading();
    return false;
  }
  if (responseRef.value) {
    shipmentRequestData.lotSid = responseRef.value.data.rData.lotSid;
    shipmentRequestData.invQty = responseRef.value.data.rData.invQtyOnSite;
    setTrfAddShipmentRequest();
  }
  closeLoading();
  return true;
};

const warningConfirmClickHandler = () => {
  closeDialog('checkTrfShipmentRequestBeforeAddWarning');
  setTrfAddShipmentRequest(
    messageBoxCheckTrfShipmentRequestBeforeAddPropsRef.value,
  );
};

/**
 * 出庫依頼明細追加の実行
 */

const checkAddAndEditForm = async () => {
  const validate =
    trfShipmentRequestAddAndEditFormRef.value.customForm !== undefined &&
    (await trfShipmentRequestAddAndEditFormRef.value.customForm.validate(
      (isValid) => {
        onValidateHandler(isValid);
      },
    ));
  if (validate) {
    if (
      checkDuplicationItem(
        trfShipmentRequestAddAndEditFormRef.value.formModel.matNo.toString(),
        trfShipmentRequestAddAndEditFormRef.value.formModel.lotNo.toString(),
        trfShipmentRequestAddAndEditFormRef.value.formModel.edNo.toString(),
      )
    ) {
      messageBoxSingleButtonRef.value.title = t(
        'Trf.Chr.txtDuplicationDetailsCheck',
      );
      messageBoxSingleButtonRef.value.content = t(
        'Trf.Msg.duplicationDetailsCheckError',
      );
      messageBoxSingleButtonRef.value.type = 'error';
      openDialog('singleButton');
      return false;
    }
    checkTransferPlanBeforeAdd();
  }
  return false;
};

/**
 * 指定アイテムの関連更新を行う
 */
const updateFormItems = (fieldId: string) => {
  // フォーカスアウト時の動作
  if (fieldId === constantData.matNo) {
    if (
      trfShipmentRequestAddAndEditFormRef.value.formItems.matNo.formRole ===
      'selectComboBox'
    ) {
      let overridePropsDisabled = true;
      let overrideUnitNm = '';
      if (
        trfShipmentRequestAddAndEditFormRef.value.formItems.matNo.selectOptions
          .length > 0 &&
        trfShipmentRequestAddAndEditFormRef.value.formItems.matNo
          .formModelValue !== ''
      ) {
        if (
          trfShipmentRequestAddAndEditFormRef.value.formItems.edNo.formRole ===
          'textBox'
        ) {
          const edMgtType = getComboBoxOptionList(
            trfShipmentRequestAddAndEditFormRef.value.formItems.matNo
              .selectOptions,
            trfShipmentRequestAddAndEditFormRef.value.formItems.matNo.formModelValue.toString(),
            'ed_mgt_type',
          )?.optionValList[0];
          if (edMgtType === '0') {
            // 版管理しない品目の場合、版番号をクリアして、非活性にする
            overridePropsDisabled = true;
            trfShipmentRequestAddAndEditFormRef.value.formItems.edNo.formModelValue =
              '';

            trfShipmentRequestAddAndEditFormRef.value.formItems.edNo.tags = [];
            trfShipmentRequestAddAndEditFormRef.value.formItems.edNo.rules = [];
            trfShipmentRequestAddAndEditFormRef.value.customForm?.clearValidate(
              'edNo',
            );
          } else {
            // 版管理する品目の場合、版番号を活性にする
            overridePropsDisabled = false;
            trfShipmentRequestAddAndEditFormRef.value.formItems.edNo.tags = [
              { text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' },
            ];
            trfShipmentRequestAddAndEditFormRef.value.formItems.edNo.rules = [
              rules.required('textBox'),
              rules.upperCaseSingleByteAlphanumeric(),
              rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
            ];
          }
          trfShipmentRequestAddAndEditFormRef.value.formItems.edNo.props!.disabled =
            overridePropsDisabled;
        }
        // 品目が選択済みの場合：選択した品目に該当する単位名を単位に表示する
        const mesUnitNm = getComboBoxOptionList(
          trfShipmentRequestAddAndEditFormRef.value.formItems.matNo
            .selectOptions,
          trfShipmentRequestAddAndEditFormRef.value.formModel.matNo.toString(),
          'mes_unit_nm',
        )?.optionValList[0];
        if (mesUnitNm) {
          overrideUnitNm = mesUnitNm.toString();
        }
      }
      trfShipmentRequestAddAndEditFormRef.value.formItems.unitNm.formModelValue =
        overrideUnitNm;
      customFormRenderingTriggerRef.value =
        !customFormRenderingTriggerRef.value;
    }
  }
};

/**
 * 初期設定
 */
const trfAddAndEditShipmentRequestDetailsInit = async () => {
  if (
    props.screenId === SCREENID.TRF_SHIPMENT_REQUEST_DETAILS_EDIT &&
    !props.selectedRowData
  )
    return;
  updateDialogChangeFlagRef(false);
  showLoading();
  // 連携テストダイアログの初期化必要なら書く
  trfShipmentRequestAddAndEditFormRef.value.formItems =
    getTrfAddAndEditShipmentRequestFormItems(props.screenId);

  let condList: CondList = [
    {
      cmbId: 'trfPlanMod',
      condKey: 'm_sys_cmt',
      where: { cmt_cat: 'TRF_PLANDTL_ADD' },
    },
    {
      cmbId: 'trfPlanModWar',
      condKey: 'm_sys_cmt',
      where: { cmt_cat: 'TRF_PLANDTL_WAR' },
    },
  ];
  if (props.screenId === SCREENID.TRF_SHIPMENT_REQUEST_DETAILS_ADD) {
    let dspNarrowTypeVal = 'M,P,N';

    if (props.dspNarrowType === 'M') {
      dspNarrowTypeVal = 'M,N';
    } else if (props.dspNarrowType === 'P') {
      dspNarrowTypeVal = 'P,N';
    }

    condList = [
      ...condList,
      {
        cmbId: 'matNo',
        condKey: 'm_mat',
        where: {
          dsp_narrow_type: dspNarrowTypeVal,
        },
        optionCol: { ed_mgt_type: '', mes_unit_nm: '' },
      },
    ];
  } else {
    dialogConfigTitle.value = t('Trf.Chr.txtEditShipmentRequest');
    shipmentRequestData = props.selectedRowData!;
    setFormModelValueFromApiResponse(
      trfShipmentRequestAddAndEditFormRef,
      shipmentRequestData,
    );
    if (
      trfShipmentRequestAddAndEditFormRef.value.formItems.edNo.formRole ===
        'textBox' &&
      !props.selectedRowData?.edNo
    ) {
      trfShipmentRequestAddAndEditFormRef.value.formItems.edNo.formModelValue =
        '';
      trfShipmentRequestAddAndEditFormRef.value.formItems.edNo.tags = [];
      trfShipmentRequestAddAndEditFormRef.value.formItems.edNo.rules = [];
      trfShipmentRequestAddAndEditFormRef.value.customForm?.clearValidate(
        'edNo',
      );
      trfShipmentRequestAddAndEditFormRef.value.formItems.edNo.props!.disabled =
        true;
    }
  }

  // 標準コンボボックスデータ取得
  comboBoxDataStandardReturnData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList,
  });
  if (
    comboBoxDataStandardReturnData &&
    comboBoxDataStandardReturnData.rData.rList.length > 0
  ) {
    setCustomFormComboBoxOptionList(
      trfShipmentRequestAddAndEditFormRef.value.formItems,
      comboBoxDataStandardReturnData.rData.rList,
    );
    if (
      'formItems' in messageBoxCheckTrfShipmentRequestBeforeAddPropsRef.value
    ) {
      // コメントメッセージボックス選択肢
      setCustomFormComboBoxOptionList(
        messageBoxCheckTrfShipmentRequestBeforeAddPropsRef.value.formItems,
        comboBoxDataStandardReturnData.rData.rList,
      );
    }
  }
  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(() => props.isClicked, trfAddAndEditShipmentRequestDetailsInit);
</script>

import i18n from '@/constants/lang';
import SCREENID from '@/constants/screenId';
import { ComboBoxDataOptionData } from '@/types/HookUseApi/CommonTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import { CustomFormType } from '@/types/CustomFormTypes';
import { ButtonExProps } from '@/types/ButtonExTypes';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import { GetManufacturingRecItemData } from '@/types/HookUseApi/SjgTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';
import CONST_FLAGS from '@/constants/flags';

const { t } = i18n.global;

type CallBackResult = Promise<boolean> | boolean | void;
type DialogButton = ButtonExProps & {
  clickHandler?: () => CallBackResult;
};

export type DialogConfig = {
  buttons?: DialogButton[];
  onResolve?: () => CallBackResult;
  onReject?: () => CallBackResult;
};

export const dialogCancelButton: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
  },
];

export const dialogExecutionButton: DialogWindowProps['buttons'] = [
  {
    text: t('Sjg.Chr.btnInspectionCompletion'),
    type: 'primary',
    size: 'normal',
    disabled: true,
  },
];

export type ManufacturingRecItemList = {
  manufacturingRecNm: string;
  manufacturingRecSts: string;
  btnExText: string;
};

export const getManufacturingRecOptions = (
  lotoutFlg: string,
): ComboBoxDataOptionData[] => {
  if (lotoutFlg) {
    if (lotoutFlg === CONST_FLAGS.SJG.LOT_OUT_STATUS.ONLY_LOT_OUT_ITEM) {
      // 未実施はロットアウト品の場合のみ表示される
      return [
        {
          value: 'FL',
          label: t('Cm.Chr.txtNG'),
          condKey: '',
          cmbId: 'cmtOdrRsltSts',
          optionList: [],
        },
        {
          value: 'NV',
          label: t('Sjg.Chr.txtVerifyStsInitial'),
          condKey: '',
          cmbId: 'cmtOdrRsltSts',
          optionList: [],
        },
      ];
    }
    //  OKはロットアウト品でない場合のみ表示される
    return [
      {
        value: 'PS',
        label: t('Cm.Chr.txtOK'),
        condKey: '',
        cmbId: 'cmtOdrRsltSts',
        optionList: [],
      },
      {
        value: 'FL',
        label: t('Cm.Chr.txtNG'),
        condKey: '',
        cmbId: 'cmtOdrRsltSts',
        optionList: [],
      },
    ];
  }
  return [
    {
      value: 'PS',
      label: t('Cm.Chr.txtOK'),
      condKey: '',
      cmbId: 'cmtOdrRsltSts',
      optionList: [],
    },
    {
      value: 'FL',
      label: t('Cm.Chr.txtNG'),
      condKey: '',
      cmbId: 'cmtOdrRsltSts',
      optionList: [],
    },
    {
      value: 'NV',
      label: t('Sjg.Chr.verifyOdrRsltNV'),
      condKey: '',
      cmbId: 'cmtOdrRsltSts',
      optionList: [],
    },
  ];
};

/**
 * 確認状態
 * 0：未確認
 * 1: 確認済
 */
const getVerifyConfirmFlgText = (manufacturingRecSts: string): string => {
  switch (manufacturingRecSts) {
    case '0':
      return t('Sjg.Chr.txtVerifyNotConfirm');
    case '1':
      return t('Sjg.Chr.txtVerifyConfirmed');
    default:
      return manufacturingRecSts;
  }
};

export const getManufacturingRecItemList = (
  manufacturingRecItem: GetManufacturingRecItemData,
): ManufacturingRecItemList[] => [
  {
    manufacturingRecNm: t('Sjg.Chr.txtOrderRecord'),
    manufacturingRecSts: manufacturingRecItem.odrRecFlg,
    btnExText: t('Sjg.Chr.btnOrderRecord'),
  },
  {
    manufacturingRecNm: t('Sjg.Chr.txtSopRecord'),
    manufacturingRecSts: manufacturingRecItem.odrSopFlg,
    btnExText: t('Sjg.Chr.btnSopRecord'),
  },
  {
    manufacturingRecNm: t('Sjg.Chr.txtOrderBomInfo'),
    manufacturingRecSts: manufacturingRecItem.odrBomFlg,
    btnExText: t('Sjg.Chr.btnOrderBomInfo'),
  },
];

export const getManufacturingRecInfoShowItems = (
  manufacturingRecItem: ManufacturingRecItemList,
): InfoShowType['infoShowItems'] => ({
  // 確認項目
  manufacturingRec: {
    label: { text: t('Sjg.Chr.txtConfirmItem') },
    infoShowModelValue: manufacturingRecItem.manufacturingRecNm,
    infoShowRole: 'text',
    span: 12,
  },
  // 確認状態
  manufacturingRecSts: {
    label: { text: t('Sjg.Chr.txtConfirmSts') },
    infoShowModelValue: getVerifyConfirmFlgText(
      manufacturingRecItem.manufacturingRecSts,
    ),
    infoShowRole: 'text',
    span: 12,
  },
});

export const getInspectManufacturingRecordInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    // 品目コード
    matNo: {
      label: { text: t('Sjg.Chr.txtMatNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
    // 品名
    matNm: {
      label: { text: t('Sjg.Chr.txtMatNm') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 18,
    },
    // 製造番号
    lotNo: {
      label: { text: t('Sjg.Chr.txtManageNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
    // ロットアウト品
    verifyReasonNm: {
      label: { text: t('Sjg.Chr.txtVerityReason') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
    // 使用期限
    expiryYmd: {
      label: { text: t('Sjg.Chr.txtExpiryYmd') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
    // 出来高日
    rsltYmd: {
      label: { text: t('Sjg.Chr.txtRsltYmd') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
  });

// 製造記録照査のアイテム定義
export const getInspectManufacturingRecordFormItems = (
  screenId: string,
): CustomFormType['formItems'] => {
  const formItems: CustomFormType['formItems'] = {
    odrRslt: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Sjg.Chr.txtVerifyOdrRslt') },
      formModelValue: '',
      rules: [rules.required('selectComboBox')],
      formRole: 'selectComboBox',
      props: {
        filterable: true,
        clearable: true,
        optionsData: [],
        disabled:
          screenId ===
          SCREENID.SJG_MANUFACTURING_RECORD_INSPECTION_CONFIRMATION,
      },
      selectOptions: [],
      cmbId: 'cmtOdrRsltSts',
    },
    rsltVerifyExpl: {
      label: { text: t('Sjg.Chr.txtVerifyExplComment') },
      formModelValue: '',
      rules: [rules.length(64, t('Cm.Chr.txtLength', [64]))],
      formRole: 'textComboBox',
      props: {
        clearable: true,
        size: 'custom',
        width: '100%',
        disabled:
          screenId ===
          SCREENID.SJG_MANUFACTURING_RECORD_INSPECTION_CONFIRMATION,
      },
      selectOptions: [],
      cmbId: 'cmtSjgVeriOdr',
    },
  };
  if (screenId === SCREENID.SJG_MANUFACTURING_RECORD_INSPECTION_CONFIRMATION) {
    formItems.odrRslt.tags = [];
    formItems.odrRslt.rules = [];
  }
  return formItems;
};

// 製造記録照査のモデル定義
export const getInspectManufacturingRecordFormModel = (
  screenId: string,
): CustomFormType['formModel'] =>
  createFormModelByFormItems(getInspectManufacturingRecordFormItems(screenId));

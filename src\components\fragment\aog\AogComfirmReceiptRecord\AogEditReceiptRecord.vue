<template>
  <!-- 受入実績修正 -->
  <DialogWindow
    :title="$t('Aog.Chr.txtAogEditReceiptRecord')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
    :onReject="commonRejectHandler"
    :onResolve="async () => checkForm()"
    @visible="updateDialogChangeFlagRef"
  >
    <BaseHeading
      level="2"
      fontSize="24px"
      class="Util_mb-16"
      :text="$t('Aog.Chr.txtAogReceiptRecordDetails')"
    />
    <div class="custom-form-container">
      <CustomForm
        :formModel="aogEditReceiptRecordFormRef.formModel"
        :formItems="aogEditReceiptRecordFormRef.formItems"
        @visible="
          (v: CustomFormType['customForm']) => {
            aogEditReceiptRecordFormRef.customForm = v;
          }
        "
        @changeFormModel="updateDialogChangeFlagRef"
      />
    </div>
    <BaseHeading
      level="2"
      fontSize="24px"
      class="Util_my-16"
      :text="$t('Aog.Chr.txtAogEditContentInput')"
    />
    <CustomForm
      :formModel="aogEditContentInputFormRef.formModel"
      :formItems="aogEditContentInputFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          aogEditContentInputFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
    <BaseHeading
      level="2"
      fontSize="24px"
      class="Util_my-16"
      :text="$t('Aog.Chr.txtAogRecord')"
    />
    <p>{{ $t('Aog.Chr.txtPltList') }}</p>
    <TabulatorTable :propsData="tablePropsDialogRef" />
  </DialogWindow>
  <!-- 受入実績修正の確認メッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.aogEditReceiptRecordConfirm"
    :dialogProps="messageBoxAogEditReceiptRecordConfirmProps"
    :cancelCallback="() => closeDialog('aogEditReceiptRecordConfirm')"
    :submitCallback="apiHandler"
  />
  <MessageBox
    v-if="dialogVisibleRef.singleButton"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('singleButton')"
  />
  <MessageBox
    v-if="dialogVisibleRef.aogEditReceiptRecordInfo"
    :dialogProps="messageBoxAogEditReceiptRecordPropsRef"
    :cancelCallback="closeAllDialog"
    :submitCallback="closeAllDialog"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { rules, check } from '@/utils/validator';
import onValidateHandler, {
  handleValidationBySingleRowSelect,
} from '@/utils/validateHandler';
import { isNullOrEmpty } from '@/utils/index';
import { CustomFormType } from '@/types/CustomFormTypes';
import {
  AogRsltFixListData,
  AogResultFixData,
  AddAttBinData,
  ModifyAogResultFixReq,
} from '@/types/HookUseApi/AogTypes';
import {
  CommonRequestType,
  ComboBoxDataStandardReturnData,
  ExtendCommonRequestType,
} from '@/types/HookUseApi/CommonTypes';
import CustomForm from '@/components/parts/CustomForm.vue';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import CONST_FLAGS from '@/constants/flags';
import {
  useGetComboBoxDataStandard,
  useGetAogResultFix,
  useModifyAogResultFix,
} from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import { getFilesForApiRequest, getRemovedFileKeys } from '@/utils/fileUpload';
import {
  getAogEditReceiptRecordFormItems,
  getAogEditContentInputFormItems,
  aogEditReceiptRecordFormModel,
  aogEditContentInputFormModel,
  tablePropsData,
} from './aogEditReceiptRecord';

const aogEditReceiptRecordFormRef = ref<CustomFormType>({
  formItems: getAogEditReceiptRecordFormItems(),
  formModel: aogEditReceiptRecordFormModel,
});

const aogEditContentInputFormRef = ref<CustomFormType>({
  formItems: getAogEditContentInputFormItems('0', '0'),
  formModel: aogEditContentInputFormModel,
});

type Props = {
  selectedRows: AogRsltFixListData[];
  isClicked: boolean;
  privilegesBtnRequestData: CommonRequestType;
};

const { t } = useI18n();
const props = defineProps<Props>();
const emit = defineEmits(['submit']);
let expiryCheck = false;
let shelfCheck = false;

let aogEditReceiptRecordFormData: AogResultFixData = {
  aogInstNo: '',
  aogInstGrpNo: '',
  aogYmd: '',
  lotNo: '',
  aogInstGrpPrtDts: '',
  poDtlNo: '',
  matNo: '',
  matNm: '',
  mBpId: '',
  bpNm: '',
  makerLotNo: '',
  edNo: '',
  rsltQty: '',
  unitNm: '',
  qltReqFlg: '',
  expiryDspCtrl: '',
  expiryDspTxt: '',
  expiryYmd: '',
  expiryStCd: '',
  shelfLifeDspCtrl: '',
  shelfLifeDspTxt: '',
  shelfLifeYmd: '',
  shelfLifeStCd: '',
  modExpl: '',
  aogAttBinList: [],
  aogRsltIoapList: [],
  aogRsltFixUpdDts: '',
};

type DialogRefKey =
  | 'singleButton'
  | 'messageBoxSingleButtonRef'
  | 'fragmentDialogVisible'
  | 'aogEditReceiptRecordConfirm'
  | 'aogEditReceiptRecordInfo';

const initialState: InitialDialogState<DialogRefKey> = {
  singleButton: false,
  messageBoxSingleButtonRef: false,
  fragmentDialogVisible: false,
  aogEditReceiptRecordConfirm: false,
  aogEditReceiptRecordInfo: false,
};
const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);
const tablePropsDialogRef = ref<TabulatorTableIF>({
  ...tablePropsData,
});
const aogInstNoRef = ref<string>();
const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const messageBoxAogEditReceiptRecordPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const messageBoxAogEditReceiptRecordConfirmProps: DialogProps = {
  title: t('Aog.Chr.txtAogEditReceiptRecord'),
  content: t('Aog.Msg.aogEditReceiptRecordConfirm'),
  type: 'question',
};

let comboBoxDataStandardReturnData: ComboBoxDataStandardReturnData | undefined;

const FILE_AOG_ATT_BIN = {
  MODEL_KEY: 'aogAttBinList',
  NAME_KEY: 'attBinFileNm',
  FILE_KEY: 'attBinFile',
  UNIQUE_KEY: 'attBinNo',
} as const;

const checkForm = async () => {
  const validate =
    aogEditContentInputFormRef.value.customForm !== undefined &&
    (await aogEditContentInputFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));
  if (validate) {
    if (expiryCheck === true && shelfCheck === true) {
      if (
        check.fromToDate(
          aogEditContentInputFormRef.value.formModel.shelfLifeYmd.toString(),
          aogEditContentInputFormRef.value.formModel.expiryYmd.toString(),
        )
      ) {
        messageBoxSingleButtonRef.value.title = t('Tst.Msg.txtChecksumExpiry');
        messageBoxSingleButtonRef.value.content = t(
          'Tst.Msg.txtExpiryMustBeInTheFuture',
        );
        messageBoxSingleButtonRef.value.type = 'error';
        openDialog('singleButton');
        return false;
      }
    }
    openDialog('aogEditReceiptRecordConfirm');
  }
  return false;
};

/**
 * 確認メッセージ
 */
const apiHandler = async () => {
  closeDialog('aogEditReceiptRecordConfirm');
  showLoading();
  const addFileList = await getFilesForApiRequest<AddAttBinData>(
    aogEditContentInputFormRef.value.formModel[FILE_AOG_ATT_BIN.MODEL_KEY],
    {
      fileNameKey: FILE_AOG_ATT_BIN.NAME_KEY,
      fileKey: FILE_AOG_ATT_BIN.FILE_KEY,
    },
  );
  const modifyAogResultFixFormModel: ExtendCommonRequestType<ModifyAogResultFixReq> =
    {
      ...props.privilegesBtnRequestData,
      msgboxTitleTxt: messageBoxAogEditReceiptRecordConfirmProps.title,
      msgboxMsgTxt: messageBoxAogEditReceiptRecordConfirmProps.content,
      msgboxBtnTxt: t('Cm.Chr.btnOk'),
      aogInstNo:
        aogEditReceiptRecordFormRef.value.formModel.aogInstNo.toString(),
      rsltQty: aogEditContentInputFormRef.value.formModel.rsltQty.toString(),
      qltReqFlg: aogEditReceiptRecordFormData.qltReqFlg,
      expiryStCd: aogEditReceiptRecordFormData.expiryStCd,
      expiryYmd: isNullOrEmpty(
        aogEditContentInputFormRef.value.formModel.expiryYmd,
      )
        ? ''
        : aogEditContentInputFormRef.value.formModel.expiryYmd.toString(),
      shelfLifeStCd: aogEditReceiptRecordFormData.shelfLifeStCd,
      shelfLifeYmd: isNullOrEmpty(
        aogEditContentInputFormRef.value.formModel.shelfLifeYmd,
      )
        ? ''
        : aogEditContentInputFormRef.value.formModel.shelfLifeYmd.toString(),
      modExpl: aogEditContentInputFormRef.value.formModel.modExpl.toString(),
      addFileList,
      delFileList: getRemovedFileKeys(
        aogEditContentInputFormRef.value.formModel[FILE_AOG_ATT_BIN.MODEL_KEY],
        {
          initialFileList: aogEditReceiptRecordFormData.aogAttBinList,
          fileKeyPropName: FILE_AOG_ATT_BIN.UNIQUE_KEY,
        },
      ),
      aogRsltFixUpdDts: aogEditReceiptRecordFormData.aogRsltFixUpdDts,
    };

  const { responseRef, errorRef } = await useModifyAogResultFix(
    modifyAogResultFixFormModel,
  );
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return false;
  }
  if (responseRef.value) {
    aogInstNoRef.value = responseRef.value.data.rData.aogInstNo;
    messageBoxAogEditReceiptRecordPropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxAogEditReceiptRecordPropsRef.value.content =
      responseRef.value.data.rMsg;
    openDialog('aogEditReceiptRecordInfo');
  }
  closeLoading();
  return true;
};

const closeAllDialog = () => {
  closeDialog('aogEditReceiptRecordInfo');
  closeDialog('fragmentDialogVisible');
  emit('submit', props.privilegesBtnRequestData, aogInstNoRef.value);
};

/**
 * 初期設定
 */
const aogEditReceiptRecordInit = async () => {
  if (!props.selectedRows) return;

  // 単一選択されていなかった場合はエラー表示とする
  try {
    await handleValidationBySingleRowSelect(props.selectedRows);
  } catch (error) {
    return;
  }
  expiryCheck = false;
  shelfCheck = false;
  // NOTE:単一チェック済みなので確実に単一行。先頭取得する
  const selectedRow = props.selectedRows.at(0)!;

  updateDialogChangeFlagRef(false);
  showLoading();
  aogEditReceiptRecordFormRef.value.formItems =
    getAogEditReceiptRecordFormItems();
  // 受入実績修正データ取得
  const { responseRef, errorRef } = await useGetAogResultFix({
    ...props.privilegesBtnRequestData,
    aogInstNo: selectedRow.aogInstNo,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return;
  }
  if (responseRef.value) {
    tablePropsDialogRef.value.tableData =
      responseRef.value.data.rData.aogRsltIoapList;
    aogEditReceiptRecordFormData = responseRef.value.data.rData;

    aogEditContentInputFormRef.value.formItems =
      getAogEditContentInputFormItems(
        aogEditReceiptRecordFormData.expiryDspCtrl,
        aogEditReceiptRecordFormData.shelfLifeDspCtrl,
      );
    setFormModelValueFromApiResponse(
      aogEditReceiptRecordFormRef,
      aogEditReceiptRecordFormData,
    );
    const filterAogEditReceiptRecordFormData = Object.fromEntries(
      Object.entries(aogEditReceiptRecordFormData).filter(
        ([key]) =>
          (key !== 'expiryYmd' && key !== 'shelfLifeYmd') ||
          (key === 'expiryYmd' &&
            aogEditReceiptRecordFormData.expiryDspCtrl !== '0') ||
          (key === 'shelfLifeYmd' &&
            aogEditReceiptRecordFormData.shelfLifeDspCtrl !== '0'),
      ),
    );
    setFormModelValueFromApiResponse(
      aogEditContentInputFormRef,
      filterAogEditReceiptRecordFormData,
      {
        fileKeys: [
          {
            formModelKey: FILE_AOG_ATT_BIN.MODEL_KEY,
            fileNameKey: FILE_AOG_ATT_BIN.NAME_KEY,
            fileKeyPropName: FILE_AOG_ATT_BIN.UNIQUE_KEY,
          },
        ],
        commonRequestData: props.privilegesBtnRequestData,
      },
    );

    Object.entries(aogEditContentInputFormRef.value.formItems).forEach(
      ([key, v]) => {
        const formItem = v;

        if (
          key === 'expiryYmd' &&
          aogEditReceiptRecordFormData.expiryDspCtrl !== '0'
        ) {
          formItem.formModelValue = aogEditReceiptRecordFormData.expiryYmd;
          if (formItem.label) {
            formItem.label.text = aogEditReceiptRecordFormData.expiryDspTxt;
          }
          if ('props' in formItem) {
            // 1:ラベル → タイトルラベル：XX期限表示テキストを表示
            // 入力領域：非活性のテキストボックスにXX期限を表示
            if (aogEditReceiptRecordFormData.expiryDspCtrl === '1') {
              formItem.tags = [];
              formItem.rules = [];
              formItem.formRole = 'textBox';
              formItem.props = {
                disabled: true,
              };
            }
            // 2:カレンダー → タイトルラベル：XX期限表示テキストを表示
            // 入力領域：カレンダーコントロールにXX期限を表示
            if (aogEditReceiptRecordFormData.expiryDspCtrl === '2') {
              formItem.tags = [
                { text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' },
              ];
              formItem.rules = [rules.required('selectComboBox')];
              formItem.formRole = 'date';
              formItem.props = {
                type: 'date',
                modelValue: '',
              };
              formItem.props.disabled = false;
              if (
                aogEditReceiptRecordFormData.expiryStCd ===
                  CONST_FLAGS.MAT_EXPIRY_ST_CD_STATUS.EXPIRE_INPUT &&
                aogEditReceiptRecordFormData.qltReqFlg ===
                  CONST_FLAGS.QLT_REQ_FLG.ON
              ) {
                expiryCheck = true;
                formItem.rules.push(rules.futureDate());
              }
            }
          }
        }
        if (
          key === 'shelfLifeYmd' &&
          aogEditReceiptRecordFormData.shelfLifeDspCtrl !== '0'
        ) {
          formItem.formModelValue = aogEditReceiptRecordFormData.shelfLifeYmd;
          if (formItem.label) {
            formItem.label.text = aogEditReceiptRecordFormData.shelfLifeDspTxt;
          }
          if ('props' in formItem) {
            // 1:ラベル → タイトルラベル：XX期限表示テキストを表示
            // 入力領域：非活性のテキストボックスにXX期限を表示
            if (aogEditReceiptRecordFormData.shelfLifeDspCtrl === '1') {
              formItem.tags = [];
              formItem.rules = [];
              formItem.formRole = 'textBox';
              formItem.props = {
                disabled: true,
              };
            }
            // 2:カレンダー → タイトルラベル：XX期限表示テキストを表示
            // 入力領域：カレンダーコントロールにXX期限を表示
            if (aogEditReceiptRecordFormData.shelfLifeDspCtrl === '2') {
              formItem.tags = [
                { text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' },
              ];
              formItem.rules = [rules.required('selectComboBox')];
              formItem.formRole = 'date';
              formItem.props = {
                type: 'date',
                modelValue: '',
              };
              if (
                aogEditReceiptRecordFormData.shelfLifeStCd ===
                  CONST_FLAGS.MAT_SHELF_LIFE_ST_CD_STATUS.EXPIRE_INPUT &&
                aogEditReceiptRecordFormData.qltReqFlg ===
                  CONST_FLAGS.QLT_REQ_FLG.ON
              ) {
                shelfCheck = true;
                formItem.rules.push(rules.futureDate());
              }
            }
          }
        }
      },
    );
  }
  // 標準コンボボックスデータ取得
  comboBoxDataStandardReturnData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'cmtAogRsltMod',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'AOG_RSLT_MOD' },
      },
    ],
  });
  if (
    comboBoxDataStandardReturnData &&
    comboBoxDataStandardReturnData.rData.rList.length > 0
  ) {
    setCustomFormComboBoxOptionList(
      aogEditReceiptRecordFormRef.value.formItems,
      comboBoxDataStandardReturnData.rData.rList,
    );
    setCustomFormComboBoxOptionList(
      aogEditContentInputFormRef.value.formItems,
      comboBoxDataStandardReturnData.rData.rList,
    );
  }
  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(() => props.isClicked, aogEditReceiptRecordInit);
</script>
<style lang="scss" scoped>
.custom-form-container {
  height: 148px;
  overflow-y: auto;
}
</style>

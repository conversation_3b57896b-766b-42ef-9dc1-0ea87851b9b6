<template>
  <!-- 出来高修正ダイアログ -->
  <!-- 見出し 出来高修正 -->
  <DialogWindow
    :title="$t('Prd.Chr.txtProductionModify')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :class="'prd-product-modify'"
    :onReject="commonRejectHandler"
    :onResolve="resolveClickHandler"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <CustomForm
      :formModel="prdProductModifyFormRef.formModel"
      :formItems="prdProductModifyFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          prdProductModifyFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 出来高修正確定完了の情報表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxPrdProductModifyFinishedVisible"
    :dialogProps="messageBoxPrdProductModifyFinishedPropsRef"
    :submitCallback="closeDialogFromMessageBoxModifyPrdProductModifyFinished"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import onValidateHandler from '@/utils/validateHandler';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { closeLoading, showLoading } from '@/utils/dialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import CustomForm from '@/components/parts/CustomForm.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import {
  useGetConfirmProductModifyInit,
  useModifyConfirmProductModify,
  useGetComboBoxDataStandard,
} from '@/hooks/useApi';
import {
  GetConfirmProductInfoListInitResponseData,
  GetConfirmProductModifyInitRequestData,
  GetConfirmProductModifyInitResponseData,
  ModifyConfirmProductModifyRequestData,
} from '@/types/HookUseApi/PrdTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import {
  getPrdProductModifyFormItems,
  prdProductModifyFormModel,
  COMBINE_ID,
} from './prdProductModify';

// 出来高修正初期表示APIのレスポンス
let initResponseData: GetConfirmProductModifyInitResponseData = {
  prdMatNo: '',
  dspNmJp: '',
  batchNo: null,
  lotSid: '',
  lotNo: '',
  lblSid: '',
  unitNmJp: '',
  rsltQty: '',
  updDts: '',
};

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxPrdProductModifyFinishedVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxPrdProductModifyFinishedVisible: false,
};

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 出来高修正確定の完了メッセージボックス
const messageBoxPrdProductModifyFinishedPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'warning',
});

const prdProductModifyFormRef = ref<CustomFormType>({
  formItems: getPrdProductModifyFormItems(),
  formModel: prdProductModifyFormModel,
});

// 親ダイアログから渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  odrNo?: string; // 親ダイアログのodrNo
  prcSeq?: number; // 親ダイアログのprcSeq
  beforeSelectedRow: GetConfirmProductInfoListInitResponseData | null; // 製造記録確認_出来高記録詳細 選択行情報
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();
const emit = defineEmits(['submit']);

// 出来高修正確定のAPIリクエスト処理
const requestApiModifyConfirmProductModify = async () => {
  if (
    props.odrNo === undefined ||
    props.prcSeq === undefined ||
    props.beforeSelectedRow === null ||
    props.beforeSelectedRow.batchNo === null ||
    props.beforeSelectedRow.rsltLnum === null
  ) {
    return;
  }

  showLoading();

  // 以下のチェックはBEで行う。FEとしてはAPIをリクエストする。
  // 3.下記チェックを行い、チェックOKなら処理継続する。
  const requestData: ModifyConfirmProductModifyRequestData = {
    odrNo: props.odrNo,
    prcSeq: props.prcSeq,
    prdMatNo: props.beforeSelectedRow.prdMatNo,
    lotSid: props.beforeSelectedRow.lotSid,
    batchNo: props.beforeSelectedRow.batchNo,
    rsltLnum: props.beforeSelectedRow.rsltLnum,
    lblSid:
      prdProductModifyFormRef.value.formItems.lblSid.formModelValue.toString(),
    updDts: initResponseData.updDts,
    beforeRsltQty:
      prdProductModifyFormRef.value.formItems.rsltQty.formModelValue.toString(),
    rsltQty:
      prdProductModifyFormRef.value.formItems.updRsltQty.formModelValue.toString(),
    modExpl:
      prdProductModifyFormRef.value.formItems.modExpl.formModelValue.toString(),
  };

  const { responseRef, errorRef } = await useModifyConfirmProductModify({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return;
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }

  // 4．出来高修正確定
  // ・データベースを更新した後、以下のメッセージを表示する。
  // 出来高修正確定完了
  messageBoxPrdProductModifyFinishedPropsRef.value.title =
    responseRef.value.data.rTitle;
  messageBoxPrdProductModifyFinishedPropsRef.value.content =
    responseRef.value.data.rMsg;

  closeLoading();

  openDialog('messageBoxPrdProductModifyFinishedVisible');
};

// 出来高修正確定完了に紐づくダイアログ閉じる処理
const closeDialogFromMessageBoxModifyPrdProductModifyFinished = () => {
  // 再検索用に親に実行を通知
  emit('submit');

  // 5.ダイアログウィンドウを閉じる
  closeDialog('messageBoxPrdProductModifyFinishedVisible');
  closeDialog('fragmentDialogVisible');
};

// 自身の 実行 押下時処理
const resolveClickHandler = async () => {
  // 1.下記チェックを行い、チェックOKなら処理継続する。
  // １ 各コントロールのチェック内容をチェックする（NG時：動作は共通仕様参照）
  const validate =
    prdProductModifyFormRef.value.customForm !== undefined &&
    (await prdProductModifyFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));

  if (!validate) {
    return false;
  }

  // 出来高修正確定のAPIリクエスト処理
  requestApiModifyConfirmProductModify();

  return false;
};

/**
 * 出来高修正ダイアログの初期設定
 */
const prdProductModifyInit = async () => {
  if (
    props.odrNo === undefined ||
    props.prcSeq === undefined ||
    props.beforeSelectedRow === null ||
    props.beforeSelectedRow.batchNo === null ||
    props.beforeSelectedRow.rsltLnum === null
  ) {
    return;
  }

  // ダイアログ終了チェック初期化
  updateDialogChangeFlagRef(false);

  // FormItems初期化
  prdProductModifyFormRef.value.formItems = getPrdProductModifyFormItems();

  showLoading();

  // 出来高修正初期表示のAPIを行う。
  const requestData: GetConfirmProductModifyInitRequestData = {
    odrNo: props.odrNo,
    prcSeq: props.prcSeq,
    batchNo: props.beforeSelectedRow.batchNo,
    prdMatNo: props.beforeSelectedRow.prdMatNo,
    rsltLnum: props.beforeSelectedRow.rsltLnum,
    lotSid: props.beforeSelectedRow.lotSid,
    lblSid: props.beforeSelectedRow.lblSid,
  };
  const { responseRef, errorRef } = await useGetConfirmProductModifyInit({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }

  // 出来高修正初期表示のAPIのレスポンスを保持
  initResponseData = responseRef.value.data.rData;

  // 出来高修正情報レイアウト用初期値設定
  setFormModelValueFromApiResponse(
    prdProductModifyFormRef,
    responseRef.value.data.rData,
  );

  // 標準コンボボックス取得
  const comboBoxResData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        // 修正コメント入力
        cmbId: COMBINE_ID.MOD_EXPL,
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'PRD_SOP_PRD_MOD' }, // 製造記録の出来高修正
      },
    ],
  });

  if (comboBoxResData) {
    setCustomFormComboBoxOptionList(
      prdProductModifyFormRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }

  closeLoading();

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await prdProductModifyInit();
  },
);
</script>

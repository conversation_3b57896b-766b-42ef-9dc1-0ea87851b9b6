<template>
  <!-- 照査結果確認 -->
  <DialogWindow
    :title="$t('Sjg.Chr.txtSjgConfirmInspectionResults')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    @closeDialog="() => closeAllDialog()"
    :buttons="[
      {
        text: $t('Cm.Chr.btnCancel'),
        type: 'secondary',
        size: 'normal',
        clickHandler: () => closeDialog('fragmentDialogVisible'),
      },
      {
        text: $t('Sjg.Chr.btnSjgConfirm'),
        type: 'primary',
        size: 'normal',
        clickHandler: () => modReleaseFinForm(),
      },
    ]"
    width="1400"
  >
    <!-- 出荷判定情報 -->
    <BaseHeading
      level="2"
      fontSize="24px"
      :text="$t('Sjg.Chr.txtSjgInformation')"
      class="Util_mb-16"
    />
    <InfoShow
      :infoShowItems="sjgConfirmInspectionResultsInfoShowRef.infoShowItems"
      :isLabelVertical="sjgConfirmInspectionResultsInfoShowRef.isLabelVertical"
      fontSizeLabel="12px"
      fontSizeContent="16px"
    />
    <!-- 製造記録照査・品質記録照査結果 -->
    <BaseHeading
      level="2"
      fontSize="24px"
      class="Util_my-16"
      :text="$t('Sjg.Chr.txtManufacturingAndQualityRecordResult')"
    />
    <TabulatorTable :propsData="tablePropsData" />
    <CustomForm
      :triggerRendering="customFormRenderingTriggerRef"
      :formModel="sjgConfirmInspectionFormRef.formModel"
      :formItems="sjgConfirmInspectionFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          sjgConfirmInspectionFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
    <!-- GMP確認結果 -->
    <BaseHeading
      level="2"
      fontSize="24px"
      :text="$t('Sjg.Chr.txtGMPInformationResult')"
      class="Util_mt-24"
    />
    <TabulatorTable
      :propsData="tablePropsDataGmpContent"
      @clickBtnColumn="handleBtnClick"
    />
    <div class="custom-Form-container">
      <CustomForm
        :triggerRendering="customFormRenderingTriggerRef1"
        :formModel="sjgConfirmInspectionFormRef1.formModel"
        :formItems="sjgConfirmInspectionFormRef1.formItems"
        @visible="
          (v: CustomFormType['customForm']) => {
            sjgConfirmInspectionFormRef1.customForm = v;
          }
        "
        @changeFormModel="updateDialogChangeFlagRef"
      />
      <BaseHeading
        level="2"
        fontSize="24px"
        :text="$t('Sjg.Chr.txtReleaseRsltM')"
        class="Util_mt-24"
      />
      <CustomForm
        class="Util_mt-24"
        :triggerRendering="customFormRenderingTriggerRef2"
        :formModel="sjgConfirmInspectionFormRef2.formModel"
        :formItems="sjgConfirmInspectionFormRef2.formItems"
        @visible="
          (v: CustomFormType['customForm']) => {
            sjgConfirmInspectionFormRef2.customForm = v;
          }
        "
        @selectedItem="updateFormItems"
        @changeFormModel="updateDialogChangeFlagRef"
      />
      <div
        v-if="
          releaseJudgeRouteType === CONST_FLAGS.SJG.RELEASE_JUDGEMENT_ROUTE.GQP
        "
      >
        <BaseHeading
          level="2"
          fontSize="24px"
          :text="$t('Sjg.Chr.txtReleaseRsltQ')"
          class="Util_mt-24"
        />
        <CustomForm
          class="Util_mt-24"
          :triggerRendering="customFormRenderingTriggerRef3"
          :formModel="sjgConfirmInspectionFormRef3.formModel"
          :formItems="sjgConfirmInspectionFormRef3.formItems"
          @visible="
            (v: CustomFormType['customForm']) => {
              sjgConfirmInspectionFormRef3.customForm = v;
            }
          "
          @selectedItem="updateFormItems"
          @changeFormModel="updateDialogChangeFlagRef"
        />
      </div>
      <CustomForm
        class="Util_mt-24"
        :triggerRendering="customFormRenderingTriggerRef4"
        :formModel="sjgConfirmInspectionFormRef4.formModel"
        :formItems="sjgConfirmInspectionFormRef4.formItems"
        @visible="
          (v: CustomFormType['customForm']) => {
            sjgConfirmInspectionFormRef4.customForm = v;
          }
        "
        @selectedItem="updateFormItems"
        @changeFormModel="updateDialogChangeFlagRef"
      />
    </div>
    <MessageBox
      v-if="dialogVisibleRef.gmpDocNeedTypeVerifyChg"
      :dialogProps="messageBoxGmpDocNeedTypeVerifyChgConfirm"
      :cancelCallback="() => closeDialog('gmpDocNeedTypeVerifyChg')"
      :submitCallback="
        () => {
          updateEmitSelectedRow(currentRowData);
          closeDialog('gmpDocNeedTypeVerifyChg');
        }
      "
    />
    <!-- メッセージの内容は以下の通り -->
    <MessageBox
      v-if="dialogVisibleRef.sjgShipmentDecisionConfirm"
      :dialogProps="messageBoxsjgShipmentDecisionConfirm"
      :cancelCallback="() => closeDialog('sjgShipmentDecisionConfirm')"
      :submitCallback="() => modShowSignDialog()"
    />
    <!-- 出荷可否判定結果チェック -->
    <MessageBox
      v-if="dialogVisibleRef.shipmentDecisionResultCheck"
      :dialogProps="messageBoxshipmentDecisionResultCheckError"
      :submitCallback="() => closeDialog('shipmentDecisionResultCheck')"
    />
  </DialogWindow>
  <!-- 異常 -->
  <MessageBox
    v-if="dialogVisibleRef.singleButton"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('singleButton')"
  />
  <!-- 強制終了 一覧画面を表示する -->
  <MessageBox
    v-if="dialogVisibleRef.forcedTermination"
    :dialogProps="messageBoxForcedTerminationRef"
    :submitCallback="
      () => {
        closeDialog('forcedTermination');
        closeAllDialog();
      }
    "
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { rules } from '@/utils/validator';
import {
  CheckBeforeReleaseReq,
  GetSjgListData,
  VerifyRsltList,
  ModReleaseFinDataReq,
  GmpInfoListConfData,
  GmpDocNeedTypeNmListData,
} from '@/types/HookUseApi/SjgTypes';
import {
  CommonRequestType,
  ExtendCommonRequestType,
} from '@/types/HookUseApi/CommonTypes';
import BaseHeading from '@/components/base/BaseHeading.vue';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import {
  useGetVerifyResult,
  useCheckBeforeRelease,
  useGetComboBoxDataStandard,
  useModifyReleaseFin,
  useCheckVerifyRsltRelease,
} from '@/hooks/useApi';
import {
  setCustomFormComboBoxOptionList,
  getComboBoxSelectOptionsLabel,
} from '@/utils/comboBoxOptionList';
import { closeLoading, showLoading } from '@/utils/dialog';
import showSignDialog from '@/components/model/SignDialog/SignDialog';
import useFileDownload from '@/hooks/useApi/fileDownLoad';
import onValidateHandler from '@/utils/validateHandler';
import { InfoShowType } from '@/types/InfoShowTypes';
import { CustomFormType } from '@/types/CustomFormTypes';
import InfoShow from '@/components/parts/InfoShow.vue';
import CONST_FLAGS from '@/constants/flags';
import { v4 as uuidv4 } from 'uuid';
import {
  getSjgConfirmInspectionResultsInfoShowItems,
  tablePropsData,
  tablePropsDataGmpContent,
  sjgConfirmInspectionFormModel,
  getSjgConfirmInspectionFormItems,
  sjgConfirmInspectionFormModel1,
  getSjgConfirmInspectionFormItems1,
  sjgConfirmInspectionFormModel2,
  getSjgConfirmInspectionFormItems2,
  sjgConfirmInspectionFormModel3,
  getSjgConfirmInspectionFormItems3,
  sjgConfirmInspectionFormModel4,
  getSjgConfirmInspectionFormItems4,
} from './sjgConfirmInspectionResults';

/**
 * 多言語
 */

const sjgConfirmInspectionResultsInfoShowRef = ref<InfoShowType>({
  infoShowItems: getSjgConfirmInspectionResultsInfoShowItems(),
  isLabelVertical: true,
});

const sjgConfirmInspectionFormRef = ref<CustomFormType>({
  formItems: getSjgConfirmInspectionFormItems(),
  formModel: sjgConfirmInspectionFormModel,
});

const sjgConfirmInspectionFormRef1 = ref<CustomFormType>({
  formItems: getSjgConfirmInspectionFormItems1(),
  formModel: sjgConfirmInspectionFormModel1,
});

const sjgConfirmInspectionFormRef2 = ref<CustomFormType>({
  formItems: getSjgConfirmInspectionFormItems2(),
  formModel: sjgConfirmInspectionFormModel2,
});

const sjgConfirmInspectionFormRef3 = ref<CustomFormType>({
  formItems: getSjgConfirmInspectionFormItems3(),
  formModel: sjgConfirmInspectionFormModel3,
});

const sjgConfirmInspectionFormRef4 = ref<CustomFormType>({
  formItems: getSjgConfirmInspectionFormItems4(),
  formModel: sjgConfirmInspectionFormModel4,
});

const customFormRenderingTriggerRef = ref(false);
const customFormRenderingTriggerRef1 = ref(false);
const customFormRenderingTriggerRef2 = ref(false);
const customFormRenderingTriggerRef3 = ref(false);
const customFormRenderingTriggerRef4 = ref(false);

const TRANSITION_FLAG = {
  NO_TRANSITION: '0', // 遷移なし
  TRANSITION: '1', // 遷移あり
} as const;

type Props = {
  selectedRow: GetSjgListData | null;
  isClicked: boolean;
  privilegesBtnRequestData: CommonRequestType;
};

const { t } = useI18n();
const props = defineProps<Props>();
const emit = defineEmits(['submit']);

type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'singleButton'
  | 'sjgShipmentDecisionConfirm'
  | 'shipmentDecisionResultCheck'
  | 'gmpDocNeedTypeVerifyChg'
  | 'forcedTermination';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  singleButton: false,
  sjgShipmentDecisionConfirm: false,
  shipmentDecisionResultCheck: false,
  gmpDocNeedTypeVerifyChg: false,
  forcedTermination: false,
};
const { dialogVisibleRef, openDialog, closeDialog, updateDialogChangeFlagRef } =
  useDialog(initialState);

let verifyRsltListData: VerifyRsltList[] = [];
let releaseJudgeRouteType: string = '';
let gmpDocNeedTypeNmList: GmpDocNeedTypeNmListData[] = [];

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});
// メッセージの内容は以下の通り
const messageBoxGmpDocNeedTypeVerifyChgConfirm: DialogProps = {
  title: t('Sjg.Msg.gmpDocNeedTypeVerifyChg'),
  content: t('Sjg.Msg.gmpDocNeedTypeVerifyChgMessage'),
  type: 'question',
};
// メッセージの内容は以下の通り
const messageBoxsjgShipmentDecisionConfirm: DialogProps = {
  title: t('Sjg.Chr.btnSjgConfirm'),
  content: t('Sjg.Msg.contentSjgConfirm'),
  type: 'question',
};
// 出荷可否判定結果チェック
const messageBoxshipmentDecisionResultCheckError: DialogProps = {
  title: t('Sjg.Msg.shipmentDecisionResultCheckError'),
  content: t('Sjg.Msg.shipmentDecisionResultCheckErrorMessage'),
  isSingleBtn: true,
  type: 'error',
};

const messageBoxForcedTerminationRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});

const constantData = {
  releaseRsltQ: 'releaseRsltQ',
  releaseRsltM: 'releaseRsltM',
};

type ExtendGmpInfoListConfData = GmpInfoListConfData & {
  gmpContentUniqueKey: string; // ユニークキーを追加
};

let currentRowData: ExtendGmpInfoListConfData | null = null;
const handleBtnClick = (rowData: ExtendGmpInfoListConfData) => {
  currentRowData = rowData;
  openDialog('gmpDocNeedTypeVerifyChg');
};

const updateEmitSelectedRow = (rowData: ExtendGmpInfoListConfData | null) => {
  if (!rowData) return;

  const newTableData = tablePropsDataGmpContent.value.tableData.map((item) => {
    if (item.gmpContentUniqueKey !== rowData.gmpContentUniqueKey) return item;
    const oppositeGmpDocNeedType = gmpDocNeedTypeNmList.find(
      (v) => v.cdVal !== item.gmpDocNeedTypeRelease,
    );
    return {
      ...item,
      gmpDocNeedTypeRelease:
        oppositeGmpDocNeedType?.cdVal ?? item.gmpDocNeedTypeRelease,
      gmpDocNeedTypeReleaseNm:
        oppositeGmpDocNeedType?.cdNm ?? item.gmpDocNeedTypeReleaseNm,
    };
  });
  tablePropsDataGmpContent.value.tableData = JSON.parse(
    JSON.stringify(newTableData),
  );
};

/**
 * 指定アイテムの関連更新を行う
 */
const updateFormItems = (fieldId: string) => {
  // SOPに異状レベル3以上があるかのフラグ
  const odrDevFlg = verifyRsltListData.some(
    (item) => item.odrDevFlg === CONST_FLAGS.SJG.ODR_DEV_FLG.HIGH_LEVEL,
  );
  // 工場/市場出荷可否判定結果のどちらか片方でも不可を選択した場合、入力必須
  // SOPに異状レベル3以上かつ、
  // 工場/市場出荷可否判定結果のどちらか片方でも出荷可を選択していた場合、入力必須
  if (
    fieldId === constantData.releaseRsltM ||
    fieldId === constantData.releaseRsltQ
  ) {
    if (
      sjgConfirmInspectionFormRef2.value.formModel.releaseRsltM ===
        CONST_FLAGS.SJG.RELEASE_JUDGEMENT_STATUS.NG ||
      sjgConfirmInspectionFormRef3.value.formModel.releaseRsltQ ===
        CONST_FLAGS.SJG.RELEASE_JUDGEMENT_STATUS.NG ||
      (odrDevFlg &&
        (sjgConfirmInspectionFormRef2.value.formModel.releaseRsltM ===
          CONST_FLAGS.SJG.RELEASE_JUDGEMENT_STATUS.OK ||
          sjgConfirmInspectionFormRef3.value.formModel.releaseRsltQ ===
            CONST_FLAGS.SJG.RELEASE_JUDGEMENT_STATUS.OK))
    ) {
      sjgConfirmInspectionFormRef4.value.formItems.releaseExpl.rules = [
        rules.required('textBox'),
        rules.length(64, t('Cm.Chr.txtLength', [64])),
      ];
      sjgConfirmInspectionFormRef4.value.formItems.releaseExpl.tags = [
        { text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' },
      ];
    } else {
      sjgConfirmInspectionFormRef4.value.formItems.releaseExpl.rules = [
        rules.length(64, t('Cm.Chr.txtLength', [64])),
      ];
      sjgConfirmInspectionFormRef4.value.formItems.releaseExpl.tags = [];
    }
    customFormRenderingTriggerRef4.value =
      !customFormRenderingTriggerRef4.value;
  }
};

const closeAllDialog = () => {
  closeDialog('fragmentDialogVisible');
  emit('submit', props.privilegesBtnRequestData);
};

const getVerifyResult = async () => {
  // 照査確認結果一覧取得
  const { responseRef, errorRef } = await useGetVerifyResult({
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedRow!.lotSid,
    addBomFlg: '1',
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return;
  }
  if (responseRef.value) {
    releaseJudgeRouteType = responseRef.value.data.rData.releaseJudgeRoute;
    verifyRsltListData = responseRef.value.data.rData.verifyRsltList;
    gmpDocNeedTypeNmList = responseRef.value.data.rData.gmpDocNeedTypeNmList;
    tablePropsData.tableData = verifyRsltListData;
    tablePropsData.tableData = verifyRsltListData.map((item) => {
      const cells = [
        item.verifyOdrRslt === CONST_FLAGS.SJG.VERIFY_ODR_RSLT_FLG.FAIL &&
          'verifyOdrRsltNm',
      ]
        .filter(Boolean)
        .join(',');
      const rectItem = { ...item, cells };
      return rectItem;
    });

    tablePropsDataGmpContent.value.tableData =
      responseRef.value.data.rData.gmpInfoList.map((item) => ({
        ...item,
        recordChg: 0,
        gmpContentUniqueKey: `${item.gmpMngNo}-${uuidv4()}`,
      }));
    const comboBoxResData = await useGetComboBoxDataStandard({
      ...props.privilegesBtnRequestData,
      condList: [
        {
          cmbId: 'sjgVerifyRslt',
          condKey: 'm_sys_cmt',
          where: { cmt_cat: 'SJG_VERIFY_RSLT' },
        },
        {
          cmbId: 'releaseRsltM',
          condKey: 'm_mp_cd',
          where: { cd_id: 'RELEASE_RSLT', cd_val: 'P,F' },
        },
        {
          cmbId: 'releaseRsltQ',
          condKey: 'm_mp_cd',
          where: { cd_id: 'RELEASE_RSLT', cd_val: 'P,F' },
        },
        {
          cmbId: 'mfgMgrQcRslt',
          condKey: 'm_mp_cd',
          where: { cd_id: 'MFG_MGR_QC_RSLT' },
        },
        {
          cmbId: 'deviateMgmtExistMeas',
          condKey: 'm_mp_cd',
          where: { cd_id: 'DEVIATE_MGMT_EXIST_MEAS' },
        },
        {
          cmbId: 'changeMgmtExistMeas',
          condKey: 'm_mp_cd',
          where: { cd_id: 'CHANGE_MGMT_EXIST_MEAS' },
        },
        {
          cmbId: 'mfgPlantRelExecConf',
          condKey: 'm_mp_cd',
          where: { cd_id: 'MFG_PLANT_REL_EXEC_CONF' },
        },
        {
          cmbId: 'mfgPlantRelRslt',
          condKey: 'm_mp_cd',
          where: { cd_id: 'MFG_PLANT_REL_RSLT' },
        },
        {
          cmbId: 'deviateOnMktRelProc',
          condKey: 'm_mp_cd',
          where: { cd_id: 'DEVIATE_ON_MKT_REL_PROC' },
        },
        {
          cmbId: 'qaMgrInstOnDeviate',
          condKey: 'm_mp_cd',
          where: { cd_id: 'QA_MGR_INST_ON_DEVIATE' },
        },
        {
          cmbId: 'mtaProdQltEfficacySafe',
          condKey: 'm_mp_cd',
          where: { cd_id: 'MTA_PROD_QLT_EFFICACY_SAFE' },
        },
        {
          cmbId: 'productTestReport',
          condKey: 'm_mp_cd',
          where: { cd_id: 'PRODUCT_TEST_REPORT' },
        },
        {
          cmbId: 'whetherMaterialRevised',
          condKey: 'm_mp_cd',
          where: { cd_id: 'WHETHER_MATERIAL_REVISED' },
        },
      ],
    });
    if (comboBoxResData && comboBoxResData.rData.rList.length > 0) {
      setCustomFormComboBoxOptionList(
        sjgConfirmInspectionFormRef.value.formItems,
        comboBoxResData.rData.rList,
      );
      setCustomFormComboBoxOptionList(
        sjgConfirmInspectionFormRef1.value.formItems,
        comboBoxResData.rData.rList,
      );
      setCustomFormComboBoxOptionList(
        sjgConfirmInspectionFormRef2.value.formItems,
        comboBoxResData.rData.rList,
      );
      setCustomFormComboBoxOptionList(
        sjgConfirmInspectionFormRef3.value.formItems,
        comboBoxResData.rData.rList,
      );
      setCustomFormComboBoxOptionList(
        sjgConfirmInspectionFormRef4.value.formItems,
        comboBoxResData.rData.rList,
      );
    }
    openDialog('fragmentDialogVisible');
    closeLoading();
  }
};

// 出荷判定可否チェック
const checkBeforeRelease = async () => {
  const { responseRef, errorRef } = await useCheckBeforeRelease({
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedRow!.lotSid,
    invQty: props.selectedRow!.invQty,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
  }
  if (responseRef.value) {
    openDialog('sjgShipmentDecisionConfirm');
  }
};

// 出荷判定用照査結果取得
const getVerifyRsltRelease = async () => {
  const { responseRef, errorRef } = await useCheckVerifyRsltRelease({
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedRow!.lotSid,
    releaseRsltM:
      sjgConfirmInspectionFormRef2.value.formModel.releaseRsltM.toString(),
    releaseRsltQ:
      sjgConfirmInspectionFormRef3.value.formModel.releaseRsltQ.toString(),
    mfgMgrQcRslt:
      sjgConfirmInspectionFormRef.value.formModel.mfgMgrQcRslt.toString(),
    deviateMgmtExistMeas:
      sjgConfirmInspectionFormRef1.value.formModel.deviateMgmtExistMeas.toString(),
    changeMgmtExistMeas:
      sjgConfirmInspectionFormRef1.value.formModel.changeMgmtExistMeas.toString(),
    mfgPlantRelExecConf:
      sjgConfirmInspectionFormRef3.value.formModel.mfgPlantRelExecConf.toString(),
    mfgPlantRelRslt:
      sjgConfirmInspectionFormRef3.value.formModel.mfgPlantRelRslt.toString(),
    deviateOnMktRelProc:
      sjgConfirmInspectionFormRef3.value.formModel.deviateOnMktRelProc.toString(),
    qaMgrInstOnDeviate:
      sjgConfirmInspectionFormRef3.value.formModel.qaMgrInstOnDeviate.toString(),
    mtaProdQltEfficacySafe:
      sjgConfirmInspectionFormRef3.value.formModel.mtaProdQltEfficacySafe.toString(),
    productTestReport:
      sjgConfirmInspectionFormRef3.value.formModel.productTestReport.toString(),
    whetherMaterialRevised:
      sjgConfirmInspectionFormRef3.value.formModel.whetherMaterialRevised.toString(),
    releaseExpl:
      sjgConfirmInspectionFormRef4.value.formModel.releaseExpl.toString(),
    expiryYmd: props.selectedRow!.expiryYmd,
    invQtyVal: props.selectedRow!.invQtyVal,
  });
  if (errorRef.value) {
    const errorResponseData = errorRef.value.response;
    if (errorResponseData.rData.transitionFlg === TRANSITION_FLAG.TRANSITION) {
      messageBoxForcedTerminationRef.value.title =
        errorRef.value.response.rTitle;
      messageBoxForcedTerminationRef.value.content =
        errorRef.value.response.rMsg;
      openDialog('forcedTermination');
    } else {
      messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
      messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
      messageBoxSingleButtonRef.value.type = 'error';
      openDialog('singleButton');
    }
  }
  if (responseRef.value) {
    // 出荷判定可否チェック
    await checkBeforeRelease();
  }
};

const modReleaseFinForm = async () => {
  const validate =
    sjgConfirmInspectionFormRef.value.customForm !== undefined &&
    (await sjgConfirmInspectionFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    })) &&
    sjgConfirmInspectionFormRef1.value.customForm !== undefined &&
    (await sjgConfirmInspectionFormRef1.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    })) &&
    sjgConfirmInspectionFormRef2.value.customForm !== undefined &&
    (await sjgConfirmInspectionFormRef2.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    })) &&
    (releaseJudgeRouteType !== CONST_FLAGS.SJG.RELEASE_JUDGEMENT_ROUTE.GQP
      ? true
      : sjgConfirmInspectionFormRef3.value.customForm !== undefined &&
        (await sjgConfirmInspectionFormRef3.value.customForm.validate(
          (isValid) => {
            onValidateHandler(isValid);
          },
        ))) &&
    sjgConfirmInspectionFormRef4.value.customForm !== undefined &&
    (await sjgConfirmInspectionFormRef4.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));

  if (validate) {
    await getVerifyRsltRelease();
  }
  return false;
};

// 出荷判定完了
const apiHandler = async () => {
  showLoading();

  const gmpRsltDtlListData = tablePropsDataGmpContent.value.tableData.map(
    (item) => ({
      lotSid: item.lotSid?.toString() || '',
      verifyCat: item.verifyCat?.toString() || '',
      gmpSysMngNo: Number(item.gmpSysMngNo),
      gmpDocNeedTypeRelease: item.gmpDocNeedTypeRelease?.toString() || '',
    }),
  );

  const requestData: ExtendCommonRequestType<ModReleaseFinDataReq> = {
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxsjgShipmentDecisionConfirm.title,
    msgboxMsgTxt: messageBoxsjgShipmentDecisionConfirm.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    lotSid: props.selectedRow!.lotSid,
    releaseJudgeRoute: releaseJudgeRouteType,
    releaseRsltM:
      sjgConfirmInspectionFormRef2.value.formModel.releaseRsltM.toString() ??
      '',
    releaseRsltMNm: getComboBoxSelectOptionsLabel(
      sjgConfirmInspectionFormRef2.value.formModel.releaseRsltM.toString(),
      sjgConfirmInspectionFormRef2.value.formItems.releaseRsltM,
    ),
    releaseRsltQ:
      sjgConfirmInspectionFormRef3.value.formModel.releaseRsltQ.toString() ??
      '',
    releaseRsltQNm: getComboBoxSelectOptionsLabel(
      sjgConfirmInspectionFormRef3.value.formModel.releaseRsltQ.toString(),
      sjgConfirmInspectionFormRef3.value.formItems.releaseRsltQ,
    ),
    mfgMgrQcRslt:
      sjgConfirmInspectionFormRef.value.formModel.mfgMgrQcRslt.toString() ?? '',
    mfgMgrQcRsltNm: getComboBoxSelectOptionsLabel(
      sjgConfirmInspectionFormRef.value.formModel.mfgMgrQcRslt.toString(),
      sjgConfirmInspectionFormRef.value.formItems.mfgMgrQcRslt,
    ),
    deviateMgmtExistMeas:
      sjgConfirmInspectionFormRef1.value.formModel.deviateMgmtExistMeas.toString() ??
      '',
    deviateMgmtExistMeasNm: getComboBoxSelectOptionsLabel(
      sjgConfirmInspectionFormRef1.value.formModel.deviateMgmtExistMeas.toString(),
      sjgConfirmInspectionFormRef1.value.formItems.deviateMgmtExistMeas,
    ),
    changeMgmtExistMeas:
      sjgConfirmInspectionFormRef1.value.formModel.changeMgmtExistMeas.toString() ??
      '',
    changeMgmtExistMeasNm: getComboBoxSelectOptionsLabel(
      sjgConfirmInspectionFormRef1.value.formModel.changeMgmtExistMeas.toString(),
      sjgConfirmInspectionFormRef1.value.formItems.changeMgmtExistMeas,
    ),
    mfgPlantRelExecConf:
      sjgConfirmInspectionFormRef3.value.formModel.mfgPlantRelExecConf.toString() ??
      '',
    mfgPlantRelExecConfNm: getComboBoxSelectOptionsLabel(
      sjgConfirmInspectionFormRef3.value.formModel.mfgPlantRelExecConf.toString(),
      sjgConfirmInspectionFormRef3.value.formItems.mfgPlantRelExecConf,
    ),
    mfgPlantRelRslt:
      sjgConfirmInspectionFormRef3.value.formModel.mfgPlantRelRslt.toString() ??
      '',
    mfgPlantRelRsltNm: getComboBoxSelectOptionsLabel(
      sjgConfirmInspectionFormRef3.value.formModel.mfgPlantRelRslt.toString(),
      sjgConfirmInspectionFormRef3.value.formItems.mfgPlantRelRslt,
    ),
    deviateOnMktRelProc:
      sjgConfirmInspectionFormRef3.value.formModel.deviateOnMktRelProc.toString() ??
      '',
    deviateOnMktRelProcNm: getComboBoxSelectOptionsLabel(
      sjgConfirmInspectionFormRef3.value.formModel.deviateOnMktRelProc.toString(),
      sjgConfirmInspectionFormRef3.value.formItems.deviateOnMktRelProc,
    ),
    qaMgrInstOnDeviate:
      sjgConfirmInspectionFormRef3.value.formModel.qaMgrInstOnDeviate.toString() ??
      '',
    qaMgrInstOnDeviateNm: getComboBoxSelectOptionsLabel(
      sjgConfirmInspectionFormRef3.value.formModel.qaMgrInstOnDeviate.toString(),
      sjgConfirmInspectionFormRef3.value.formItems.qaMgrInstOnDeviate,
    ),
    mtaProdQltEfficacySafe:
      sjgConfirmInspectionFormRef3.value.formModel.mtaProdQltEfficacySafe.toString() ??
      '',
    mtaProdQltEfficacySafeNm: getComboBoxSelectOptionsLabel(
      sjgConfirmInspectionFormRef3.value.formModel.mtaProdQltEfficacySafe.toString(),
      sjgConfirmInspectionFormRef3.value.formItems.mtaProdQltEfficacySafe,
    ),
    productTestReport:
      sjgConfirmInspectionFormRef3.value.formModel.productTestReport.toString() ??
      '',
    productTestReportNm: getComboBoxSelectOptionsLabel(
      sjgConfirmInspectionFormRef3.value.formModel.productTestReport.toString(),
      sjgConfirmInspectionFormRef3.value.formItems.productTestReport,
    ),
    whetherMaterialRevised:
      sjgConfirmInspectionFormRef3.value.formModel.whetherMaterialRevised.toString() ??
      '',
    whetherMaterialRevisedNm: getComboBoxSelectOptionsLabel(
      sjgConfirmInspectionFormRef3.value.formModel.whetherMaterialRevised.toString(),
      sjgConfirmInspectionFormRef3.value.formItems.whetherMaterialRevised,
    ),
    releaseExpl:
      sjgConfirmInspectionFormRef4.value.formModel.releaseExpl.toString() ?? '',
    expiryYmd: props.selectedRow!.expiryYmd,
    expiryTxt: props.selectedRow!.expiryTxt,
    invQtyVal: props.selectedRow!.invQtyVal,
    unitNm: props.selectedRow!.unitNm,
    gmpRsltDtlList: gmpRsltDtlListData,
  };

  const { responseRef, errorRef } = await useModifyReleaseFin(requestData);

  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
  }

  if (responseRef.value) {
    await useFileDownload({
      commonRequestData: {
        ...props.privilegesBtnRequestData,
        msgboxTitleTxt: messageBoxsjgShipmentDecisionConfirm.title,
        msgboxMsgTxt: messageBoxsjgShipmentDecisionConfirm.content,
        msgboxBtnTxt: t('Cm.Chr.btnOk'),
      },
      sysBinNo: responseRef.value.data.rData.sysBinNo,
    });
    closeLoading();
    closeDialog('fragmentDialogVisible');
    emit('submit');
  }
  return false;
};

const modShowSignDialog = async () => {
  closeDialog('sjgShipmentDecisionConfirm');
  // 3.署名（顔認証）ダイアログ(W1Z2410)を表示し、顔認証を行う
  // 認証が成功した場合、下記の処理を行う
  try {
    // 署名ダイアログを表示
    await showSignDialog({
      commonRequestParam: {
        ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
      },
    });
    apiHandler();
  } catch (error) {
    console.log(error);
  }
};

/**
 * 初期設定
 */
const sjgConfirmInspectionResultsInit = async () => {
  if (!props.selectedRow) return;
  showLoading();
  sjgConfirmInspectionFormRef.value.formItems =
    getSjgConfirmInspectionFormItems();
  sjgConfirmInspectionFormRef1.value.formItems =
    getSjgConfirmInspectionFormItems1();
  sjgConfirmInspectionFormRef2.value.formItems =
    getSjgConfirmInspectionFormItems2();
  sjgConfirmInspectionFormRef3.value.formItems =
    getSjgConfirmInspectionFormItems3();
  sjgConfirmInspectionFormRef4.value.formItems =
    getSjgConfirmInspectionFormItems4();

  Object.entries(props.selectedRow).forEach(([key, value]) => {
    if (key in sjgConfirmInspectionResultsInfoShowRef.value.infoShowItems) {
      sjgConfirmInspectionResultsInfoShowRef.value.infoShowItems[
        key
      ].infoShowModelValue = value?.toString() ?? '';
    }
  });
  const requestData: ExtendCommonRequestType<CheckBeforeReleaseReq> = {
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedRow.lotSid,
    invQty: props.selectedRow.invQty,
  };
  const { responseRef, errorRef } = await useCheckBeforeRelease(requestData);
  if (errorRef.value) {
    closeLoading();
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    return;
  }
  if (responseRef.value) {
    await getVerifyResult();
  }
};

watch(() => props.isClicked, sjgConfirmInspectionResultsInit);
</script>
<style lang="scss" scoped></style>

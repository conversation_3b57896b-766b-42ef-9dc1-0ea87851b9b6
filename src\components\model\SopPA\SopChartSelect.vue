<template>
  <el-dialog
    v-model="state.SOPChartDialogVisible"
    :show-close="false"
    :close-on-click-modal="false"
    :append-to-body="true"
    width="70%"
    :style="getDialogMinWidth()"
    :title="$t('SOP.Chr.txtSopTempSelectTitle')"
    center
  >
    <el-card class="box-card">
      <div class="box-card-main">
        <div class="box-card-left">
          <div class="common-row-padding-top">
            <div style="overflow: auto" class="block-list-main">
              <el-tree
                style="max-width: 600px"
                :data="state.treeData"
                @node-click="handleNodeClick"
              />
            </div>
          </div>
        </div>
        <div class="box-card-right">
          <div class="common-row-padding-top">
            <div class="select-block-main">
              <div class="box-card-stencil" ref="boxCardStencil"></div>
              <div class="box-card-chart" ref="boxChardGraph"></div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
    <template #footer>
      <div class="dialog-footer">
        <div class="dialog-footer-left">
          <ButtonEx
            type="secondary"
            size="normal"
            :text="t('Cm.Chr.btnCancel')"
            icon-class="cancel_dark"
            @click="SOPAddCancel"
          />
        </div>
        <div class="dialog-footer-right">
          <ButtonEx
            type="primary"
            size="normal"
            :text="t('Cm.Chr.btnDecision')"
            icon-class="ok"
            :disabled="false"
            @click="SOPAddSave"
          />
        </div>
        <div class="dialog-footer-center">
          <ButtonEx
            type="dangerSecond"
            size="normal"
            :text="t('Cm.Chr.btnDelate')"
            icon-class="cancel_dark"
            @click="SOPDelete"
          />
        </div>
      </div>
    </template>
    <MessageBox
      v-show="dialogVisibleRef.singleButtonRef"
      :dialog-props="messageBoxSingleButtonRef"
      :cancelCallback="() => closeDialog('singleButtonRef')"
      :submitCallback="() => closeDialog('singleButtonRef')"
    />
    <MessageBox
      v-show="dialogVisibleRef.sopDeleteConfirmRef"
      :dialog-props="messageBoxSopDeleteConfirmRef"
      :cancelCallback="() => closeDialog('sopDeleteConfirmRef')"
      :submitCallback="
        () => {
          const selOption = getSelectedBlock(state.selectID);
          deleteSopBlock(selOption.sopFlowNo, selOption.updDts);
        }
      "
    />
    <MessageBox
      v-show="dialogVisibleRef.sopDeleteRef"
      :dialog-props="messageBoxSopDeleteRef"
      :cancelCallback="() => closeDialog('sopDeleteRef')"
      :submitCallback="
        () => {
          closeDialog('sopDeleteRef');
          createNewGraph();
        }
      "
    />
  </el-dialog>
</template>
<script setup lang="ts">
import { Graph, Shape, Node, Edge } from '@antv/x6';
import { reactive, watch, ref, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import ButtonEx from '@/components/parts/ButtonEx.vue';
import {
  getSOPSetting,
  getImageMap,
  // formatSOPFlowEdges,
  getSOPFlowChartData,
  checkPartCode,
} from '@/components/model/SopPA/SopChartSetting';
import {
  SopNodeProperty,
  ImageOption,
  // SopFlowEdgeOption,
  SopBlockOption,
  SopSelectBlock,
  SopFlowDataOption,
  SopRectPartsOption,
  // [課題345] ADD ST NODEの連結の設計をリファクタリング
  SopControlPartOption,
  PositionOption,
  // [課題399] ADD ST 新しいListを作るのため
  SopFlowGetDataOption,
  SelectBranchOption,
  // [課題399] ADD ED
  // [課題345] ADD ED
} from '@/types/SopDialogInterface';
import svgMenuHome from '@/assets/icons/svg/menuHome.svg';
import svgSopTemplate from '@/assets/icons/svg/sopTemplate.svg';
import svgQuestion from '@/assets/icons/svg/icon_question_1_blue.svg';
import svgSopBlock from '@/assets/icons/svg/sopBlock.svg';
import svgAdd from '@/assets/icons/svg/add.svg';
import svgWCheck from '@/assets/icons/svg/wCheck.svg';
import svgWrite from '@/assets/icons/svg/write.svg';
import svgAbnormalityLevel1 from '@/assets/icons/svg/abnormalityLevel1.svg';
import svgAbnormalityLevel2 from '@/assets/icons/svg/abnormalityLevel2.svg';
import svgAbnormalityLevel3 from '@/assets/icons/svg/abnormalityLevel3.svg';
import svgAbnormalityLevel4 from '@/assets/icons/svg/abnormalityLevel4.svg';
import svgAbnormalityLevel5 from '@/assets/icons/svg/abnormalityLevel5.svg';
import svgEdit from '@/assets/icons/svg/edit.svg';
import svgTrash from '@/assets/icons/svg/trashBox.svg';
import svgCopy from '@/assets/icons/svg/copy.svg';
import PartNumericTextInput from '@/assets/icons/svg/PartNumericTextInput.svg';
import PartInstructionConfirm from '@/assets/icons/svg/PartInstructionConfirm.svg';
import PartSopTimer from '@/assets/icons/svg/PartSopTimer.svg';
import PartDateRecord from '@/assets/icons/svg/PartDateRecord.svg';
import PartElectronicFile from '@/assets/icons/svg/PartElectronicFile.svg';
import PartReceiveConsumption from '@/assets/icons/svg/PartReceiveConsumption.svg';
import PartResultConfirm from '@/assets/icons/svg/PartResultConfirm.svg';
import svgTestPartButtonBranch from '@/assets/icons/svg/testpartButtonBranch.svg';
import svgTestPartSystemBranch from '@/assets/icons/svg/testpartSystemBranch.svg';
import PartElectronicShelfLabel from '@/assets/icons/svg/PartElectronicShelfLabel.svg';
import PartInventoryConsumption from '@/assets/icons/svg/PartInventoryConsumption.svg';
import svgTestPartUpdDevice from '@/assets/icons/svg/testpartUpdDevice.svg';
import svgTestPartLabelOutput from '@/assets/icons/svg/testpartLabelOutput.svg';
import svgTestPartCommDevice from '@/assets/icons/svg/testpartCommDevice.svg';
import svgTestPartWeighingCalib from '@/assets/icons/svg/testpartWeighingCalib.svg';
import svgTestPartPalletCargo from '@/assets/icons/svg/testpartPalletCargo.svg';
import PartCopyNode from '@/assets/icons/svg/PartCopyNode.svg';
import CONST from '@/constants/utils';
import {
  useGetSopBlockList,
  useGetSopFlowData,
  useDeleteSopBlock,
} from '@/hooks/useApi';
import {
  GetSopBlockList,
  GetSopFlowData,
  DeleteSopBlock,
} from '@/types/HookUseApi/SopTypes';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import SOP_PARTS_VARIABLES from '@/constants/sopPartsVariables';
import {
  BranchList,
  Matrix,
  createNodesMatrixFromDb,
  calcNodePosY,
  findNearParentBranchId,
  addBlockTargetEdge4Temp,
  findNodeRow,
  addPathTargetEdge,
  findNodeCol,
  findParentBranchId,
  getEdgeNodeType,
  getLoopTargetNode,
  createBranchList,
  createNodeIdToAttrMapFromDB,
} from '@/utils/sopDrawGraph';

type DialogRefKey = 'singleButtonRef' | 'sopDeleteConfirmRef' | 'sopDeleteRef';
const initialState: InitialDialogState<DialogRefKey> = {
  singleButtonRef: false,
  sopDeleteConfirmRef: false,
  sopDeleteRef: false,
};

const props = withDefaults(defineProps<Props>(), {
  SOPSelectFlag: false,
  SOPBlockType: '',
  SOPblockNo: '',
  SOPFlowType: '',
  screenWidth: 1280,
  screenHeight: 1024,
});
const emit = defineEmits(['SOPSelectVisable', 'deleteBlockNode']);
const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);
const { t } = useI18n();
const sopSetting = getSOPSetting(t);
const boxChardGraph = ref<HTMLDivElement | null>(null);
const boxCardStencil = ref<HTMLDivElement | null>(null);

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});
const messageBoxSopDeleteConfirmRef = ref<DialogProps>({
  title: '',
  content: '',
  type: 'question',
});
const messageBoxSopDeleteRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'warning',
});

interface State {
  screenWidthVal: number;
  screenHeightVal: number;
  localeSelectGraph: Graph | null;
  SOPChartDialogVisible: boolean;
  treeData: Tree[];
  selectID: string;
  activeName: string;
  SOPSetData: SopNodeProperty;
  images: ImageOption[];
  imageMap: Map<string, string>;
  blockNo: string;
  blockType: string;
  selectFlowNo: string;
  selectItemName: string;
  blockList: SopBlockOption[];
  SOPBlockOption: SopBlockOption;
}
// [課題399] ADD ST 新しいListを作ります。ブロックのノードを保存します。
let selectList: SopFlowGetDataOption[] = [];
// [課題399] ADD ED
const state = reactive<State>({
  screenWidthVal: 0,
  screenHeightVal: 0,
  localeSelectGraph: null,
  SOPChartDialogVisible: false,
  treeData: [],
  selectID: '',
  activeName: '',
  SOPSetData: {
    ...sopSetting,
  },
  imageMap: new Map<string, string>(),
  images: [
    {
      fileName: 'menuHome',
      path: svgMenuHome,
    },
    {
      fileName: 'sopTemplate',
      path: svgSopTemplate,
    },
    {
      fileName: 'question',
      path: svgQuestion,
    },
    {
      fileName: 'add',
      path: svgAdd,
    },
    {
      fileName: 'sopBlock',
      path: svgSopBlock,
    },
    {
      fileName: 'write',
      path: svgWrite,
    },
    {
      fileName: 'wCheck',
      path: svgWCheck,
    },
    {
      fileName: 'abnormalityLevel1',
      path: svgAbnormalityLevel1,
    },
    {
      fileName: 'abnormalityLevel2',
      path: svgAbnormalityLevel2,
    },
    {
      fileName: 'abnormalityLevel3',
      path: svgAbnormalityLevel3,
    },
    {
      fileName: 'abnormalityLevel4',
      path: svgAbnormalityLevel4,
    },
    {
      fileName: 'abnormalityLevel5',
      path: svgAbnormalityLevel5,
    },
    {
      fileName: 'edit',
      path: svgEdit,
    },
    {
      fileName: 'trash',
      path: svgTrash,
    },
    {
      fileName: 'copy',
      path: svgCopy,
    },
    {
      fileName: 'partNumericTextInput',
      path: PartNumericTextInput,
    },
    {
      fileName: 'partInstructionConfirm',
      path: PartInstructionConfirm,
    },
    {
      fileName: 'partSopTimer',
      path: PartSopTimer,
    },
    {
      fileName: 'partDateRecord',
      path: PartDateRecord,
    },
    {
      fileName: 'partElectronicFile',
      path: PartElectronicFile,
    },
    {
      fileName: 'partReceiveConsumption',
      path: PartReceiveConsumption,
    },
    {
      fileName: 'partResultConfirm',
      path: PartResultConfirm,
    },
    {
      fileName: 'testpartButtonBranch',
      path: svgTestPartButtonBranch,
    },
    {
      fileName: 'testpartSystemBranch',
      path: svgTestPartSystemBranch,
    },
    {
      fileName: 'partElectronicShelfLabel',
      path: PartElectronicShelfLabel,
    },
    {
      fileName: 'partInventoryConsumption',
      path: PartInventoryConsumption,
    },
    {
      fileName: 'testpartUpdDevice',
      path: svgTestPartUpdDevice,
    },
    {
      fileName: 'testpartLabelOutput',
      path: svgTestPartLabelOutput,
    },
    {
      fileName: 'testpartCommDevice',
      path: svgTestPartCommDevice,
    },
    {
      fileName: 'testpartWeighingCalib',
      path: svgTestPartWeighingCalib,
    },
    {
      fileName: 'testpartPalletCargo',
      path: svgTestPartPalletCargo,
    },
    {
      fileName: 'partCopyNode',
      path: PartCopyNode,
    },
  ],
  blockNo: '',
  blockType: '',
  selectFlowNo: '',
  selectItemName: '',
  blockList: [],
  SOPBlockOption: {
    sopFlowNo: '',
    sopFlowNmJp: '',
    sopCatTxtJp: '',
    untSopCat: '',
    updDts: '',
  },
});
interface Props {
  SOPSelectFlag: boolean;
  SOPBlockType: string;
  SOPblockNo: string;
  SOPFlowType: string;
  screenWidth: number;
  screenHeight: number;
  commonRequest: CommonRequestType;
}
interface Tree {
  id: string;
  label: string;
  children?: Tree[];
}
interface TreeChildren {
  id: string;
  label: string;
}
/**
 * キャンバスサイズの計算
 * @param {*} sizeWidth - screen Width
 * @param {*} sizeHeight - screen Height
 */
const getLocaleGraphSize = (sizeWidth: number, sizeHeight: number) => {
  // -40(dialog padding)-20(card padding)-10(card left padding)-10(card right padding)-310 (select list)
  let localeWidthNum: number = 0;
  const pageSizeWidthData: string = '0';
  const pageSizeWidthValue: number = Number(
    pageSizeWidthData.replace('px', ''),
  );
  if (sizeWidth > pageSizeWidthValue) {
    localeWidthNum = sizeWidth * 0.7 - 404;
  } else {
    localeWidthNum = pageSizeWidthValue * 0.7 - 404;
  }
  // -54(dialog header)-20(dialog padding)-80(dialog footer)-30(block name)-210(form)
  let localeHeightNum: number = 0;
  const pageSizeHeightData: string = '0';
  const pageSizeHeightValue: number = Number(
    pageSizeHeightData.replace('px', ''),
  );
  if (sizeHeight > pageSizeHeightValue) {
    localeHeightNum = sizeHeight * 0.7 - 117;
  } else {
    localeHeightNum = pageSizeHeightValue * 0.7 - 117;
  }
  return {
    localeWidth: localeWidthNum,
    localeHeight: localeHeightNum,
  };
};
/**
 * set Locale Graph Size
 * @param {*} widthVal - screen width
 * @param {*} heightVal - screen height
 */
const setLocaleGraphSize = (widthVal: number, heightVal: number) => {
  const localeData = getLocaleGraphSize(widthVal, heightVal);
  if (state.localeSelectGraph !== null && props.SOPSelectFlag !== false) {
    state.localeSelectGraph!.resize(
      localeData.localeWidth,
      localeData.localeHeight,
    );
    state.localeSelectGraph!.zoomToFit({ maxScale: 0.6 });
    state.localeSelectGraph!.centerContent();
  }
};

// /**
//  * エッジの作成
//  * @param {*} edgeList - エッジのデータ
//  */
// const createSOPFlowEdges = (edgeList: SopFlowEdgeOption[]) => {
//   if (edgeList === null) {
//     return;
//   }
//   const nodes: Node[] = state.localeSelectGraph!.getNodes();
//   edgeList.forEach((item) => {
//     let fromPortId: string = '';
//     let fromNodeId: string = '';
//     let toPortId: string = '';
//     let toNodeId: string = '';
//     nodes.forEach((nodeItem: Node) => {
//       if (nodeItem.id === item.fromId) {
//         fromNodeId = nodeItem.id;
//         const fromPort = getCellPortsId(nodeItem.id);
//         fromPortId = fromPort.bottomId;
//       }
//       if (nodeItem.id === item.toId) {
//         toNodeId = nodeItem.id;
//         const toPort = getCellPortsId(nodeItem.id);
//         toPortId = toPort.topId;
//       }
//     });
//     if (toNodeId !== '' && fromNodeId !== '') {
//       // [課題2][No.4] MOD ST
//       // テンプレート選択画面のフロー表示とMAIN画面に展開後の表示に+ノードへの連結線は矢印なしにする
//       const targetNode = state.localeSelectGraph!.getCellById(toNodeId);
//       const targetNodeCD = targetNode.getProp<string>('sopPartsCD');
//       const isTargetJoinNodeFlg = checkPartCode(
//         state.SOPSetData.addPartCds,
//         targetNodeCD,
//       );
//       if (isTargetJoinNodeFlg) {
//         // 終了ノードは+ノードの場合、矢印なしの連結線で描画
//         state.localeSelectGraph!.addEdge({
//           source: { cell: fromNodeId, port: fromPortId },
//           target: { cell: toNodeId, port: toPortId },
//           attrs: {
//             line: {
//               stroke: '#a8b0c2',
//               strokeWidth: 2,
//               targetMarker: {
//                 name: 'path',
//                 d: '',
//               },
//             },
//           },
//           zIndex: 3,
//         });
//       } else {
//         // 終了ノードは通常ノードの場合、矢印ありの連結線で描画
//         state.localeSelectGraph!.addEdge({
//           source: { cell: fromNodeId, port: fromPortId },
//           target: { cell: toNodeId, port: toPortId },
//           attrs: {
//             line: {
//               stroke: '#a8b0c2',
//               strokeWidth: 2,
//               targetMarker: {
//                 name: 'block',
//                 width: 12,
//                 height: 8,
//               },
//             },
//           },
//           zIndex: 3,
//         });
//       }
//       // [課題2][No.4] MOD ED
//     }
//   });
// };
// [課題345] ADD ST NODEの連結の設計をリファクタリング
// /**
//  * getItemValueByKey
//  * @param item
//  * @param key
//  */
// const getItemValueByKey = <T extends object, K extends keyof T>(
//   item: T,
//   key: K,
// ): T[K] => item[key];
// /**
//  * パーツの作成
//  * @param {*} positionX - 座標 X
//  * @param {*} positionY - 座標 Y
//  * @remarks
//  */
// const createAddPart = (positionX: number, positionY: number) => {
//   const sopCommonSetting = state.SOPSetData.commonSetting;
//   const sopIndividualPara = state.SOPSetData.individualPara;
//   // @ts-expect-error SopControlPartOptionにstartParts既に存在します
//   const controls: SopControlPartOption[] = state.SOPSetData.startParts;
//   const rectItem = {
//     ...controls[1],
//     ports: { ...state.SOPSetData.addPartPorts },
//   };
//   const sopPartsCD = rectItem.sopPartsCD as keyof typeof sopIndividualPara;
//   const individualPara = getItemValueByKey(sopIndividualPara, sopPartsCD);
//   rectItem.zIndex = 4;
//   rectItem.x = positionX;
//   rectItem.y = positionY;
//   rectItem.cloneFlag = false;
//   rectItem.commonSetting = sopCommonSetting;
//   rectItem.individualPara = individualPara;
//   const addPart = state.localeSelectGraph!.addNode(rectItem);
//   return addPart;
// };
// /**
//  * ノード座標の取得
//  * @param {*} cellPosition - 親ノードの座標
//  * @param {*} branchNum - 分岐数
//  * @param {*} count - count
//  * @remarks
//  */
// const getPartPosition = (
//   cellPosition: PositionOption,
//   branchNum: number,
//   count: number,
// ) => {
//   const { sopPartConst } = state.SOPSetData;
//   const widthValue =
//     (sopPartConst!.defaultWidth * branchNum +
//       sopPartConst!.marginLeft * (branchNum - 1)) /
//     2;
//   const positionX =
//     cellPosition.x +
//     sopPartConst!.defaultWidth / 2 -
//     widthValue +
//     (sopPartConst!.defaultWidth + sopPartConst!.marginLeft) * count;
//   const positionY = cellPosition.y;
//   return {
//     x: positionX,
//     y: positionY,
//   };
// };
// [課題345] ADD ED

/**
 * ノードの2次元配列（ノードマトリックス）からグラフを再描画する
 *
 * @param allNodesMatrix - ノードIDの2次元配列（行: Y方向, 列: X方向）
 * @param nodeAttrMap - ノードIDをキーとした属性情報マップ（位置・種別など）
 *
 * 概要:
 * 1. 既存のノード・エッジを全削除し、allNodesMatrixの内容に基づきノードを再生成する。
 * 2. ノード種別ごとにX/Y座標を計算し、ノードを配置する。
 *    - Start/Endノードは特別なY計算
 *    - Join/Branch/Confluenceなどaddpart系は個別の間隔定数で調整可能
 *    - 通常パーツは等間隔、直前が収束点の場合は間隔を個別調整可能
 * 3. outgoingIdsやbranchOption.valueなどの情報を元にエッジを再接続する。
 * 4. 必要に応じてラベルや属性の復元、レイアウト調整も行う。
 * 5. 最後に全ノードの位置情報をログ出力（デバッグ用）
 *
 * 主な用途:
 * - パーツ追加・削除・編集後のグラフ再描画
 * - ノードの位置や接続関係の再構築
 */
const redrawFromNodesMatrix = (
  allNodesMatrix: Matrix,
  nodeAttrMap: Record<string, unknown>,
  branchList: BranchList,
  parentToConfluenceMap: Record<string, string>,
  outputLog = false,
) => {
  // state.sopPartAddType = 'redrawFromNodesMatrixAdd';
  if (outputLog)
    console.log(
      'redrawFromNodesMatrix: 開始',
      'nodeAttrMap:',
      nodeAttrMap,
      'branchList:',
      branchList,
    );
  // 1. 既存ノード・エッジを全削除
  state.localeSelectGraph!.getNodes().forEach((node) => {
    state.localeSelectGraph!.removeNode(node.id);
    node.removeTool('button');
    node.removeTool('boundary');
  });
  state
    .localeSelectGraph!.getEdges()
    .forEach((edge) => state.localeSelectGraph!.removeEdge(edge.id));
  const localeData = getLocaleGraphSize(
    state.screenWidthVal,
    state.screenHeightVal,
  );
  // 位置情報（パーツ幅＋パーツ間のスペース）を定数化
  const sopPartConst = state.SOPSetData?.sopPartConst;
  const partWidth = sopPartConst?.partWidth ?? 376;
  const partSpaceX = sopPartConst?.marginLeft ?? 20;
  const addPartWidth = sopPartConst?.addPartWidth ?? 24;
  const startPartWidth = sopPartConst?.startPartWidth ?? partWidth;
  const xInterval = partWidth + partSpaceX;
  const baseX = localeData.localeWidth / 2 - startPartWidth / 2;

  // 2. 2次元配列からノードを再作成
  const idToNodeMap = new Map<string, Node>();
  allNodesMatrix.forEach((row, yIdx) => {
    row.forEach((id, xIdx) => {
      if (!id) return;
      // ノード種別ごとに位置を調整
      let posX = baseX;
      // let posY = baseY;
      let sopPartsCD = '';
      if (nodeAttrMap && nodeAttrMap[id]) {
        sopPartsCD =
          // @ts-expect-error sopPartsCD/shape属性ある
          nodeAttrMap[id]?.sopPartsCD || nodeAttrMap[id]?.shape || '';
      }

      // --- X位置計算 ---
      if (
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_START_NAME ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_START_CD ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_END_NAME ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_END_CD
      ) {
        posX = baseX + xIdx * xInterval;
      } else if (
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_JOIN_NAME ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_NAME ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_NAME ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD
      ) {
        posX = baseX + (startPartWidth - addPartWidth) / 2 + xIdx * xInterval;
      } else {
        posX = baseX + (startPartWidth - partWidth) / 2 + xIdx * xInterval;
      }
      // --- Y位置計算 ---
      // オプション指定
      const options = {
        baseY: 10, // ベースY
        marginY: 104, // マージン（Y間隔）
      };
      // 呼び出し
      const posY = calcNodePosY(
        sopPartsCD,
        yIdx,
        allNodesMatrix,
        nodeAttrMap,
        options,
      );
      // --- ノード追加処理 ---
      // @ts-expect-error nodeAttrMap[id]はオブジェクトです。
      const attr = { ...nodeAttrMap[id] };
      const node = state.localeSelectGraph!.addNode({
        id,
        ...attr,
        position: { x: posX, y: posY },
      });
      // nodeAttrMapが関数パラメータの場合、直接代入せずにObject.assignを使う
      if (nodeAttrMap[id]) {
        Object.assign(nodeAttrMap[id], {
          position: { x: posX, y: posY }, // ノード位置を更新
        });
      }
      // ノードの位置を設定
      if (node && typeof node.setPosition === 'function') {
        node.setPosition(posX, posY);
      }
      idToNodeMap.set(id, node);
    });
  });
  if (outputLog) console.log('ノード配置完了:', Array.from(idToNodeMap.keys()));

  // 3. ノード間のエッジを再接続
  // ループ設定時の出力先リスト
  const loopOutputList: { id: string; outputId: string }[] = [];

  state
    .localeSelectGraph!.getEdges()
    .forEach((edge) => state.localeSelectGraph!.removeEdge(edge.id));
  allNodesMatrix.forEach((row, yIdx) => {
    row.forEach((id, xIdx) => {
      if (!id) return;
      // ノード種別判定
      const rowNode = idToNodeMap.get(id);
      if (!rowNode) {
        return;
      }
      const sopPartsCd = rowNode.getProp<string>('sopPartsCD');
      const nodeType = getEdgeNodeType(sopPartsCd, state.SOPSetData.partCds);

      // 入力エッジ
      let inputSourceId: string | null = null;
      // 出力エッジ
      let outputTargetId: string | null = null;

      if (nodeType === 'normal') {
        // 入力エッジ: 前の行・同じ列
        if (yIdx > 0) {
          inputSourceId = allNodesMatrix[yIdx - 1][xIdx];
        }
        // 出力エッジ: 次の行・同じ列
        if (yIdx < allNodesMatrix.length - 1) {
          outputTargetId = allNodesMatrix[yIdx + 1][xIdx];
        }
      } else if (nodeType === SOP_PARTS_VARIABLES.PART_SPEC_JOIN_NAME) {
        // 入力エッジ: 前の行・同じ列
        if (yIdx > 0) {
          inputSourceId = allNodesMatrix[yIdx - 1][xIdx];
        }
        // 出力エッジ: 次の行・同じ列
        if (
          yIdx < allNodesMatrix.length - 1 &&
          allNodesMatrix[yIdx + 1][xIdx] !== undefined &&
          allNodesMatrix[yIdx + 1][xIdx] !== null &&
          allNodesMatrix[yIdx + 1][xIdx] !== ''
        ) {
          outputTargetId = allNodesMatrix[yIdx + 1][xIdx];
        } else {
          // 関連する収束ノード
          const parentBranchId =
            findNearParentBranchId(branchList, allNodesMatrix[yIdx][xIdx]) ||
            allNodesMatrix[yIdx][xIdx];
          const confluenceNodeId = parentToConfluenceMap[parentBranchId];
          if (confluenceNodeId) {
            outputTargetId = confluenceNodeId;
          }
        }
      } else if (nodeType === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_NAME) {
        // 入力エッジ: 親分岐ノード
        // 分岐親パーツ特定
        const parentBranchId =
          findNearParentBranchId(branchList, allNodesMatrix[yIdx][xIdx]) ||
          allNodesMatrix[yIdx][xIdx];
        const parentRow = findNodeRow(allNodesMatrix, parentBranchId);
        const parentBranchList = allNodesMatrix[parentRow + 1].filter(
          (cell) => cell !== undefined && cell !== null && cell !== '',
        );
        if (
          parentBranchList.length > 0 &&
          parentBranchList.includes(allNodesMatrix[yIdx][xIdx])
        ) {
          inputSourceId = parentBranchId;
        }
        // 出力エッジ
        // 次の行・同じ列
        if (
          yIdx < allNodesMatrix.length - 1 &&
          allNodesMatrix[yIdx + 1][xIdx] !== undefined &&
          allNodesMatrix[yIdx + 1][xIdx] !== null &&
          allNodesMatrix[yIdx + 1][xIdx] !== ''
        ) {
          outputTargetId = allNodesMatrix[yIdx + 1][xIdx];
        } else {
          // ループ設定がある場合
          const loopTarget = getLoopTargetNode(
            id,
            parentBranchId,
            parentBranchList,
            nodeAttrMap,
            // @ts-expect-error localeSelectGraph
            state.localeSelectGraph,
          );
          if (loopTarget) {
            outputTargetId = loopTarget.id;
            loopOutputList.push({ id, outputId: outputTargetId });
          } else {
            // 関連する収束ノード
            const confluenceNodeId = parentToConfluenceMap[parentBranchId];
            if (confluenceNodeId) {
              outputTargetId = confluenceNodeId;
            }
          }
        }
      } else if (nodeType === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_NAME) {
        // 入力エッジ: 前の行・同じ列
        if (yIdx > 0) {
          inputSourceId = allNodesMatrix[yIdx - 1][xIdx];
        }
        // 出力エッジ: 次の行・同じ列
        if (
          yIdx < allNodesMatrix.length - 1 &&
          allNodesMatrix[yIdx + 1][xIdx] !== undefined &&
          allNodesMatrix[yIdx + 1][xIdx] !== null &&
          allNodesMatrix[yIdx + 1][xIdx] !== ''
        ) {
          outputTargetId = allNodesMatrix[yIdx + 1][xIdx];
        } else {
          // 関連する収束ノード
          const parentBranchId =
            findParentBranchId(branchList, allNodesMatrix[yIdx][xIdx]) ||
            allNodesMatrix[yIdx][xIdx];
          const rowFrom = findNodeRow(
            allNodesMatrix,
            allNodesMatrix[yIdx][xIdx],
          );
          const rowTo = findNodeRow(
            allNodesMatrix,
            parentToConfluenceMap[parentBranchId],
          );
          // 収束ノードの行を探す
          for (let i = rowFrom + 1; i <= rowTo; i++) {
            const nextRow = allNodesMatrix[i];
            if (
              nextRow &&
              nextRow[0] !== undefined &&
              nextRow[0] !== null &&
              nextRow[0] !== ''
            ) {
              const nextNode = idToNodeMap.get(nextRow[0]);
              if (!nextNode) {
                return;
              }
              const partsCd = rowNode.getProp<string>('sopPartsCD');
              if (partsCd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD) {
                const [firstId] = nextRow;
                outputTargetId = firstId;
                break;
              }
            }
          }
        }
      }
      // エッジ追加
      if (outputTargetId && outputTargetId !== id) {
        const targetNode = state.localeSelectGraph!.getCellById(outputTargetId);
        const targetPartsCd = targetNode.getProp<string>('sopPartsCD');
        const sameCdFlag = checkPartCode(
          state.SOPSetData.partCds,
          targetPartsCd,
        );
        if (
          sameCdFlag ||
          [
            SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_CD,
            SOP_PARTS_VARIABLES.PART_SPEC_END_CD,
          ].includes(targetPartsCd)
        ) {
          // @ts-expect-error localeSelectGraph
          addBlockTargetEdge4Temp(id, outputTargetId, state.localeSelectGraph);
        } else {
          // @ts-expect-error localeSelectGraph
          addPathTargetEdge(id, outputTargetId, state.localeSelectGraph);
        }
      }
      if (inputSourceId && inputSourceId !== id) {
        const targetNode = state.localeSelectGraph!.getCellById(id);
        const targetPartsCd = targetNode.getProp<string>('sopPartsCD');
        if (targetPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD) {
          // @ts-expect-error localeSelectGraph
          addPathTargetEdge(inputSourceId, id, state.localeSelectGraph);
        }
      }
    });
  });

  // 4. ノード属性やラベルなどの追加復元が必要な場合はここで行う
  allNodesMatrix.forEach((row) => {
    row.forEach((id) => {
      if (!id) return;
      let sopPartsCD = '';
      if (nodeAttrMap && nodeAttrMap[id]) {
        sopPartsCD =
          // @ts-expect-error sopPartsCD/shape属性ある
          nodeAttrMap[id]?.sopPartsCD || nodeAttrMap[id]?.shape || '';
      }
      if (sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD) {
        // branchOptionが配列の場合、各branchOptionにlabelをセット
        // @ts-expect-error branchOption属性ある
        const branchOption = nodeAttrMap[id]?.branchOption;
        if (Array.isArray(branchOption)) {
          branchOption.forEach((opt: SelectBranchOption) => {
            // ノードのラベルや他の情報からlabelをセット
            // @ts-expect-error label属性ある
            // eslint-disable-next-line no-param-reassign
            opt.label = nodeAttrMap[id]?.label ?? '';
          });
          // @ts-expect-error branchOption属性ある
          // eslint-disable-next-line no-param-reassign
          nodeAttrMap[id].branchOption = branchOption;
        }
      }
    });
  });

  // 5. レイアウト調整やツール追加なども必要に応じて
  // 収束ノードに対する.verticesプロパティの設定
  const allConfluenceNodes = state
    .localeSelectGraph!.getNodes()
    .filter(
      (node) =>
        node.getProp('sopPartsCD') ===
          SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_NAME ||
        node.getProp('sopPartsCD') ===
          SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD,
    );
  allConfluenceNodes.forEach((confluenceNode) => {
    const incomingEdges = state.localeSelectGraph!.getConnectedEdges(
      confluenceNode,
      {
        incoming: true,
      },
    );
    const confluencePos = confluenceNode.getProp<PositionOption>('position');
    incomingEdges.forEach((edge) => {
      // 収束ノードのY座標と異なる場合のみverticesを設定
      const sourceNode = state.localeSelectGraph!.getCellById(
        edge.getSourceCellId(),
      );
      const sourcePos = sourceNode.getProp<PositionOption>('position');
      if (sourcePos.y !== confluencePos.y) {
        // eslint-disable-next-line no-param-reassign
        edge.vertices = [
          {
            x: sourcePos.x,
            y:
              confluencePos.y -
              (state.SOPSetData?.sopPartConst?.addPartHeight ?? 24),
          },
        ];
      }
    });
  });
  // ループ設定パーツに対する.verticesプロパティの設定
  loopOutputList.forEach(({ id, outputId }) => {
    const columnIndex = findNodeCol(allNodesMatrix, id);
    if (columnIndex === 0) {
      return;
    }
    const outGoingEdges = state.localeSelectGraph!.getConnectedEdges(id, {
      outgoing: true,
    });
    const outputIdNode = state.localeSelectGraph!.getCellById(outputId);
    if (!outputIdNode) return;
    const outputPos = outputIdNode.getProp<PositionOption>('position');
    outGoingEdges.forEach((edge) => {
      // ループ設定ノードのY座標と異なる場合のみverticesを設定
      const sourceNode = state.localeSelectGraph!.getCellById(
        edge.getSourceCellId(),
      );
      if (!sourceNode) return;
      const sourcePos = sourceNode.getProp<PositionOption>('position');
      if (sourcePos.y !== outputPos.y) {
        // eslint-disable-next-line no-param-reassign
        edge.vertices = [
          {
            x: sourcePos.x + 20,
            y:
              outputPos.y -
              (state.SOPSetData?.sopPartConst?.addPartHeight ?? 24),
          },
        ];
      }
    });
  });

  // tools追加
  const addToolsNodes = state
    .localeSelectGraph!.getNodes()
    .filter(
      (node) =>
        node.getProp('sopPartsCD') !== SOP_PARTS_VARIABLES.PART_SPEC_START_CD &&
        node.getProp('sopPartsCD') !== SOP_PARTS_VARIABLES.PART_SPEC_END_CD &&
        node.getProp('sopPartsCD') !==
          SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD &&
        node.getProp('sopPartsCD') !==
          SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD &&
        node.getProp('sopPartsCD') !== SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD,
    );
  addToolsNodes.forEach((node) => {
    node.addTools({
      name: 'button',
      args: {
        zIndex: 1000,
        markup: [
          {
            tagName: 'image',
            selector: 'copy',
            attrs: {
              'xlink:href': state.imageMap.get('copy'),
              width: 20,
              height: 20,
              cursor: 'pointer',
              'pointer-events': 'all',
            },
          },
        ],
        x: '100%',
        y: 0,
        offset: { x: -25, y: 8 },
        onClick() {},
      },
    });
    node.addTools({
      name: 'button',
      args: {
        zIndex: 1000,
        markup: [
          {
            tagName: 'image',
            selector: 'trash',
            attrs: {
              'xlink:href': state.imageMap.get('trash'),
              width: 20,
              height: 20,
              cursor: 'pointer',
              'pointer-events': 'all',
            },
          },
        ],
        x: '100%',
        y: 0,
        offset: { x: -55, y: 8 },
        onClick() {},
      },
    });
    node.addTools({
      name: 'button',
      args: {
        zIndex: 1000,
        markup: [
          {
            tagName: 'image',
            selector: 'edit',
            attrs: {
              'xlink:href': state.imageMap.get('edit'),
              width: 20,
              height: 20,
              cursor: 'pointer',
              'pointer-events': 'all',
            },
          },
        ],
        x: '100%',
        y: 0,
        offset: { x: -85, y: 8 },
        onClick() {},
      },
    });
  });

  // 位置情報チェック処理
  const nodeAll = state.localeSelectGraph!.getNodes();
  const diffNodes: string[] = [];
  nodeAll.forEach((node) => {
    const { id } = node;
    const graphPos = node.getProp<PositionOption>('position');
    const attrPos =
      nodeAttrMap &&
      nodeAttrMap[id] &&
      // @ts-expect-error 既にnodeAttrMap[id]にposition追加
      (nodeAttrMap[id] as SopControlPartOption).position
        ? // @ts-expect-error 既にnodeAttrMap[id]にposition追加
          (nodeAttrMap[id] as SopControlPartOption).position
        : undefined;
    if (attrPos && (graphPos.x !== attrPos.x || graphPos.y !== attrPos.y)) {
      diffNodes.push(id);
      // 位置が異なる場合はnodeAttrMapの座標で更新
      if (typeof node.setPosition === 'function') {
        node.setPosition(attrPos.x, attrPos.y);
        console.log(
          `[位置修正] ID: ${id}, graphPos: x=${graphPos.x}, y=${graphPos.y} → attrPos: x=${attrPos.x}, y=${attrPos.y}`,
        );
      }
    }
  });

  if (outputLog) {
    // 全ノードのエッジをノード毎にログ表示（ノードID、入力エッジの接続先ノードID、出力エッジの接続先ノードID）
    const allEdges = state.localeSelectGraph!.getNodes();
    console.log('--- 全ノードのエッジ情報 ---');
    allEdges.forEach((node) => {
      // 入力エッジの接続元ノードID
      const incomingEdges =
        state.localeSelectGraph!.getIncomingEdges(node.id) || [];
      const incomingNodeIds = incomingEdges.map((e) => e.getSourceCellId());

      // 出力エッジの接続先ノードID
      const outgoingEdges =
        state.localeSelectGraph!.getOutgoingEdges(node.id) || [];
      const outgoingNodeIds = outgoingEdges.map((e) => e.getTargetCellId());
      console.log(
        `ノードID: ${node.id}, 入力エッジ接続元: [${incomingNodeIds.join(', ')}], 出力エッジ接続先: [${outgoingNodeIds.join(', ')}]`,
      );
    });

    // 全ノードの位置情報をログ出力（デバッグ用)
    const allNodes = state.localeSelectGraph!.getNodes();
    console.log('--- 全ノードの位置情報 ---');
    allNodes.forEach((node) => {
      const pos = node.getProp<PositionOption>('position');
      const partsCD = node.getProp<string>('sopPartsCD');
      console.log(
        `ID: ${node.id}, パーツ種別: ${partsCD}, 位置: x=${pos.x}, y=${pos.y}`,
      );
    });
  }
};

/**
 * ノードマトリックスとノード属性マップからグラフを再描画する（ラップ関数）
 * @param nodesMatrix - ノードIDの2次元配列
 */
const redrawGraphFromMatrix = (
  nodesMatrix: Matrix,
  branchList: BranchList,
  parentToConfluenceMap: Record<string, string>,
  node: SopFlowDataOption[],
) => {
  // @ts-expect-error 一旦assignします
  const nodeAttrMap = createNodeIdToAttrMapFromDB(node);
  redrawFromNodesMatrix(
    nodesMatrix,
    nodeAttrMap,
    branchList,
    parentToConfluenceMap,
    false,
  );
};

/**
 * flow chartの作成
 * @param {*} flowList - flowのデータ
 * @param {*} startNode - flowのstart Node
 */
const setSOPFlowChart = (flowList: SopFlowDataOption[], startNode: string) => {
  state.localeSelectGraph!.disableHistory();
  const startCell: SopFlowDataOption[] = [];
  const otherCell: SopFlowDataOption[] = [];
  const endCell: SopFlowDataOption[] = [];
  // // [課題345] ADD ST NODEの連結の設計をリファクタリング
  // const { sopPartConst } = state.SOPSetData;
  // const flowListLastNode = flowList[flowList.length - 1];
  // flowList.forEach((node) => {
  //   const addpartObj = {
  //     nodeId: '',
  //     sopNodeNo: '',
  //     sopCieX: 0,
  //     sopCieY: 0,
  //     sopPartsCd: '',
  //     sopPartsNm: '',
  //     helpSetting: {
  //       helpFileType: 'N',
  //     },
  //     sopJoin: {
  //       nextCondSeq: 1,
  //       nextNodeNo: '',
  //     },
  //   };
  //   /**
  //    * sopPartsCd 91: controlPartStart
  //    * sopPartsCd 94: controlPartEnd
  //    * sopPartsCd 92: PartSpecConfluence
  //    * sopPartsCd 02: PartSopTimer
  //    * sopPartsCd 01: PartInstructionConfirm
  //    * sopPartsCd 03: PartNumericTextInput
  //    * sopPartsCd 08: PartButtonBranch
  //    * sopPartsCd 09: PartSystemBranch
  //    * sopPartsCd 10: PartExternalDevice
  //    * sopPartsCd 15: PartWeightCalibration
  //    */
  //   const sopPartsCdList = [
  //     SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD,
  //     SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_CD,
  //     SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD,
  //     SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_CD,
  //     SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD,
  //     SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD,
  //     SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD,
  //     SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD,
  //     SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD,
  //     SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD,
  //   ];
  //   if (node.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_START_CD) {
  //     const addPart = createAddPart(node.sopCieX, node.sopCieY);
  //     addpartObj.nodeId = addPart.id;
  //     addpartObj.sopNodeNo = addPart.id;
  //     addpartObj.sopCieX =
  //       node.sopCieX +
  //       sopPartConst!.startPartHeight / 2 -
  //       sopPartConst!.addPartWidth / 2;
  //     addpartObj.sopCieY = node.sopCieY + 95;
  //     addpartObj.sopPartsCd = SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD;
  //     addpartObj.sopPartsNm = SOP_PARTS_VARIABLES.PART_SPEC_JOIN_NAME;
  //     addpartObj.sopJoin.nextNodeNo = JSON.stringify({
  //       condBrDst1: JSON.parse(node.sopJoin.nextNodeNo).condBrDst1,
  //     });
  //     // eslint-disable-next-line no-param-reassign
  //     node.sopJoin.nextNodeNo = JSON.stringify({ condBrDst1: addPart.id });
  //     // @ts-expect-error 簡潔なobjectをflowListに保存します。
  //     flowList.push(addpartObj);
  //     // @ts-expect-error 動的に変数判断
  //   } else if (sopPartsCdList.includes(node.sopPartsCd)) {
  //     const branchNextNodes = node.sopJoin.nextNodeNo
  //       ? JSON.parse(node.sopJoin.nextNodeNo)
  //       : '';
  //     const branchNodeLength = Object.values(branchNextNodes).filter(
  //       (item) => item !== '',
  //     ).length;
  //     Object.keys(branchNextNodes).forEach((item, index) => {
  //       if (branchNextNodes[item] !== '') {
  //         const positionX =
  //           node.sopCieX +
  //           sopPartConst!.blockWidth +
  //           sopPartConst!.addPartWidth;
  //         const positionY = node.sopCieY + 80 + sopPartConst!.marginTop;
  //         const position = getPartPosition(
  //           { x: positionX, y: positionY },
  //           branchNodeLength,
  //           index,
  //         );
  //         const addPart = createAddPart(position.x, position.y);
  //         const branchAddpart = {
  //           nodeId: addPart.id,
  //           sopNodeNo: addPart.id,
  //           sopCieX: position.x,
  //           sopCieY: position.y,
  //           sopPartsCd: SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD,
  //           helpSetting: {
  //             helpFileType: 'N',
  //           },
  //           sopPartsNm: SOP_PARTS_VARIABLES.PART_SPEC_JOIN_NAME,
  //           sopJoin: {
  //             nextNodeNo: JSON.stringify({
  //               condBrDst1: JSON.parse(node.sopJoin.nextNodeNo)[item],
  //             }),
  //           },
  //         };
  //         // eslint-disable-next-line no-param-reassign
  //         node.sopJoin.nextNodeNo = JSON.parse(node.sopJoin.nextNodeNo);
  //         Object.assign(node.sopJoin.nextNodeNo, { [`${item}`]: addPart.id });
  //         // eslint-disable-next-line no-param-reassign
  //         node.sopJoin.nextNodeNo = JSON.stringify(node.sopJoin.nextNodeNo);
  //         // @ts-expect-error 簡潔なobjectをflowListに保存します。
  //         flowList.push(branchAddpart);
  //       }
  //     });
  //   } else if (
  //     node.sopPartsCd !== SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD &&
  //     node.sopPartsCd !== SOP_PARTS_VARIABLES.PART_SPEC_END_CD &&
  //     node !== flowListLastNode
  //   ) {
  //     const addPart = createAddPart(node.sopCieX, node.sopCieY);
  //     addpartObj.nodeId = addPart.id;
  //     addpartObj.sopNodeNo = addPart.id;
  //     addpartObj.sopCieX =
  //       node.sopCieX +
  //       sopPartConst!.defaultWidth / 2 -
  //       sopPartConst!.addPartWidth / 2;
  //     addpartObj.sopCieY = node.sopCieY + sopPartConst!.marginTop + 80;
  //     addpartObj.sopPartsCd = SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD;
  //     addpartObj.sopPartsNm = SOP_PARTS_VARIABLES.PART_SPEC_JOIN_NAME;
  //     addpartObj.sopJoin.nextNodeNo = JSON.stringify({
  //       condBrDst1: node.sopJoin.nextNodeNo
  //         ? JSON.parse(node.sopJoin.nextNodeNo).condBrDst1
  //         : '',
  //     });
  //     // eslint-disable-next-line no-param-reassign
  //     node.sopJoin.nextNodeNo = JSON.stringify({ condBrDst1: addPart.id });
  //     // @ts-expect-error 簡潔なobjectをflowListに保存します。
  //     flowList.push(addpartObj);
  //   }
  // });
  // [課題345] ADD ED
  flowList.forEach((node) => {
    const cell = node;
    cell.nodeId = node.sopNodeNo.replace(`${node.sopFlowNo}-`, '');
    cell.sopPartsCd = node.sopPartsCd;
    switch (cell.helpSetting.helpFileType) {
      case 'N':
        cell.helpSetting.helpFileType = 'N';
        break;
      case 'I':
        cell.helpSetting.helpFileType = 'I';
        break;
      case 'M':
        cell.helpSetting.helpFileType = 'M';
        break;
      case 'P':
        cell.helpSetting.helpFileType = 'P';
        break;
      default:
        break;
    }
    cell.commonSetting = { ...cell.commonSetting, ...cell.helpSetting };
    if (node.sopNodeNo === startNode) {
      startCell.push(cell);
    } else if (node.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_END_CD) {
      endCell.push(cell);
    } else {
      otherCell.push(cell);
    }
  });
  // const FlowObj = formatSOPFlowEdges(startCell, otherCell, endCell);
  const nodeData = getSOPFlowChartData(
    flowList,
    state.SOPSetData,
    state.imageMap,
    t,
  );
  // @ts-expect-error 一旦assignします
  const newNodeMatrix = createNodesMatrixFromDb(nodeData);

  // 分岐パーツと収束ノードのマッピング情報を作成する（行の昇順で作成）
  const { branchList, parentToConfluenceMap } = createBranchList(
    newNodeMatrix,
    // @ts-expect-error state.localeSelectGraph
    state.localeSelectGraph,
    flowList,
  );
  // state.localeSelectGraph!.fromJSON({ nodes: nodeData });
  // createSOPFlowEdges(FlowObj.edges);
  // --- 収束ノードIDを分岐親パーツに追加 ---
  const confluenceList = flowList.filter(
    (node) => node.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD,
  );
  confluenceList.forEach((confluenceNode) => {
    const parentNodeId = confluenceNode.parentSopNodeNo;
    if (!parentNodeId) return;
    // 分岐親パーツを型安全に取得
    const parentNode = flowList.find((node) => node.sopNodeNo === parentNodeId);
    if (parentNode) {
      (
        parentNode as SopFlowGetDataOption & { confluenceNodeId?: string }
      ).confluenceNodeId = confluenceNode.sopNodeNo;
    }
  });

  // --- 収束ノードのプロパティに親ノードIDを設定し、親ノードのプロパティに収束ノードIDを設定（x6 Node型オブジェクトに対して） ---
  const confluenceNodes = state
    .localeSelectGraph!.getNodes()
    .filter(
      (node) =>
        node.getProp<string>('sopPartsCD') ===
        SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD,
    );
  confluenceNodes.forEach((confluenceNode) => {
    // 収束ノードの親ノードIDを取得
    const confluenceItem = confluenceList.find(
      (item) => item.sopNodeNo === confluenceNode.id,
    );
    const parentNodeId = confluenceItem
      ? confluenceItem.parentSopNodeNo
      : undefined;
    if (!parentNodeId) return;
    // 親ノード（Node型）を取得
    const parentNode = state.localeSelectGraph!.getCellById(parentNodeId);
    if (parentNode && parentNode.isNode()) {
      // 親ノードに収束ノードIDをセット
      parentNode.prop('confluenceNodeId', confluenceNode.id);
      // 収束ノードに親ノードIDをセット
      confluenceNode.prop('parentNodeId', parentNode.id);
    }
  });

  redrawGraphFromMatrix(
    newNodeMatrix,
    branchList,
    parentToConfluenceMap,
    // @ts-expect-error 一旦assignします
    nodeData,
  );
  // 各パーツデザイン調整
  const allNodes = state.localeSelectGraph!.getNodes();
  allNodes.forEach((item) => {
    const currentCell = state.localeSelectGraph!.getCellById(item.id);
    if (currentCell.isNode()) {
      const partsCD = currentCell.getProp<string>('sopPartsCD');
      const titleText = currentCell.getAttrByPath<string>('title/text');
      const commonSetting = currentCell.getProp('commonSetting');
      const sameCdFlag = checkPartCode(state.SOPSetData.partCds, partsCD);
      // [課題外] ADD ST ブロックとテンプレートをグラフに追加する時に、一部のパラメータをアサインします。
      currentCell.prop(
        'individualPara',
        flowList.filter((flowItem) => flowItem.nodeId === currentCell.id)[0]
          .individualPara,
      );
      currentCell.prop(
        'helpSetting',
        flowList.filter((flowItem) => flowItem.nodeId === currentCell.id)[0]
          .helpSetting,
      );
      // [課題外] ADD ED
      if (sameCdFlag) {
        const rectPartsData: SopRectPartsOption[] = state.SOPSetData.rectParts;
        const rectItem = rectPartsData.filter(
          (v) => v.sopPartsCD === partsCD,
        )[0];
        currentCell.setAttrs({
          line: rectItem.attrs.line,
          body: {
            stroke: '#a8b0c2',
            fill: '#f0ffff',
          },
          image: {
            refX: 5,
            refY: 5,
          },
          title: {
            text: titleText,
            refX: 55,
            refY: 40,
            fill: '#000000',
            fontSize: 16,
            fontWeight: 'bold',
            textAnchor: 'left',
          },
          text: {
            text: '',
          },
          wCheck: {
            display: commonSetting.dcheckFlg === '1' ? 'block' : 'none',
            refX: 150,
            refY: 3,
            width: 20,
            height: 20,
            x: 5,
            y: 5,
          },
          abnormalityLevel1: {
            display: commonSetting.devCorrLv === '1' ? 'block' : 'none',
            refX: 180,
            refY: 3,
            width: 20,
            height: 20,
            x: 5,
            y: 5,
          },
          abnormalityLevel2: {
            display: commonSetting.devCorrLv === '2' ? 'block' : 'none',
            refX: 180,
            refY: 3,
            width: 20,
            height: 20,
            x: 5,
            y: 5,
          },
          abnormalityLevel3: {
            display: commonSetting.devCorrLv === '3' ? 'block' : 'none',
            refX: 180,
            refY: 3,
            width: 20,
            height: 20,
            x: 5,
            y: 5,
          },
          write: {
            display: commonSetting.recFillFlg === '1' ? 'block' : 'none',
            refX: 120,
            refY: 3,
            width: 20,
            height: 20,
            x: 5,
            y: 5,
          },
        });
        if (commonSetting.scnShowFlg === '0') {
          currentCell.addTools({
            name: 'boundary',
            args: {
              zIndex: 0,
              attrs: {
                fill: '#f0f8ff',
                stroke: '#4169e1',
                'stroke-dasharray': '7, 8',
                strokeWidth: 3,
                fillOpacity: 0,
              },
            },
          });
        }
      }
    }
  });
};
/**
 * テンプレート一覧をTree形式に変換
 */
const convertBlockList = () => {
  if (state.blockList.length === 0) {
    return false;
  }
  const noCategoryName = t('SOP.Chr.txtNoCategory');
  const categorizedMap = new Map<
    string,
    { id: string; label: string; children: TreeChildren[] }
  >();
  state.blockList.forEach((item: SopBlockOption) => {
    const key = item.sopCatTxtJp || noCategoryName;
    if (!categorizedMap.has(key)) {
      categorizedMap.set(key, {
        id: key,
        label: key === noCategoryName ? noCategoryName : key,
        children: [],
      });
    }
    categorizedMap.get(key)!.children.push({
      id: item.sopFlowNo,
      label: item.sopFlowNmJp,
    });
  });
  state.treeData = Array.from(categorizedMap.values());
  return true;
};
/**
 * テンプレート／ブロック一覧を取得
 */
const getAllBlockList = async () => {
  state.treeData = [];
  const apiRequestData: GetSopBlockList = {
    UntSopCat: state.blockType,
    SopFlowType: props.SOPFlowType,
  };
  const { responseRef, errorRef } = await useGetSopBlockList({
    ...props.commonRequest,
    ...apiRequestData,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButtonRef');
    return;
  }
  if (responseRef.value) {
    state.blockList = responseRef.value.data.rData.rList;
    convertBlockList();
  } else {
    messageBoxSingleButtonRef.value.content = t('Tt.Chr.txtNoDataSet');
    messageBoxSingleButtonRef.value.type = 'info';
    openDialog('singleButtonRef');
    state.blockList = [];
  }
};
/**
 * 選択されたブロック情報を絞り込み
 */
const getSelectedBlock = (sopFlowNo: string) => {
  let selCell = state.SOPBlockOption;
  const allCell: SopBlockOption[] = state.blockList;
  allCell.forEach((item) => {
    if (item.sopFlowNo === sopFlowNo) {
      selCell = item;
    }
  });
  return selCell;
};
// [課題426] ADD ST ブロック、テンプレートDBから取得する時、数字で前後関係を構築します。
/**
 * ブロック中のノードをフローに接続する
 * @param array
 * 呼び出しメソッド getSOPFlowData
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const flowListUpdate = (array: any[]) => {
  let returnData = [];
  // TODO ST バックエンドの対応完了したら、この部分を削除できます。
  returnData = array.reduce(
    (cleanList, current) => {
      const key = `${current.sopNodeNo}-${current.sopJoin.nextCondSeq}-${current.sopJoin.nextNodeNo}`;
      if (!cleanList.seen[key]) {
        cleanList.result.push(current);
        // eslint-disable-next-line no-param-reassign
        cleanList.seen[key] = true;
      }
      return cleanList;
    },
    { result: [], seen: {} },
  ).result;
  // TODO ED バックエンドの対応完了したら、この部分を削除できます。
  returnData = returnData.reduce(
    (
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      dataItem: any[],
      current: {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        sopNodeNo: any;
        sopJoin: {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          nextCondSeq: any;
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          nextNodeNo: any;
        };
      },
    ) => {
      // 条件分岐のnextNodeNoを結合します。
      const existingItem = dataItem.find(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (item: { sopNodeNo: any }) => item.sopNodeNo === current.sopNodeNo,
      );
      if (existingItem) {
        if (existingItem.sopJoin.nextCondSeq < current.sopJoin.nextCondSeq) {
          existingItem.sopJoin.nextNodeNo = `${existingItem.sopJoin.nextNodeNo},${current.sopJoin.nextNodeNo}`;
        } else {
          existingItem.sopJoin.nextNodeNo = `${current.sopJoin.nextNodeNo},${existingItem.sopJoin.nextNodeNo}`;
        }
      } else {
        dataItem.push(current);
      }
      return dataItem;
    },
    [],
  );
  returnData.forEach((item: { sopJoin: { nextNodeNo: string } }) => {
    const branchNodeIdList = item.sopJoin.nextNodeNo.split(',');
    // @ts-expect-error nextNodeNoを初期化
    // eslint-disable-next-line no-param-reassign
    item.sopJoin.nextNodeNo = {};
    branchNodeIdList.forEach(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (branchItem: { toString: () => any }, index: number) => {
        if (branchItem) {
          // @ts-expect-error この形でnextNodeNoの値を取得します
          // eslint-disable-next-line no-param-reassign
          item.sopJoin.nextNodeNo[`condBrDst${index + 1}`] =
            branchItem.toString();
        } else {
          // eslint-disable-next-line no-param-reassign
          item.sopJoin.nextNodeNo = '';
        }
      },
    );
    // eslint-disable-next-line no-param-reassign
    item.sopJoin.nextNodeNo = JSON.stringify(item.sopJoin.nextNodeNo);
  });
  return returnData;
};
// [課題426] ADD ED
/**
 * 選択されたテンプレート／ブロック情報を取得
 */
const getBlockItemSelect = async (item: SopBlockOption) => {
  const apiRequestData: GetSopFlowData = { sopFlowNo: item.sopFlowNo };
  const { responseRef, errorRef } = await useGetSopFlowData({
    ...props.commonRequest,
    ...apiRequestData,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButtonRef');
    return;
  }
  if (responseRef.value) {
    let flowList = responseRef.value.data.rData.rList;
    if (flowList.length > 0) {
      state.SOPBlockOption = item;
      state.selectFlowNo = item.sopFlowNo;
      state.selectItemName = item.sopCatTxtJp;
      state.localeSelectGraph!.clearCells();
      const localeData = getLocaleGraphSize(
        state.screenWidthVal,
        state.screenHeightVal,
      );
      if (flowList.length > 0) {
        flowList = flowListUpdate(JSON.parse(JSON.stringify(flowList)));
        // [課題399] ADD ST 新しいListを作ります。ブロックのノードを保存します。
        selectList = JSON.parse(JSON.stringify(flowList));
        // [課題399] ADD ED
        setSOPFlowChart(flowList, flowList[0].sopNodeNo);
      }
      state.localeSelectGraph!.resize(
        localeData.localeWidth,
        localeData.localeHeight,
      );
      state.localeSelectGraph!.zoomToFit({ maxScale: 0.6 });
      state.localeSelectGraph!.centerContent();
    }
  } else {
    messageBoxSingleButtonRef.value.content = t('Tt.Chr.txtNoDataSet');
    messageBoxSingleButtonRef.value.type = 'info';
    openDialog('singleButtonRef');
    state.blockList = [];
  }
};
/**
 * キャンバスの新規作成
 */
const createNewGraph = async () => {
  // すべてのノードタイプをビルドする
  state.SOPSetData = getSOPSetting(t);
  state.imageMap = getImageMap(state.images);
  const localeData = getLocaleGraphSize(
    state.screenWidthVal,
    state.screenHeightVal,
  );
  if (!boxChardGraph.value) {
    return;
  }
  const LocaleSelectGraph = new Graph({
    container: boxChardGraph.value,
    width: localeData.localeWidth,
    height: localeData.localeHeight,
    translating: {
      restrict: true,
    },
    connecting: {
      router: 'manhattan',
      connector: {
        name: 'rounded',
        args: {
          radius: 28,
        },
      },
      anchor: 'center',
      connectionPoint: 'anchor',
      allowBlank: false,
      snap: {
        radius: 20,
        anchor: 'center',
      },
      createEdge() {
        return new Shape.Edge({
          attrs: {
            line: {
              stroke: '#A2B1C3',
              strokeWidth: 2,
              targetMarker: {
                name: 'block',
                width: 12,
                height: 8,
              },
            },
          },
          zIndex: 3,
        });
      },
      validateConnection({ targetMagnet }) {
        return !!targetMagnet;
      },
    },
    interacting: {
      nodeMovable() {
        return false;
      },
    },
  });

  // DBからテンプレートデータ取得
  await getAllBlockList();
  state.localeSelectGraph = LocaleSelectGraph;
  state.selectID = '';
};
/**
 * テンプレートグラフを描画
 */
const createSOPFlow = async () => {
  if (state.selectID === '') {
    return false;
  }
  const selOption = getSelectedBlock(state.selectID);
  await getBlockItemSelect(selOption);
  return true;
};
/**
 * ブロックの削除処理
 * @param {*}  flowList - Flow Chart data
 */
const deleteSopBlock = async (flowNo: string, updateDate: string) => {
  closeDialog('sopDeleteConfirmRef');
  const apiRequestData: DeleteSopBlock = {
    sopFlowNo: flowNo,
    updDts: updateDate,
  };
  const { responseRef, errorRef } = await useDeleteSopBlock({
    ...props.commonRequest,
    btnId: 'btnDelate',
    ...apiRequestData,
  });
  if (errorRef.value) {
    messageBoxSopDeleteRef.value.title = errorRef.value.response.rTitle;
    messageBoxSopDeleteRef.value.content = errorRef.value.response.rMsg;
    messageBoxSopDeleteRef.value.type = 'error';
    openDialog('sopDeleteRef');
    return;
  }
  if (responseRef.value?.data.rCode === CONST.API_STATUS_CODE.SUCCESS) {
    messageBoxSopDeleteRef.value.title = responseRef.value.data.rTitle;
    messageBoxSopDeleteRef.value.content = responseRef.value.data.rMsg;
    messageBoxSopDeleteRef.value.type = 'info';
    openDialog('sopDeleteRef');
  }
  state.localeSelectGraph!.clearCells();
};
/**
 * テンプレート初期描画
 */
watch(
  () => props.SOPSelectFlag,
  (newOC: Props['SOPSelectFlag']) => {
    if (newOC) {
      state.SOPChartDialogVisible = true;
      state.blockType = props.SOPBlockType;
      state.blockNo = props.SOPblockNo;
      state.screenWidthVal = props.screenWidth;
      state.screenHeightVal = props.screenHeight;
      state.blockList = [];
      state.SOPBlockOption = {
        sopFlowNo: '',
        sopFlowNmJp: '',
        sopCatTxtJp: '',
        untSopCat: props.SOPBlockType,
        updDts: '',
      };
      nextTick(() => {
        createNewGraph();
      });
    }
  },
  { deep: true },
);
/**
 * 選択されたテンプレートを描画
 */
watch(
  () => state.selectID,
  (newValue) => {
    if (newValue) {
      // フロー描画
      createSOPFlow();
    }
  },
  { deep: true },
);
/**
 * screen Width変更の場合は実行
 */
watch(
  () => props.screenWidth,
  (newValue: Props['screenWidth']) => {
    if (newValue) {
      state.screenWidthVal = newValue;
      state.screenHeightVal = props.screenHeight;
      setLocaleGraphSize(newValue, props.screenHeight);
    }
  },
  { deep: true },
);
/**
 * screen Height変更の場合は実行
 */
watch(
  () => props.screenHeight,
  (newValue: Props['screenHeight']) => {
    if (newValue) {
      state.screenWidthVal = props.screenWidth;
      state.screenHeightVal = newValue;
      setLocaleGraphSize(props.screenWidth, newValue);
    }
  },
  { deep: true },
);
/**
 * Dialog Min Width
 */
const getDialogMinWidth = () => {
  const minWidth = 1280 * 0.7;
  const minWidthStr: string = `min-width:${minWidth}px`;
  return minWidthStr;
};
/**
 * [キャンセル] ボタン
 */
const SOPAddCancel = () => {
  state.SOPChartDialogVisible = false;
  state.selectFlowNo = '';
  state.selectItemName = '';
  state.localeSelectGraph!.clearCells();
  const selectType: string = 'CANCEL';
  const selectData: SopSelectBlock = {
    ...state.SOPBlockOption,
    nodes: [],
    edges: [],
    commonSettings: [],
    individualParas: [],
    helpSettings: undefined,
    upperLowerSetting: [],
  };
  emit('SOPSelectVisable', {
    type: selectType,
    data: selectData,
    block: state.blockType,
  });
  if (props.SOPBlockType === 'B') {
    emit('deleteBlockNode');
  }
};
/**
 * [削除] ボタンクリックイベント
 */
const SOPDelete = () => {
  const selOption = getSelectedBlock(state.selectID);
  if (selOption.sopFlowNo === '') {
    messageBoxSingleButtonRef.value.title = `${t('SOP.Chr.txtSopTempDelete')}`;
    messageBoxSingleButtonRef.value.content = `${t('SOP.Msg.unSelectTemplate')}`;
    openDialog('singleButtonRef');
    return;
  }
  let confirmContent = '';
  if (state.blockType === 'T') {
    confirmContent = `${t('SOP.Chr.txtSopTemplate')}`;
  } else {
    confirmContent = `${t('SOP.Chr.txtSopBlock')}`;
  }
  messageBoxSopDeleteConfirmRef.value.title = `${t('SOP.Chr.txtSopTempDelete')}`;
  messageBoxSopDeleteConfirmRef.value.content = t('SOP.Msg.deleteTemplate', [
    confirmContent,
  ]);
  openDialog('sopDeleteConfirmRef');
};
/**
 * [OK] ボタンクリックイベント
 */
const SOPAddSave = () => {
  if (selectList.length === 0) {
    messageBoxSingleButtonRef.value.title = `${props.SOPBlockType === 'T' ? t('SOP.Chr.txtSopNoTempSelected') : t('SOP.Chr.txtSopNoBlockSelected')}`;
    messageBoxSingleButtonRef.value.content = `${props.SOPBlockType === 'T' ? t('SOP.Msg.noSelectedTemp') : t('SOP.Msg.noSelectedBlock')}`;
    openDialog('singleButtonRef');
    return;
  }
  state.SOPChartDialogVisible = false;
  state.selectItemName = '';
  const selectType: string = 'SAVE';
  const selectData: SopSelectBlock = {
    ...state.SOPBlockOption,
    nodes: [],
    edges: [],
    commonSettings: [],
    individualParas: [],
    upperLowerSetting: [],
    // [課題外] ADD ST ブロックとテンプレートをグラフに追加する時に、一部のパラメータをアサインします。
    helpSettings: [],
    // [課題外] ADD ED
  };
  const nodeData: Node[] = state.localeSelectGraph!.getNodes();
  const edgeData: Edge[] = state.localeSelectGraph!.getEdges();
  selectData.nodes = nodeData;
  selectData.edges = edgeData;
  nodeData.forEach((node) => {
    const commonSetting = node.getProp('commonSetting');
    const individualPara = node.getProp('individualPara');
    // [課題外] ADD ST ブロックとテンプレートをグラフに追加する時に、一部のパラメータをアサインします。
    const helpSetting = node.getProp('helpSetting');
    const upperLowerSetting = node.getProp('upperLowerSetting');
    selectData.commonSettings.push(commonSetting || {});
    selectData.individualParas.push(individualPara || '{}');
    selectData.helpSettings.push(helpSetting || {});
    selectData.upperLowerSetting.push(upperLowerSetting || {});
    // [課題外] ADD ED
  });
  state.localeSelectGraph!.clearCells();
  emit('SOPSelectVisable', {
    type: selectType,
    data: selectData,
    block: state.blockType,
    name: state.activeName,
    // [課題399] ADD ST ブロックのノードを保存した後、親componentにemit。
    selectList,
    // [課題399] ADD ED
  });
};
/**
 * テンプレート選択イベント
 */
const handleNodeClick = (data: Tree) => {
  const setData = data;
  // 選択IDをセットする
  if (setData.children === undefined) {
    state.selectID = data.id.toString();
    state.activeName = data.label;
  }
};
</script>
<style lang="scss" scoped>
.box-card-main {
  display: flex;
  .box-card-left {
    float: left;
    width: 300px;
    height: 600px;
    padding: 5px;
    .block-list-main {
      width: 100%;
      height: 600px;
      border: 1px solid #9bb3e2;
      .selected {
        background: #9bb3e2;
      }
    }
    .block-item-div {
      padding: 5px 10px;
    }
  }
  .box-card-right {
    float: left;
    width: calc(100% - 320px);
    height: 600px;
    padding: 5px;
    .select-block-name {
      width: 100%;
      height: 30px;
      line-height: 30px;
      text-align: center;
    }
    .select-block-main {
      border: 1px solid #9bb3e2;
      width: 100%;
      min-height: 600px;
      position: relative;
      color: #000000;
    }
    .select-block-edit {
      width: 100%;
      height: 210px;
      display: flex;
      .block-edit-left {
        float: left;
        width: calc(100% - 300px);
        height: 100%;
      }
      .block-edit-right {
        float: left;
        width: 300px;
        height: 100%;
      }
      .block-edit-right-title {
        height: 38px;
        line-height: 38px;
        text-align: center;
        border-left: 2px solid #dcdfe6;
        border-bottom: 2px solid #dcdfe6;
      }
      .block-edit-right-main {
        width: 398px;
        height: calc(100% - 40px);
        border-left: 2px solid #dcdfe6;
      }
      .demo-tabs-div {
        width: calc(100% - 20px);
        height: calc(100% - 20px);
        padding: 10px;
      }
    }
  }
}
.dialog-footer {
  height: 20px;
  margin-bottom: 10px;
}
.dialog-footer-left {
  float: left;
}
.dialog-footer-center {
  width: 200px;
  margin-left: auto;
  margin-right: auto;
}
.dialog-footer-right {
  float: right;
}
</style>

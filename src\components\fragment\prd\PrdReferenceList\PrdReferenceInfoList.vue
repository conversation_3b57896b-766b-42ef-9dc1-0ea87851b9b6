<template>
  <div class="prd-reference-info-list-dialog">
    <!-- 製造記録参照_記録詳細ダイアログ -->
    <DialogWindow
      :title="$t('Prd.Chr.txtReferenceList')"
      :dialogVisible="dialogVisibleRef.prdReferenceInfoListDialog"
      :buttons="[
        {
          type: 'secondary',
          size: 'normal',
          text: $t('Cm.Chr.btnCancel'),
        },
      ]"
      :width="OVERRIDE_DIALOG_WIDTH"
      @closeDialog="() => closeDialog('prdReferenceInfoListDialog')"
    >
      <!-- 見出し 製造指図情報 -->
      <BaseHeading
        level="2"
        :text="$t('Prd.Chr.txtOrderInformation')"
        fontSize="24px"
      />
      <!-- 製造指図情報の見出し+テキスト項目表示 -->
      <InfoShow
        class="Util_mt-16"
        :infoShowItems="productReferenceInfoShowRef.infoShowItems"
        :isLabelVertical="productReferenceInfoShowRef.isLabelVertical"
      />
      <div class="prd-reference-info-list-dialog_box-card-margin">
        <!-- 秤量記録エリア -->
        <div class="prd-reference-info-list-dialog_button-box">
          <span class="prd-reference-info-list-dialog_button-area-text">{{
            t('Prd.Chr.txtWeightRecord')
          }}</span>
          <div class="prd-reference-info-list-dialog_button-border-box">
            <div class="prd-reference-info-list-dialog_button-area">
              <div>
                <!-- 確認ボタン -->
                <ButtonEx
                  class="prd-reference-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnCheck')"
                  @click="clickWeightingRecBtn()"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- SOP記録エリア -->
        <div class="prd-reference-info-list-dialog_button-box">
          <span class="prd-reference-info-list-dialog_button-area-text">{{
            t('Prd.Chr.txtSopRec')
          }}</span>
          <div class="prd-reference-info-list-dialog_button-border-box">
            <div class="prd-reference-info-list-dialog_button-area">
              <div>
                <!-- 確認ボタン -->
                <ButtonEx
                  class="prd-reference-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnCheck')"
                  @click="clickSopRecordBtn()"
                />
              </div>
              <div>
                <!-- 修正履歴ボタン -->
                <ButtonEx
                  class="prd-reference-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnFixHistory')"
                  @click="clickSopRecModHistoryBtn()"
                />
              </div>
              <div>
                <!-- 異状履歴ボタン -->
                <ButtonEx
                  class="prd-reference-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnDeviationHistory')"
                  @click="clickSopRecDevHistoryBtn()"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- 投入実績エリア -->
        <div class="prd-reference-info-list-dialog_button-box">
          <span class="prd-reference-info-list-dialog_button-area-text">{{
            t('Prd.Chr.txtActualInput')
          }}</span>
          <div class="prd-reference-info-list-dialog_button-border-box">
            <div class="prd-reference-info-list-dialog_button-area">
              <div>
                <!-- 確認ボタン -->
                <ButtonEx
                  class="prd-reference-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnCheck')"
                  @click="clickBomListBtn()"
                />
              </div>
              <div>
                <!-- 修正履歴ボタン -->
                <ButtonEx
                  class="prd-reference-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnFixHistory')"
                  @click="clickInputRsltModHistoryBtn()"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- 出来高エリア -->
        <div class="prd-reference-info-list-dialog_button-box">
          <span class="prd-reference-info-list-dialog_button-area-text">{{
            t('Prd.Chr.txtProduction')
          }}</span>
          <div class="prd-reference-info-list-dialog_button-border-box">
            <div class="prd-reference-info-list-dialog_button-area">
              <div>
                <!-- 確認ボタン -->
                <ButtonEx
                  class="prd-reference-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnCheck')"
                  @click="clickProductListBtn()"
                />
              </div>
              <div>
                <!-- 修正履歴ボタン -->
                <ButtonEx
                  class="prd-reference-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnFixHistory')"
                  @click="clickProductModHistoryBtn()"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- 作業工数エリア -->
        <div class="prd-reference-info-list-dialog_button-box">
          <span class="prd-reference-info-list-dialog_button-area-text">{{
            t('Prd.Chr.txtWorkCost')
          }}</span>
          <div class="prd-reference-info-list-dialog_button-border-box">
            <div class="prd-reference-info-list-dialog_button-area">
              <div>
                <!-- 確認ボタン -->
                <ButtonEx
                  class="prd-reference-info-list-dialog_button-margin"
                  type="secondary"
                  size="normal"
                  :text="t('Prd.Chr.btnCheck')"
                  @click="clickPrdApprovalWorkButton()"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 枠外下部ボタンエリア -->
      <div class="prd-reference-info-list-dialog_button-area-bottom">
        <!-- 左側ボタン -->
        <div class="prd-reference-info-list-dialog_button-area-bottom-list">
          <!-- コメント確認 -->
          <ButtonEx
            type="secondary"
            size="normal"
            :text="t('Prd.Chr.btnCommentConfirm')"
            @click="clickCommentHistoryBtn()"
          />
          <!-- 記録書再出力 -->
          <ButtonEx
            type="secondary"
            size="normal"
            :text="t('Prd.Chr.btnRecordRePrint')"
            @click="clickPrintBtn()"
          />
        </div>
        <!-- 右側ボタン -->
        <div class="prd-reference-info-list-dialog_button-area-bottom-list">
          <!-- 記録承認取消 -->
          <ButtonEx
            type="dangerSecond"
            size="normal"
            :text="t('Prd.Chr.btnRecordApprovalCancel')"
            @click="clickRecordApprovalCancelBtn()"
          />
        </div>
      </div>
    </DialogWindow>
    <!-- 秤量記録ダイアログ -->
    <PrdApprovalWeighing
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowWeightingRecDialogRef"
      :odrNo="getOdrNo()"
      :infoData="initResponseData"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
      :dspNarrowType="PRD_APPROVAL_WEIGHING_NARROW_TYPE.REFERENCE"
    />
    <!-- SOP記録ダイアログ -->
    <PrdApprovalSOPFlowList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowSopRecordDialogRef"
      :selectedRow="selectedRow"
      :infoData="initResponseData"
      :routerName="props.routerName"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
      :dspNarrowType="PRD_APPROVAL_SOP_FLOW_LIST_NARROW_TYPE.REFERENCE"
    />
    <!-- SOP修正履歴ダイアログ -->
    <PrdModifyLogList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowSopRecordModifyHistoryDialogRef"
      :odrNo="getOdrNo()"
      :dspNarrowType="MODIFY_NARROW_TYPE.REFERENCE"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
    />
    <!-- 異状確認履歴ダイアログ -->
    <PrdApprovalDeviantList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowPrdApprovalDeviantListDialogRef"
      :selectedRow="selectedRow"
      :infoData="initResponseData"
      :routerName="props.routerName"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
      :dspNarrowType="PRD_APPROVAL_DEVIANT_LIST_NARROW_TYPE.REFERENCE"
    />
    <!-- 投入実績修正履歴ダイアログ -->
    <PrdBomModifyList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowBomModifyHistoryDialogRef"
      :odrNo="getOdrNo()"
      :dspNarrowType="BOM_LOG_NARROW_TYPE.REFERENCE"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
    />
    <!-- 出来高修正履歴ダイアログ -->
    <PrdProductModifyList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowPrdModifyHistoryDialogRef"
      :odrNo="getOdrNo()"
      :dspNarrowType="PRD_LOG_NARROW_TYPE.REFERENCE"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
    />
    <!-- コメント確認ダイアログ -->
    <PrdCommentLogList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowCommentHistoryDialogRef"
      :odrNo="getOdrNo()"
      :dspNarrowType="COMMENT_NARROW_TYPE.REFERENCE"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
    />
    <!-- 製造記録承認 投入実績ダイアログ -->
    <PrdBomList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowBomListDialogRef"
      :orderDetailInfo="productReferenceInfoShowItems"
      :odrNo="getOdrNo()"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
      :routerName="props.routerName"
      :dspNarrowType="PRDBOMLIST_NARROW_TYPE.REFERENCE"
    />
    <!-- 製造記録承認 出来高記録ダイアログ -->
    <PrdProductList
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowProductListDialogRef"
      :orderDetailInfo="productReferenceInfoShowItems"
      :odrNo="getOdrNo()"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
      :routerName="props.routerName"
      :dspNarrowType="PRDPRODUCTLIST_NARROW_TYPE.REFERENCE"
    />
    <!-- 製造記録承認 記録書再出力ダイアログ -->
    <PrdReferencePrint
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedShowPrintDialogRef"
      :odrNo="getOdrNo()"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
    />
    <!-- 製造記録承認_作業工数ダイアログ -->
    <PrdApprovalWork
      v-if="props.privilegesBtnRequestData"
      :isClicked="isClickedPrdApprovalWorkDialogRef"
      :odrNo="getOdrNo()"
      :prcSeq="getPrcSeq()"
      :accgYmd="initResponseData.accgYmd"
      :privilegesBtnRequestData="props.privilegesBtnRequestData"
      :dspNarrowType="PRD_APPROVAL_WORK_NARROW_TYPE.REFERENCE"
    />
    <!-- APIのエラー表示 -->
    <MessageBox
      v-if="dialogVisibleRef.messageBoxApiErrorVisible"
      :dialogProps="messageBoxApiErrorPropsRef"
      :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
    />
    <!-- 記録承認取消の確認表示 -->
    <MessageBox
      v-if="dialogVisibleRef.messageBoxRecordApprovalCancelVisible"
      :dialogProps="messageBoxRecordApprovalCancelPropsRef"
      :cancelCallback="
        () => closeDialog('messageBoxRecordApprovalCancelVisible')
      "
      :submitCallback="requestApiRecordApprovalCancel"
    />
    <!-- 記録承認取消の完了表示 -->
    <MessageBox
      v-if="dialogVisibleRef.messageBoxRecordApprovalCancelCompleteVisible"
      :dialogProps="messageBoxRecordApprovalCancelCompletePropsRef"
      :submitCallback="closeDialogFromMessageBoxRecordApprovalCancelComplete"
    />
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import { closeLoading, showLoading } from '@/utils/dialog';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import {
  MODIFY_NARROW_TYPE,
  BOM_LOG_NARROW_TYPE,
  PRD_LOG_NARROW_TYPE,
  COMMENT_NARROW_TYPE,
  PRDBOMLIST_NARROW_TYPE,
  PRDPRODUCTLIST_NARROW_TYPE,
  PRD_APPROVAL_DEVIANT_LIST_NARROW_TYPE,
  PRD_APPROVAL_WEIGHING_NARROW_TYPE,
  PRD_APPROVAL_SOP_FLOW_LIST_NARROW_TYPE,
  PRD_APPROVAL_WORK_NARROW_TYPE,
  GetReferenceInfoResponseData,
  GetReferenceInfoInitRequestData,
  GetReferenceInfoInitResponseData,
  ModifyReferenceApprovalRequestData,
} from '@/types/HookUseApi/PrdTypes';
import {
  useGetReferenceInfoInit,
  useModifyReferenceApproval,
  useGetComboBoxDataStandard,
} from '@/hooks/useApi';
import BaseHeading from '@/components/base/BaseHeading.vue';
import showSignDialog from '@/components/model/SignDialog/SignDialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import createMessageBoxForm from '@/utils/commentMessageBox';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import ButtonEx from '@/components/parts/ButtonEx.vue';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import PrdApprovalWeighing from '@/components/include/prd/PrdApprovalWeighing.vue';
import PrdApprovalSOPFlowList from '@/components/include/prd/PrdApprovalSOPFlowList.vue';
import PrdApprovalDeviantList from '@/components/include/prd/PrdApprovalDeviantList.vue';
import PrdModifyLogList from '@/components/include/prd/PrdModifyLogList.vue';
import PrdBomModifyList from '@/components/include/prd/PrdBomModifyList.vue';
import PrdProductModifyList from '@/components/include/prd/PrdProductModifyList.vue';
import PrdCommentLogList from '@/components/include/prd/PrdCommentLogList.vue';
import PrdBomList from '@/components/include/prd/PrdBomList.vue';
import PrdProductList from '@/components/include/prd/PrdProductList.vue';
import PrdApprovalWork from '@/components/include/prd/PrdApprovalWork.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import { InfoShowType, InfoShowItem } from '@/types/InfoShowTypes';
import PrdReferencePrint from '@/components/fragment/prd/PrdReferenceList/PrdReferencePrint.vue';
import getProductReferenceInfoShowItems from './prdReferenceInfoList';

/**
 * 多言語
 */
const { t } = useI18n();

// NOTE:ボタン配置が特殊なため、デフォルトのダイアログ幅を使用せず、上書きします。
const OVERRIDE_DIALOG_WIDTH = '1000px';

const messageBoxForm = createMessageBoxForm('message', 'cmtWarning');

// 製造記録参照_記録詳細初期表示のレスポンスデータ
let initResponseData: GetReferenceInfoInitResponseData = {
  matNo: '',
  dspNmJp: '',
  rxNmJp: '',
  lotNo: '',
  prcSeq: null,
  rsltQty: '',
  odrStYmd: '',
  rsltDts: '',
  accgYmd: '',
  yieldVal: '',
  recConfirmDts: '',
  recConfirmUsr: '',
  recApprovDts: '',
  recApprovUsr: '',
  recDocVer: '',
  recVerifyFlg: false,
  updDts: '',
};

const productReferenceInfoShowRef = ref<InfoShowType>({
  infoShowItems: getProductReferenceInfoShowItems(),
  isLabelVertical: true,
});

// 製造記録参照 ダイアログ用 製造指図情報表示用データ
let productReferenceInfoShowItems: Record<string, InfoShowItem>;

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 記録承認取消の確認メッセージボックス
const messageBoxRecordApprovalCancelPropsRef = ref<DialogProps>({
  title: t('Prd.Msg.titleOrderRecordApprovalCancel'),
  content: t('Prd.Msg.contentOrderRecordApprovalCancel'),
  isPrompt: true,
  formModel: messageBoxForm.formModel,
  formItems: messageBoxForm.formItems,
  type: 'warning',
});

// 記録承認取消の完了メッセージボックス
const messageBoxRecordApprovalCancelCompletePropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});

// 入力付き確認メッセージボックスの入力を初期化
const clearInputMessageBoxForm = () => {
  if ('isPrompt' in messageBoxRecordApprovalCancelPropsRef.value) {
    messageBoxRecordApprovalCancelPropsRef.value.formItems.message.formModelValue =
      '';
  }
};

// ダイアログの表示切替用定義
const initialState: InitialDialogState<DialogRefKey> = {
  prdReferenceInfoListDialog: false,
  messageBoxApiErrorVisible: false,
  messageBoxRecordApprovalCancelVisible: false,
  messageBoxRecordApprovalCancelCompleteVisible: false,
  messageBoxRecordApprovalVisible: false,
  messageBoxRecordApprovalCompleteVisible: false,
};

const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);

type DialogRefKey =
  | 'prdReferenceInfoListDialog'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxRecordApprovalCancelVisible'
  | 'messageBoxRecordApprovalCancelCompleteVisible'
  | 'messageBoxRecordApprovalVisible'
  | 'messageBoxRecordApprovalCompleteVisible';

type Props = {
  selectedRow: GetReferenceInfoResponseData | null; // 遷移元選択行
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  routerName: string; // TabulatorTable権限
  privilegesBtnRequestData: CommonRequestType;
};
const props = defineProps<Props>();

const emit = defineEmits(['submit']);

// 秤量記録確認ボタン' クリック
const isClickedShowWeightingRecDialogRef = ref<boolean>(false);
// 'SOP記録確認ボタン' クリック
const isClickedShowSopRecordDialogRef = ref<boolean>(false);
// 'SOP記録修正履歴ボタン' クリック
const isClickedShowSopRecordModifyHistoryDialogRef = ref<boolean>(false);
// 'SOP異状履歴ボタン' クリック
const isClickedShowPrdApprovalDeviantListDialogRef = ref<boolean>(false);
// '投入実績修正履歴ボタン' クリック
const isClickedShowBomModifyHistoryDialogRef = ref<boolean>(false);
// '出来高修正履歴ボタン' クリック
const isClickedShowPrdModifyHistoryDialogRef = ref<boolean>(false);
// 'コメント確認ボタン' クリック
const isClickedShowCommentHistoryDialogRef = ref<boolean>(false);
// '投入実績確認ボタン' クリック
const isClickedShowBomListDialogRef = ref<boolean>(false);
// '出来高記録確認ボタン' クリック
const isClickedShowProductListDialogRef = ref<boolean>(false);
// '記録書再出力ボタン' クリック
const isClickedShowPrintDialogRef = ref<boolean>(false);
// '作業工数確認ボタン' クリック
const isClickedPrdApprovalWorkDialogRef = ref<boolean>(false);

const getOdrNo = () => {
  if (props.selectedRow === null) {
    return undefined;
  }

  return props.selectedRow.odrNo;
};
const getPrcSeq = () => {
  if (initResponseData.prcSeq === null) {
    return 0;
  }

  return initResponseData.prcSeq;
};

/**
 * 秤量記録確認ボタン押下時
 */
const clickWeightingRecBtn = () => {
  isClickedShowWeightingRecDialogRef.value =
    !isClickedShowWeightingRecDialogRef.value;
};

/**
 * SOP記録確認ボタン押下時
 */
const clickSopRecordBtn = () => {
  isClickedShowSopRecordDialogRef.value =
    !isClickedShowSopRecordDialogRef.value;
};

/**
 * SOP修正履歴ボタン押下時
 */
const clickSopRecModHistoryBtn = () => {
  isClickedShowSopRecordModifyHistoryDialogRef.value =
    !isClickedShowSopRecordModifyHistoryDialogRef.value;
};

/**
 * 投入実績修正履歴ボタン押下時
 */
const clickInputRsltModHistoryBtn = () => {
  isClickedShowBomModifyHistoryDialogRef.value =
    !isClickedShowBomModifyHistoryDialogRef.value;
};

/**
 * 出来高修正履歴ボタン押下時
 */
const clickProductModHistoryBtn = () => {
  isClickedShowPrdModifyHistoryDialogRef.value =
    !isClickedShowPrdModifyHistoryDialogRef.value;
};

/**
 * SOP異状履歴ボタン押下時
 */
const clickSopRecDevHistoryBtn = () => {
  isClickedShowPrdApprovalDeviantListDialogRef.value =
    !isClickedShowPrdApprovalDeviantListDialogRef.value;
};

/**
 * コメント確認ボタン押下時
 */
const clickCommentHistoryBtn = () => {
  isClickedShowCommentHistoryDialogRef.value =
    !isClickedShowCommentHistoryDialogRef.value;
};

/**
 * 投入実績確認ボタン押下時
 */
const clickBomListBtn = () => {
  // 製造記録承認 投入実績ダイアログ用 製造指図情報表示用データをセット
  productReferenceInfoShowItems =
    productReferenceInfoShowRef.value.infoShowItems;

  isClickedShowBomListDialogRef.value = !isClickedShowBomListDialogRef.value;
};

/**
 * 出来高記録確認ボタン押下時
 */
const clickProductListBtn = () => {
  // 製造記録承認 出来高記録ダイアログ用 製造指図情報表示用データをセット
  productReferenceInfoShowItems =
    productReferenceInfoShowRef.value.infoShowItems;

  isClickedShowProductListDialogRef.value =
    !isClickedShowProductListDialogRef.value;
};

/**
 * 記録書再出力ボタン押下時
 */
const clickPrintBtn = () => {
  // 製造記録承認 出来高記録ダイアログ用 製造指図情報表示用データをセット
  productReferenceInfoShowItems =
    productReferenceInfoShowRef.value.infoShowItems;

  isClickedShowPrintDialogRef.value = !isClickedShowPrintDialogRef.value;
};

/**
 * 記録承認取消ボタン押下時
 */
const clickRecordApprovalCancelBtn = () => {
  if (props.selectedRow === null) {
    return false;
  }
  if (props.selectedRow.odrNo === '') {
    return false;
  }
  // 記録承認取消の確認メッセージ表示
  clearInputMessageBoxForm(); // 開く前に入力フォームを初期化
  openDialog('messageBoxRecordApprovalCancelVisible');
  return false;
};

/**
 * 作業工数確認ボタン押下時
 */
const clickPrdApprovalWorkButton = () => {
  isClickedPrdApprovalWorkDialogRef.value =
    !isClickedPrdApprovalWorkDialogRef.value;
};

/**
 * 記録承認取消押下時処理
 */
const requestApiRecordApprovalCancel = async () => {
  // OK押下でメッセージ不要につき閉じる
  closeDialog('messageBoxRecordApprovalCancelVisible');

  try {
    // 署名ダイアログを表示
    await showSignDialog({
      commonRequestParam: {
        ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
      },
    });
  } catch (error) {
    // 署名ダイアログがキャンセルされた場合の処理
    return;
  }

  if (props.selectedRow === null) {
    return;
  }

  if (!('isPrompt' in messageBoxRecordApprovalCancelPropsRef.value)) {
    return;
  }

  showLoading();

  // 以下のチェックはBEで行う。FEとしてはAPIをリクエストする。
  // 3.下記チェックを行い、チェックOKなら処理継続する。
  const requestData: ModifyReferenceApprovalRequestData = {
    odrNo:
      productReferenceInfoShowRef.value.infoShowItems.odrNo.infoShowModelValue,
    modExpl:
      messageBoxRecordApprovalCancelPropsRef.value.formItems.message.formModelValue.toString(),
    updDts: initResponseData.updDts,
    lotSid: props.selectedRow.lotSid,
  };
  const { responseRef, errorRef } = await useModifyReferenceApproval({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxRecordApprovalCancelPropsRef.value.title,
    msgboxMsgTxt: messageBoxRecordApprovalCancelPropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    msgboxInputCmt:
      messageBoxRecordApprovalCancelPropsRef.value.formItems.message.formModelValue.toString(),
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }

  // ・データベースを更新した後、以下のメッセージを表示する。
  // 記録承認取消完了
  messageBoxRecordApprovalCancelCompletePropsRef.value.title =
    responseRef.value.data.rTitle;
  messageBoxRecordApprovalCancelCompletePropsRef.value.content =
    responseRef.value.data.rMsg;

  closeLoading();

  openDialog('messageBoxRecordApprovalCancelCompleteVisible');
};

/**
 * 製造記録参照_記録詳細ダイアログの初期設定
 */
const prdReferenceInfoListInit = async () => {
  // NOTE:未選択なら処理させない。エラーメッセージはTabulatorTable側で出してくれる。
  if (props.selectedRow === null) return;

  // ローディング表示
  showLoading();

  // 標準コンボボックス取得
  const comboBoxResData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        // 記録承認取消コメント
        cmbId: 'cmtWarning',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'PRD_ODR_APP_DENY' },
      },
    ],
  });

  if (
    'isPrompt' in messageBoxRecordApprovalCancelPropsRef.value &&
    comboBoxResData
  ) {
    // NOTE: 暫定対応 formItemsを都度生成したフォームで上書きして初期化する
    const resetData = createMessageBoxForm('message', 'cmtWarning');
    messageBoxRecordApprovalCancelPropsRef.value.formItems =
      resetData.formItems;

    // NOTE:共通コンボボックス情報を元にコメント付き警告メッセージを設定
    setCustomFormComboBoxOptionList(
      messageBoxRecordApprovalCancelPropsRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }

  // 製造記録参照_記録詳細初期表示APIを呼び出す
  const apiRequestData: GetReferenceInfoInitRequestData = {
    odrNo: props.selectedRow.odrNo,
  };
  const { responseRef, errorRef } = await useGetReferenceInfoInit({
    ...props.privilegesBtnRequestData,
    ...apiRequestData,
  });
  if (errorRef.value) {
    closeLoading();
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }
  if (responseRef.value) {
    const resData = responseRef.value.data;
    initResponseData = resData.rData;
    // 製造指図情報レイアウト用初期値設定
    Object.entries(initResponseData).forEach(([key, value]) => {
      if (key in productReferenceInfoShowRef.value.infoShowItems) {
        productReferenceInfoShowRef.value.infoShowItems[
          key
        ].infoShowModelValue = value?.toString() ?? '';
      }
    });
    productReferenceInfoShowRef.value.infoShowItems.odrNo.infoShowModelValue =
      props.selectedRow.odrNo;
  }

  closeLoading();
  // 問題なければダイアログ起動する
  openDialog('prdReferenceInfoListDialog');
};

// 記録承認取消に紐づくダイアログ閉じる処理
const closeDialogFromMessageBoxRecordApprovalCancelComplete = () => {
  // 再検索用に親に実行を通知
  emit('submit', props.privilegesBtnRequestData);

  // 自身を閉じる
  closeDialog('messageBoxRecordApprovalCancelCompleteVisible');
  closeDialog('prdReferenceInfoListDialog');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    prdReferenceInfoListInit();
  },
);
</script>
<style lang="scss" scoped>
$namespace: 'prd-reference-info-list-dialog';
// NOTE: 製造記録確認、承認、参照で似通った実装をしているため、修正時は要確認
$boxTextFontSize: 18px; // ボックス見出しのフォントサイズ
$boxTextHeight: $boxTextFontSize + 2px; // ボックス見出しの高さ(フォントサイズより少し大きく確保)
$boxLineWidth: 1px; // ボックスの枠線の太さ
// (枠線内)ボックスの幅と高さ
$boxWidth: 180px;
$boxHeight: 200px;
// 枠線付きボックスの幅と高さ
$boxBorderWidth: $boxWidth + $boxLineWidth * 2;
$boxBorderHeight: $boxHeight + $boxLineWidth * 2;
// ボックスエリア単体の幅と高さ(ボックス+見出し)
$boxAreaWidth: $boxBorderWidth;
$boxAreaHeight: $boxBorderHeight + $boxTextHeight; // 見出しが上に付くため足しておく

.#{$namespace} {
  // 画面中央ボックス群
  &_box-card-margin {
    height: $boxAreaHeight; // 各箱エリアの高さと同一
    margin-top: 30px;
    display: flex;
    justify-content: space-between;
  }
  // 箱エリア一つ辺りの設定
  &_button-box {
    width: $boxAreaWidth;
    height: $boxAreaHeight;
    float: left;
    text-align: center !important;
  }
  // 箱枠線
  &_button-border-box {
    width: $boxWidth; // 枠線抜きの幅
    height: $boxHeight; // 枠線抜きの高さ
    border: 1px solid #000000;
  }
  // 箱内ボタンテキスト
  &_button-area-text {
    font-size: $boxTextFontSize;
    display: block;
    height: $boxTextHeight;
  }
  // 箱内ボタンエリアに対する設定
  &_button-area {
    // NOTE:ボタン位置を10pxから始めるためのネガティブマージン
    margin-top: -20px;
  }
  // 箱内ボタンマージン
  &_button-margin {
    margin-top: 30px;
  }
  // 画面下部ボタンエリア
  &_button-area-bottom {
    margin-top: 24px;
    display: flex;
    justify-content: space-between;
  }
  // 画面下部ボタンリスト
  &_button-area-bottom-list {
    display: flex;
  }
}
</style>

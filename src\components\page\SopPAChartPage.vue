<template>
  <div>
    <div class="sop-header-area">
      <div class="sop-header-right">
        <ButtonEx
          type="primary"
          class="btn-margin-size"
          size="normal"
          :text="$t('SOP.Chr.txtSopTemplate')"
          iconName="ok"
          :disabled="false"
          @click="SOPBlockRegistration"
        />
        <ButtonEx
          type="primary"
          class="btn-margin-size"
          size="normal"
          :text="$t('SOP.Chr.txtSopSetting')"
          iconName="ok"
          :disabled="false"
          @click="openDialogSopSetting"
        />
        <ButtonEx
          type="primary"
          class="btn-margin-size"
          size="normal"
          :text="$t('Cm.Chr.btnDecision')"
          iconName="ok"
          :disabled="false"
          @click="SopChartSave"
        />
      </div>
    </div>
    <!-- 動作不安定のため、一旦非表示 -->
    <!--<div class="sop-reset">
      <img
        width="50"
        height="40"
        alt=""
        src="@/assets/icons/svg/icon_setting_1_gray.svg"
        @click="sopReset"
        @keydown.enter="sopReset"
      />
    </div>-->
    <div class="sop-master-area">
      <div ref="graphContainer"></div>
      <el-card
        class="sop-info-area"
        v-if="state.refererScreenId === SCREEN_ID.SOP_RX_LIST"
      >
        <el-row :gutter="20">
          <el-col :span="7" style="text-align: right"
            >{{ $t('SOP.Chr.txtSopFlowNo') }}：</el-col
          >
          <el-col :span="17">{{ state.sopDataRx.sopFlowNo }}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="7" style="text-align: right"
            >{{ $t('SOP.Chr.txtSopFlowNm') }}：</el-col
          >
          <el-col :span="17">{{ state.sopDataRx.sopFlowNmJp }}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="7" style="text-align: right"
            >{{ $t('SOP.Chr.txtMatNm') }}：</el-col
          >
          <el-col :span="17">{{ state.sopDataRx.matNm }}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="7" style="text-align: right"
            >{{ $t('SOP.Chr.txtRxNm') }}：</el-col
          >
          <el-col :span="17">{{ state.sopDataRx.rxNm }}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="7" style="text-align: right"
            >{{ $t('SOP.Chr.txtPrcSeq') }}：</el-col
          >
          <el-col :span="17">{{ state.sopDataRx.prcSeq }}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="7" style="text-align: right"
            >{{ $t('SOP.Chr.txtPrcNm') }}：</el-col
          >
          <el-col :span="17">{{ state.sopDataRx.prcNm }}</el-col>
        </el-row>
      </el-card>
      <el-card
        class="sop-info-area"
        v-if="state.refererScreenId === SCREEN_ID.SOP_PRC_LIST"
      >
        <el-row :gutter="20">
          <el-col :span="7" style="text-align: right"
            >{{ $t('SOP.Chr.txtSopFlowNo') }}：</el-col
          >
          <el-col :span="17">{{ state.sopDataPrc.sopFlowNo }}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="7" style="text-align: right"
            >{{ $t('SOP.Chr.txtSopFlowNm') }}：</el-col
          >
          <el-col :span="17">{{ state.sopDataPrc.sopFlowNmJp }}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="7" style="text-align: right"
            >{{ $t('SOP.Chr.SOPPrcSetting.txtPrcNm') }}：</el-col
          >
          <el-col :span="17">{{ state.sopDataPrc.prcNm }}</el-col>
        </el-row>
      </el-card>
      <el-card
        class="sop-info-area"
        v-if="state.refererScreenId === SCREEN_ID.SOP_WGT_LIST"
      >
        <el-row :gutter="20">
          <el-col :span="7" style="text-align: right"
            >{{ $t('SOP.Chr.txtSopFlowNo') }}：</el-col
          >
          <el-col :span="17">{{ state.sopDataWgt.sopFlowNo }}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="7" style="text-align: right"
            >{{ $t('SOP.Chr.txtSopFlowNm') }}：</el-col
          >
          <el-col :span="17">{{ state.sopDataWgt.sopFlowNmJp }}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="7" style="text-align: right"
            >{{ $t('SOP.Chr.SOPWgtListSetting.txtWgtRoomNo') }}：</el-col
          >
          <el-col :span="17">{{ state.sopDataWgt.wgtRoomNo }}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="7" style="text-align: right"
            >{{ $t('SOP.Chr.SOPWgtListSetting.txtWgtRoom') }}：</el-col
          >
          <el-col :span="17">{{ state.sopDataWgt.wgtRoomNm }}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="7" style="text-align: right"
            >{{ $t('SOP.Chr.SOPWgtListSetting.txtWgtOperationType') }}：</el-col
          >
          <el-col :span="17">{{ state.sopDataWgt.wgtSopCat }}</el-col>
        </el-row>
      </el-card>
      <div class="sop-part-list-area" v-if="state.sopPartListShowType">
        <SopPartListSetting
          :SOPSetData="state.SOPSetData"
          :refererScreenId="state.refererScreenId"
          @sopRectPartSetting="sopRectPartSetting"
          @sopBlockPartSetting="sopBlockPartSetting"
          @sopControlPartSetting="sopControlPartSetting"
        />
      </div>
      <!-- <div class="sop-node-property-area" v-if="state.isSopInfoSettingVisible">
        <div
          class="sop-node-property-close"
          @click="closeRightMenu"
          @keydown.enter="closeRightMenu"
        >
          <SvgIcon class="menu-icon-margin" iconName="close" />
        </div>
      </div> -->
    </div>
    <!-- 各パーツダイアログ -->
    <SopNodePropertySettingDialog
      :isSopInfoSettingDialogVisible="state.isSopInfoSettingDialogVisible"
      :SOPPartSetData="state.SOPPartSetData"
      :sopDataRx="state.sopDataRx"
      :sopNodeNmList="state.sopNodeNmList"
      :screenWidth="state.screenWidth"
      :screenHeight="state.screenHeight"
      :incomingNodes="editIncomingNodesRef"
      :childNodeList="state.childNodeList"
      :commonRequest="state.cmnRequest"
      :refererScreenId="state.refererScreenId"
      :isRxSop="state.isRxSop"
      @datasourceChange="datasourceChange"
      @closeSopInfoSetting="closeSopInfoSetting"
      @resetNodeColor="resetNodeColor"
    />
    <!-- テンプレート選択ダイアログ -->
    <SopChartSelect
      :SOPSelectFlag="state.SOPSelectFlag"
      :SOPBlockType="state.SOPBlockType"
      :SOPblockNo="state.SOPblockNo"
      :SOPFlowType="state.sopFlowType"
      :screenWidth="state.screenWidth"
      :screenHeight="state.screenHeight"
      :commonRequest="state.cmnRequest"
      @SOPSelectVisable="SOPSelectVisable"
      @deleteBlockNode="deleteBlockNode"
    />
    <!-- テンプレート登録ダイアログ -->
    <SopChartEdit
      :SOPAddFlag="state.SOPAddFlag"
      :SOPChartType="state.SOPChartType"
      :chartOptions="state.chartOptions"
      :SOPFlowType="state.sopFlowType"
      :screenWidth="state.screenWidth"
      :screenHeight="state.screenHeight"
      :commonRequest="state.cmnRequest"
      :blockSeleckValue="state.blockSeleckValue"
      @SOPAddVisible="SOPAddVisible"
    />
    <!-- 設定ダイアログ(指図SOP) -->
    <SopRxSettingDialog
      :dialogVisible="dialogVisibleRef.sopRxSettingDialogVisible"
      :commonRequest="state.cmnRequest"
      :sopSetting="state.sopDataRx"
      ref="SopRxSettingDialogRef"
      @closeDialog="
        () => {
          getRxSopFlowByNo(state.sopDataRx.sopFlowNo);
          closeDialog('sopRxSettingDialogVisible');
        }
      "
    />
    <!-- 設定ダイアログ(工程任意SOP) -->
    <SopPrcSettingDialog
      :dialogVisible="dialogVisibleRef.sopPrcSettingDialogVisible"
      :commonRequest="state.cmnRequest"
      :sopSetting="state.sopDataPrc"
      ref="SopPrcSettingDialogRef"
      @closeDialog="
        () => {
          getPrcSopFlowByNo(state.sopDataPrc.sopFlowNo);
          closeDialog('sopPrcSettingDialogVisible');
        }
      "
    />
    <!-- 設定ダイアログ(秤量前後SOP) -->
    <SopWgtSettingDialog
      :dialogVisible="dialogVisibleRef.sopWgtSettingDialogVisible"
      :commonRequest="state.cmnRequest"
      :sopSetting="state.sopDataWgt"
      ref="SopWgtSettingDialogRef"
      @closeDialog="
        () => {
          getWgtSopFlowByNo(state.sopDataWgt.sopFlowNo);
          closeDialog('sopWgtSettingDialogVisible');
        }
      "
    />
  </div>
  <MessageBox
    v-show="dialogVisibleRef.templateButtonRef"
    :dialog-props="messageBoxTemplateButtonRef"
    :cancelCallback="() => closeDialog('templateButtonRef')"
    :submitCallback="() => closeDialog('templateButtonRef')"
  />
  <MessageBox
    v-show="dialogVisibleRef.singleButtonRef"
    :dialog-props="messageBoxSingleButtonRef"
    :cancelCallback="() => closeDialog('singleButtonRef')"
    :submitCallback="() => closeDialog('singleButtonRef')"
  />
  <MessageBox
    v-show="dialogVisibleRef.sopChartSaveRef"
    :dialog-props="messageBoxSopChartSaveRef"
    :cancelCallback="() => closeDialog('sopChartSaveRef')"
    :submitCallback="() => closeDialog('sopChartSaveRef')"
  />
  <MessageBox
    v-show="dialogVisibleRef.cellRemoveRef"
    :dialog-props="messageBoxCellRemoveRef"
    :cancelCallback="() => closeDialog('cellRemoveRef')"
    :submitCallback="
      () => {
        closeDialog('cellRemoveRef');
        nodeDelete(state.removeNodeId);
        nodeDeleteCheck();
      }
    "
  />
  <MessageBox
    v-show="dialogVisibleRef.pageLeaveRef"
    :dialog-props="messageBoxPageLeaveRef"
    :cancelCallback="handleCancel"
    :submitCallback="handlePageLeave"
  />
</template>
<script setup lang="ts">
import stores from '@/store/index';
import { useI18n } from 'vue-i18n';
import { NavigationGuardNext, onBeforeRouteLeave } from 'vue-router';
import { v4 as uuidv4 } from 'uuid';
import { reactive, computed, onMounted, watch, nextTick, ref } from 'vue';
import { Graph, Shape, Node, Cell, Edge, NodeView } from '@antv/x6';
import { Stencil } from '@antv/x6-plugin-stencil';
import { Transform } from '@antv/x6-plugin-transform';
import { Selection } from '@antv/x6-plugin-selection';
import { Snapline } from '@antv/x6-plugin-snapline';
import { Keyboard } from '@antv/x6-plugin-keyboard';
import { Clipboard } from '@antv/x6-plugin-clipboard';
import { History } from '@antv/x6-plugin-history';
import CONST from '@/constants/utils';
import {
  getSOPSetting,
  getImageMap,
  checkPartCode,
  checkBlockPartCode,
  checkControlPartCode,
  checkConditionPartCode,
  checkPartNodeCode,
  // formatSOPFlowEdges,
  getSOPFlowChartData,
  setSOPFlowItemModel,
  convertSopPartsName,
  convertToSeconds,
} from '@/components/model/SopPA/SopChartSetting';
import SopNodePropertySettingDialog from '@/components/model/SopNodePropertyDialog/SopNodePropertySetting.vue';
import SopPartListSetting from '@/components/model/SopPA/SopPartListSetting.vue';
import SopChartSelect from '@/components/model/SopPA/SopChartSelect.vue';
import SopChartEdit from '@/components/model/SopPA/SopChartEdit.vue';
import SopRxSettingDialog from '@/components/fragment/sop/sopPAChartPage/SopRxSettingDialog.vue';
import SopPrcSettingDialog from '@/components/fragment/sop/sopPAChartPage/SopPrcSettingDialog.vue';
import SopWgtSettingDialog from '@/components/fragment/sop/sopPAChartPage/SopWgtSettingDialog.vue';
import Module from '@/constants/scssVariables';
import {
  ImageOption,
  SizeOption,
  PartMenuBranchingProps,
  PartSystemBranchProps,
  NodeProperty,
  SelectOption,
  PositionOption,
  SelectBranchOption,
  SopPartDataSource,
  SopRectPartsOption,
  SopCustomNodeOption,
  SopControlPartOption,
  SopBlockPartOption,
  SopSelectVisableOption,
  // SopFlowEdgeOption,
  ChartOptions,
  NeighborsOption,
  SopJoinDstOption,
  RxSopSettingOption,
  PrcSopSettingOption,
  WgtSopSettingOption,
  PartsNameOption,
  SopFlowGetDataOption,
  PartExternalDeviceProps,
  NumericTextInputChildNodeProps,
  SopConditions,
  SopBranchOption,
  NumericTextInputProps,
  PartButtonBranchProps,
  PartInstructionConfirmProps,
  PartSopTimerProps,
  PartWeightCalibrationProps,
  SopConditionProps,
  NodeIdOption,
  SopFlowDataOption,
} from '@/types/SopDialogInterface';
import svgMenuHome from '@/assets/icons/svg/menuHome.svg';
import svgSopTemplate from '@/assets/icons/svg/sopTemplate.svg';
import svgQuestion from '@/assets/icons/svg/question.svg';
import svgSopBlock from '@/assets/icons/svg/sopBlock.svg';
import svgAdd from '@/assets/icons/svg/add.svg';
import svgWCheck from '@/assets/icons/svg/wCheck.svg';
import svgWrite from '@/assets/icons/svg/write.svg';
import svgAbnormalityLevel1 from '@/assets/icons/svg/abnormalityLevel1.svg';
import svgAbnormalityLevel2 from '@/assets/icons/svg/abnormalityLevel2.svg';
import svgAbnormalityLevel3 from '@/assets/icons/svg/abnormalityLevel3.svg';
import svgAbnormalityLevel4 from '@/assets/icons/svg/abnormalityLevel4.svg';
import svgAbnormalityLevel5 from '@/assets/icons/svg/abnormalityLevel5.svg';
import svgEdit from '@/assets/icons/svg/edit.svg';
import svgTrash from '@/assets/icons/svg/trashBox.svg';
import svgCopy from '@/assets/icons/svg/copy.svg';
import PartNumericTextInput from '@/assets/icons/svg/PartNumericTextInput.svg';
import PartInstructionConfirm from '@/assets/icons/svg/PartInstructionConfirm.svg';
import PartSopTimer from '@/assets/icons/svg/PartSopTimer.svg';
import PartDateRecord from '@/assets/icons/svg/PartDateRecord.svg';
import PartElectronicFile from '@/assets/icons/svg/PartElectronicFile.svg';
import PartReceiveConsumption from '@/assets/icons/svg/PartReceiveConsumption.svg';
import PartResultConfirm from '@/assets/icons/svg/PartResultConfirm.svg';
import svgTestPartButtonBranch from '@/assets/icons/svg/testpartButtonBranch.svg';
import svgTestPartSystemBranch from '@/assets/icons/svg/testpartSystemBranch.svg';
import PartElectronicShelfLabel from '@/assets/icons/svg/PartElectronicShelfLabel.svg';
import PartInventoryConsumption from '@/assets/icons/svg/PartInventoryConsumption.svg';
import svgTestPartUpdDevice from '@/assets/icons/svg/testpartUpdDevice.svg';
import svgTestPartLabelOutput from '@/assets/icons/svg/testpartLabelOutput.svg';
import svgTestPartCommDevice from '@/assets/icons/svg/testpartCommDevice.svg';
import svgTestPartWeighingCalib from '@/assets/icons/svg/testpartWeighingCalib.svg';
import svgTestPartPalletCargo from '@/assets/icons/svg/testpartPalletCargo.svg';
import PartCopyNode from '@/assets/icons/svg/PartCopyNode.svg';
import ButtonEx from '@/components/parts/ButtonEx.vue';
// import SvgIcon from '@/components/base/SvgIcon.vue';
import {
  useInsertSopChart,
  useGetSopFlowData,
  useGetComboBoxDataStandard,
  useSearchSopListByNo,
  useSearchSopPrcListByNo,
  useSearchSopWgtListByNo,
  useDeleteSysExSop,
} from '@/hooks/useApi';
import {
  ComboBoxDataOptionData,
  CommonRequestType,
} from '@/types/HookUseApi/CommonTypes';
import {
  InsertSopChart,
  GetSopFlowData,
  SearchSopListByNo,
  SearchSopPrcListByNo,
  SearchSopWgtListByNo,
  DeleteSysExSop,
} from '@/types/HookUseApi/SopTypes';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { CustomFormType } from '@/types/CustomFormTypes';
import {
  sopSettingDialogFormItems,
  sopSettingDialogFormModel,
} from '@/components/fragment/sop/sopPAChartPage/sopRxSettingDialog';
import {
  sopPrcSettingDialogFormItems,
  sopPrcSettingDialogFormModel,
} from '@/components/fragment/sop/sopPAChartPage/sopPrcSettingDialog';
import {
  sopWgtSettingDialogFormItems,
  sopWgtSettingDialogFormModel,
} from '@/components/fragment/sop/sopPAChartPage/sopWgtSettingDialog';
import SCREEN_ID from '@/constants/screenId';
import SOP_PARTS_VARIABLES from '@/constants/sopPartsVariables';
import { RouterInfoType } from '@/types/RouterTypes';
import { initCommonRequestFromPrivilegesType } from '@/hooks/useApi/util';
import { closeLoading, showLoading } from '@/utils/dialog';
// import { toNumberOrNull } from '@/utils';

type DialogRefKey =
  | 'templateButtonRef'
  | 'singleButtonRef'
  | 'sopChartSaveRef'
  | 'cellRemoveRef'
  | 'pageLeaveRef'
  | 'sopRxSettingDialogVisible'
  | 'sopPrcSettingDialogVisible'
  | 'sopWgtSettingDialogVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  templateButtonRef: false,
  singleButtonRef: false,
  sopChartSaveRef: false,
  cellRemoveRef: false,
  sopRxSettingDialogVisible: false,
  sopPrcSettingDialogVisible: false,
  sopWgtSettingDialogVisible: false,
  pageLeaveRef: false,
};
const editIncomingNodesRef = ref<string[]>([]);
const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);
const { t } = useI18n();

const props = defineProps<RouterInfoType>();
const menuMode = props.routerInfo.name.at(-1);
const commonActionRequestData = initCommonRequestFromPrivilegesType({
  menu2Scn: props.routerInfo.name.slice(0, -1),
  menu3Act: menuMode,
  signType: '0',
});
const graphContainer = ref<HTMLDivElement | null>(null);
const moduleValue = computed(() => Module);
const sopSetting = getSOPSetting(t);
const countUpId = ref(0);
const copyNode = ref<Node | null>(null);
const lastNormalPartId = ref<string>();
const lastAddPartId = ref<string>();
let oldSopNodeNmJp: string = '';
const recApprovFlgData = [
  {
    condKey: '',
    cmbId: 'recApprovFlg',
    label: 'なし',
    value: '0',
    optionList: [],
  },
  {
    condKey: '',
    cmbId: 'recApprovFlg',
    label: 'あり',
    value: '1',
    optionList: [],
  },
];
const notDeleteParts: string[] = [
  SOP_PARTS_VARIABLES.PART_SPEC_START_CD,
  SOP_PARTS_VARIABLES.PART_SPEC_START_NAME,
  SOP_PARTS_VARIABLES.PART_SPEC_END_CD,
  SOP_PARTS_VARIABLES.PART_SPEC_END_NAME,
];

const messageBoxTemplateButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});
const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});
const messageBoxSopChartSaveRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});
const messageBoxCellRemoveRef = ref<DialogProps>({
  title: '',
  content: '',
  type: 'warning',
});
const messageBoxPageLeaveRef = ref<DialogProps>({
  title: '',
  content: '',
  type: 'warning',
});
const sopSettingDialogFormRef = ref<CustomFormType>({
  formItems: sopSettingDialogFormItems,
  formModel: sopSettingDialogFormModel,
});
const sopPrcSettingDialogFormRef = ref<CustomFormType>({
  formItems: sopPrcSettingDialogFormItems,
  formModel: sopPrcSettingDialogFormModel,
});
const sopWgtSettingDialogFormRef = ref<CustomFormType>({
  formItems: sopWgtSettingDialogFormItems,
  formModel: sopWgtSettingDialogFormModel,
});
type State = {
  sopGraph: Graph | null;
  sopStencil: Stencil | null;
  screenWidth: number;
  screenHeight: number;
  sopPartListShowType: boolean;
  isSopInfoSettingVisible: boolean;
  imageMap: Map<string, string>;
  images: ImageOption[];
  conditionals: (keyof PartMenuBranchingProps)[];
  destinationParts: (keyof PartMenuBranchingProps)[];
  // 「課題No.292」 ADD ST branchNameを通じて、ブランチが新しいブランチであるかどうかを判断できるために、destinationMenusを追加します。
  destinationMenus: (keyof PartMenuBranchingProps)[];
  // 「課題No.292」ADD ED
  destinationMessages: (keyof PartSystemBranchProps)[];
  SOPSetData: NodeProperty;
  SOPPartSetData: SopPartDataSource;
  oldSOPPartSetData: SopPartDataSource;
  maxNodeNum: number;
  checkedAddPartId: string;
  checkeAddPartFlag: boolean;
  selectedPartId: string;
  selectedStartPartId: string;
  selectedEndPartId: string;
  sopClonePartId: string;
  sopPartAddType: string;
  currentPartId: string;
  currentPosition: PositionOption;
  currentPartMoveFlag: boolean;
  selectPartMoveFlag: boolean;
  isSopInfoSettingDialogVisible: boolean;
  copyPartId: string;
  localeCheckId: string;
  localeCheckType: string;
  SOPSelectFlag: boolean;
  SOPBlockType: string;
  SOPblockNo: string;
  sopSelectVisableOption: SopSelectVisableOption[];
  chartOptions: ChartOptions;
  SOPAddFlag: boolean;
  SOPChartType: string;
  sopFlowChartType: string;
  showBlockAddCard: boolean;
  blnLastcheckedAddPart: boolean;
  LastcheckedAddPartSuccessors: Cell[];
  SOPData: {
    sopFlowNo: string;
    sopFlowNm: string;
  };
  sopDataRx: RxSopSettingOption;
  sopDataPrc: PrcSopSettingOption;
  sopDataWgt: WgtSopSettingOption;
  removeNodeId: string;
  refererScreenId: string;
  partNameList: PartsNameOption[];
  // [課題管理表][No.120] ADD ST
  // ノード追加時、新規作成する通常ノードを退避する
  lastNormalNode: Node | null;
  // [課題管理表][No.120] ADD ED
  sopNodeNmList: string[];
  // [課題399] ADD ST フローの全部のブロックのノードの情報を保存します。
  blockExpandList: SopFlowGetDataOption[];
  childNodeList: SopFlowGetDataOption[];
  cmnRequest: CommonRequestType;
  sopFlowType: string;
  isRxSop: boolean;
  flowList: SopFlowGetDataOption[];
  blockSeleckValue: string;
};
const state = reactive<State>({
  sopGraph: null,
  sopStencil: null,
  screenWidth: document.body.clientWidth,
  screenHeight: document.body.clientHeight,
  sopPartListShowType: false,
  isSopInfoSettingVisible: false,
  blockExpandList: [],
  // [課題399] ADD ED
  imageMap: new Map<string, string>(),
  images: [
    {
      fileName: 'menuHome',
      path: svgMenuHome,
    },
    {
      fileName: 'sopTemplate',
      path: svgSopTemplate,
    },
    {
      fileName: 'question',
      path: svgQuestion,
    },
    {
      fileName: 'add',
      path: svgAdd,
    },
    {
      fileName: 'sopBlock',
      path: svgSopBlock,
    },
    {
      fileName: 'wCheck',
      path: svgWCheck,
    },
    {
      fileName: 'abnormalityLevel1',
      path: svgAbnormalityLevel1,
    },
    {
      fileName: 'abnormalityLevel2',
      path: svgAbnormalityLevel2,
    },
    {
      fileName: 'abnormalityLevel3',
      path: svgAbnormalityLevel3,
    },
    {
      fileName: 'abnormalityLevel4',
      path: svgAbnormalityLevel4,
    },
    {
      fileName: 'abnormalityLevel5',
      path: svgAbnormalityLevel5,
    },
    {
      fileName: 'write',
      path: svgWrite,
    },
    {
      fileName: 'edit',
      path: svgEdit,
    },
    {
      fileName: 'trash',
      path: svgTrash,
    },
    {
      fileName: 'copy',
      path: svgCopy,
    },
    {
      fileName: 'partNumericTextInput',
      path: PartNumericTextInput,
    },
    {
      fileName: 'partInstructionConfirm',
      path: PartInstructionConfirm,
    },
    {
      fileName: 'partSopTimer',
      path: PartSopTimer,
    },
    {
      fileName: 'partDateRecord',
      path: PartDateRecord,
    },
    {
      fileName: 'partElectronicFile',
      path: PartElectronicFile,
    },
    {
      fileName: 'partReceiveConsumption',
      path: PartReceiveConsumption,
    },
    {
      fileName: 'partResultConfirm',
      path: PartResultConfirm,
    },
    {
      fileName: 'testpartButtonBranch',
      path: svgTestPartButtonBranch,
    },
    {
      fileName: 'testpartSystemBranch',
      path: svgTestPartSystemBranch,
    },
    {
      fileName: 'partElectronicShelfLabel',
      path: PartElectronicShelfLabel,
    },
    {
      fileName: 'partInventoryConsumption',
      path: PartInventoryConsumption,
    },
    {
      fileName: 'testpartUpdDevice',
      path: svgTestPartUpdDevice,
    },
    {
      fileName: 'testpartLabelOutput',
      path: svgTestPartLabelOutput,
    },
    {
      fileName: 'testpartCommDevice',
      path: svgTestPartCommDevice,
    },
    {
      fileName: 'testpartWeighingCalib',
      path: svgTestPartWeighingCalib,
    },
    {
      fileName: 'testpartPalletCargo',
      path: svgTestPartPalletCargo,
    },
    {
      fileName: 'partCopyNode',
      path: PartCopyNode,
    },
  ],
  conditionals: [
    'conditional1',
    'conditional2',
    'conditional3',
    'conditional4',
    'conditional5',
    'conditional6',
    'conditional7',
    'conditional8',
  ],
  destinationParts: [
    'branchNodeId1',
    'branchNodeId2',
    'branchNodeId3',
    'branchNodeId4',
    'branchNodeId5',
    'branchNodeId6',
    'branchNodeId7',
    'branchNodeId8',
  ],
  // 「課題No.292」 ADD ST branchNameを通じて、ブランチが新しいブランチであるかどうかを判断できるために、destinationMenusを追加します。
  destinationMenus: [
    'branchMenu1',
    'branchMenu2',
    'branchMenu3',
    'branchMenu4',
    'branchMenu5',
    'branchMenu6',
    'branchMenu7',
    'branchMenu8',
  ],
  destinationMessages: [
    'branchMessage1',
    'branchMessage2',
    'branchMessage3',
    'branchMessage4',
    'branchMessage5',
    'branchMessage6',
  ],
  // 「課題No.292」 ADD ED
  SOPSetData: {
    ...sopSetting,
  },
  SOPPartSetData: {
    id: '',
    settingNodeCd: '',
    incomingList: [],
    outgoingList: [],
    destinationNodeNames: [],
    receiveConsumptionNodes: [],
    sopCondition: {
      condition1: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition2: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition3: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition4: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition5: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition6: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
    },
    commonSetting: sopSetting.commonSetting,
    individualPara: null,
    nodeClassName: '',
    dispNodeId: '',
    sopNodeNo: '',
    dispFlg: false,
    conditionProps: {
      judgeValueShowFlg: '',
      deviationMessageText: '',
      conditionBranch: '',
      deviationBranchNodeId: '',
    },
    upperLowerSetting: {
      thJudgeFlg: '',
      thRangeType: '',
      thValType: '',
      thValLlmt: '',
      thValUlmt: '',
    },
    privGroupList: [],
    instUnitTxt: '',
    errorDestinationNodeList: [],
    confluenceNodeId: '',
  },
  oldSOPPartSetData: {
    id: '',
    settingNodeCd: '',
    incomingList: [],
    outgoingList: [],
    destinationNodeNames: [],
    receiveConsumptionNodes: [],
    sopCondition: {
      condition1: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition2: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition3: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition4: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition5: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
      condition6: {
        nextCondLeft: '',
        nextCondOpe: '',
        nextCondRight: '',
      },
    },
    commonSetting: sopSetting.commonSetting,
    individualPara: null,
    nodeClassName: '',
    dispNodeId: '',
    sopNodeNo: '',
    dispFlg: false,
    conditionProps: {
      judgeValueShowFlg: '',
      deviationMessageText: '',
      conditionBranch: '',
      deviationBranchNodeId: '',
    },
    upperLowerSetting: {
      thJudgeFlg: '',
      thRangeType: '',
      thValType: '',
      thValLlmt: '',
      thValUlmt: '',
    },
    privGroupList: [],
    instUnitTxt: '',
    errorDestinationNodeList: [],
    confluenceNodeId: '',
  },
  maxNodeNum: 1000,
  checkedAddPartId: '',
  checkeAddPartFlag: false,
  selectedPartId: '',
  selectedStartPartId: '',
  selectedEndPartId: '',
  sopClonePartId: '',
  sopPartAddType: '',
  currentPartId: '',
  currentPosition: {
    x: 0,
    y: 0,
  },
  currentPartMoveFlag: false,
  selectPartMoveFlag: false,
  isSopInfoSettingDialogVisible: false,
  copyPartId: '',
  localeCheckId: '',
  localeCheckType: '',
  SOPSelectFlag: false,
  SOPBlockType: '',
  SOPblockNo: '',
  sopSelectVisableOption: [],
  chartOptions: {
    blockData: {
      sopFlowNo: '',
      sopFlowNmJp: '',
      sopCatTxtJp: '',
      untSopCat: '',
      updDts: '',
      flowList: [],
    },
    cellData: {},
    blockNo: '',
  },
  SOPAddFlag: false,
  SOPChartType: '',
  sopFlowChartType: '',
  showBlockAddCard: false,
  blnLastcheckedAddPart: false,
  LastcheckedAddPartSuccessors: [],
  SOPData: {
    sopFlowNo: '',
    sopFlowNm: '',
  },
  sopDataRx: {
    sopFlowNo: '',
    sopFlowNmJp: '',
    matNo: '',
    matNm: '',
    rxNo: '',
    rxNm: '',
    prcSeq: 0,
    prcNm: '',
    sopBatchType: '',
    helpBinPath: '',
    forcePrivGrpCd: '',
    skipPrivGrpCd: '',
    dspSeq: null,
    updDts: '',
  },
  sopDataPrc: {
    sopFlowNo: '',
    sopFlowNmJp: '',
    prcNo: '',
    prcNm: '',
    stYmd: '',
    edYmd: '',
    recApprovFlg: '',
    helpBinPath: '',
    forcePrivGrpCd: '',
    skipPrivGrpCd: '',
    dspSeq: null,
    updDts: '',
  },
  sopDataWgt: {
    sopFlowNo: '',
    sopFlowNmJp: '',
    wgtRoomNo: '',
    wgtRoomNm: '',
    wgtSopCat: '',
    helpBinPath: '',
    forcePrivGrpCd: '',
    skipPrivGrpCd: '',
    dspSeq: null,
    updDts: '',
  },
  removeNodeId: '',
  // [課題管理表][No.120] ADD ST
  lastNormalNode: null,
  // [課題管理表][No.120] ADD ED
  refererScreenId: '',
  partNameList: [
    {
      partsCD: SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_CD,
      text: t('SOP.Menu.txtInstructionConfirm'),
    },
    {
      partsCD: SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD,
      text: t('SOP.Menu.txtSopTimer'),
    },
    {
      partsCD: SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD,
      text: t('SOP.Menu.txtNumericTextInput'),
    },
    {
      partsCD: SOP_PARTS_VARIABLES.PART_DATE_RECORD_CD,
      text: t('SOP.Menu.txtDateRecord'),
    },
    {
      partsCD: SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD,
      text: t('SOP.Menu.txtReceiveConsumption'),
    },
    {
      partsCD: SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD,
      text: t('SOP.Menu.txtResultConfirm'),
    },
    {
      partsCD: SOP_PARTS_VARIABLES.PART_ELECTRONIC_FILE_CD,
      text: t('SOP.Menu.txtElectronicFile'),
    },
    {
      partsCD: SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD,
      text: t('SOP.Menu.txtButtonBranch'),
    },
    {
      partsCD: SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD,
      text: t('SOP.Menu.txtSystemBranch'),
    },
    {
      partsCD: SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD,
      text: t('SOP.Menu.txtExternalDevice'),
    },
    {
      partsCD: SOP_PARTS_VARIABLES.PART_ELECTRONIC_SHELF_LABEL_CD,
      text: t('SOP.Menu.txtElectronicShelfLabel'),
    },
    {
      partsCD: SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD,
      text: t('SOP.Menu.txtInventoryConsumption'),
    },
    {
      partsCD: SOP_PARTS_VARIABLES.PART_EQUIPMENT_CONTAINER_CD,
      text: t('SOP.Menu.txtEquipmentContainer'),
    },
    {
      partsCD: SOP_PARTS_VARIABLES.PART_LABEL_OUTPUT_CD,
      text: t('SOP.Menu.txtLabelOutput'),
    },
    {
      partsCD: SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_CD,
      text: t('SOP.Menu.txtWeightCalibration'),
    },
    {
      partsCD: SOP_PARTS_VARIABLES.PART_PALLET_CARGO_CD,
      text: t('SOP.Menu.txtPalletCargo'),
    },
    {
      partsCD: SOP_PARTS_VARIABLES.PART_SPEC_COPY_CD,
      text: t('SOP.Menu.txtCopyNode'),
    },
  ],
  sopNodeNmList: [],
  childNodeList: [],
  cmnRequest: {
    langCd: '',
    loginUsrId: '',
    appId: '',
    scnId: '',
    actId: '',
    btnId: '',
    signType: '',
    msgboxTitleTxt: '',
    msgboxMsgTxt: '',
    msgboxBtnTxt: '',
    msgboxInputCmt: '',
  },
  sopFlowType: '',
  isRxSop: false,
  flowList: [],
  blockSeleckValue: '',
});
// 別ページ遷移
let savedNext: NavigationGuardNext | null = null;
onBeforeRouteLeave((_to, _from, next) => {
  if (_to.path === '/errorPage') {
    next();
    return;
  }
  messageBoxPageLeaveRef.value.title = `${t('SOP.Menu.txtSopTitle')}`;
  messageBoxPageLeaveRef.value.content = `${t('SOP.Msg.confirmPageLeave')}`;
  openDialog('pageLeaveRef');
  savedNext = next;
});
const handlePageLeave = async () => {
  closeDialog('pageLeaveRef');
  if (savedNext !== null) {
    savedNext();
    savedNext = null;
  }
  let strSopFlowNo = '';
  if (state.refererScreenId === SCREEN_ID.SOP_RX_LIST) {
    strSopFlowNo = state.sopDataRx.sopFlowNo;
  } else if (state.refererScreenId === SCREEN_ID.SOP_PRC_LIST) {
    strSopFlowNo = state.sopDataPrc.sopFlowNo;
  } else if (state.refererScreenId === SCREEN_ID.SOP_WGT_LIST) {
    strSopFlowNo = state.sopDataWgt.sopFlowNo;
  }
  // 排他ロック解除
  const apiRequestData: DeleteSysExSop = {
    sopFlowNo: strSopFlowNo,
  };
  const { errorRef } = await useDeleteSysExSop({
    ...commonActionRequestData,
    ...apiRequestData,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButtonRef');
  }
};
const handleCancel = () => {
  closeDialog('pageLeaveRef');
  if (savedNext !== null) {
    savedNext(false);
    savedNext = null;
  }
};

const graphHeight = computed(() => {
  const navbarHeightData: string = moduleValue.value.navbarHeight;
  const navbarValue: number = Number(navbarHeightData.replace('px', ''));
  const pageSizeWidthData: string = moduleValue.value.pageSizeWidth;
  const pageSizeWidthValue: number = Number(
    pageSizeWidthData.replace('px', ''),
  );
  const pageSizeHeightData: string = moduleValue.value.pageSizeHeight;
  const pageSizeHeightValue = Number(pageSizeHeightData.replace('px', ''));
  // control:50 - title:50 17(scrollbarのheight)
  let defHeight: number = 100;
  if (state.screenWidth < pageSizeWidthValue) {
    defHeight = 100 + 17;
  }
  let heightVal: number = 0;
  if (state.screenHeight < pageSizeHeightValue) {
    // pageSizeHeightValue - defHeight - 88.5px(navbarのheight)
    heightVal = pageSizeHeightValue - defHeight - navbarValue;
  } else {
    // screenHeight - defHeight - 88.5px(navbarのheight)
    heightVal = state.screenHeight - defHeight - navbarValue;
  }
  return heightVal;
});
const SopRxSettingDialogRef = ref();
const SopPrcSettingDialogRef = ref();
const SopWgtSettingDialogRef = ref();
const graphWidth = computed(() => {
  const sideBarWidthData: string = moduleValue.value.sideBarWidth;
  const sideBarValue: number = Number(sideBarWidthData.replace('px', ''));
  const sidebar: boolean = stores.app().siderbarOpened;
  const pageSizeWidthData: string = moduleValue.value.pageSizeWidth;
  const pageSizeWidthValue: number = Number(
    pageSizeWidthData.replace('px', ''),
  );
  const pageSizeHeightData: string = moduleValue.value.pageSizeHeight;
  const pageSizeHeightValue: number = Number(
    pageSizeHeightData.replace('px', ''),
  );
  // 300(stencil)  sideBarのwidth 17(scrollbarのwidth)
  let defWidth: number = 0;
  if (sidebar) {
    defWidth = sideBarValue;
  }
  if (state.screenHeight < pageSizeHeightValue) {
    defWidth += 17;
  }
  let widthVal: number = 0;
  if (state.screenWidth < pageSizeWidthValue) {
    // sideBarMinValue - defWidth
    widthVal = pageSizeWidthValue - defWidth;
  } else {
    // screenWidth -defWidth
    widthVal = state.screenWidth - defWidth;
  }
  return widthVal;
});

/* **************************************************************************************************************************************************** */
/* *** サブメソッド定義                                                                                                                                   */
/* **************************************************************************************************************************************************** */
/**
 * ノードに接続されているスタブの ID を取得
 * @param {*} nodeId - データのId
 * @remarks
 * <<呼び出しメソッド>>
 * addPathTargetEdge
 * addBlockTargetEdge
 * sopEdgeEdit
 * createSOPFlowEdges
 */
const getCellPortsId = (nodeId: string) => {
  let itemTopId: string = '';
  let itemBottomId: string = '';
  const nodeCell = state.sopGraph!.getCellById(nodeId);
  if (nodeCell && nodeCell.isNode()) {
    const portList = nodeCell.getPorts();
    portList.forEach((item) => {
      const itemOption = item;
      if (!itemOption.id) return;
      if (itemOption.group === 'top') {
        itemTopId = itemOption.id;
      }
      if (itemOption.group === 'bottom') {
        itemBottomId = itemOption.id;
      }
    });
  }
  return {
    topId: itemTopId,
    bottomId: itemBottomId,
  };
};
/**
 * Current node Neighbors
 * @param {*} currentNode - データ
 * @remarks
 * <<呼び出しメソッド>>
 * nodeDeleteSelectedPart
 * removeSelectedPart
 * removeDragPart
 */
const getCurrentNeighbors = (currentNode: Cell) => {
  const incoming: Cell[] = state.sopGraph!.getNeighbors(currentNode, {
    incoming: true,
  });
  const incomingValue = incoming.map((item: Cell) => {
    const sopPartsCD = item.getProp<string>('sopPartsCD');
    const itemCdFlag: boolean = checkPartCode(
      state.SOPSetData.partCds,
      sopPartsCD,
    );
    let textValue = item.getAttrByPath<string>('text/text');
    if (itemCdFlag) {
      const titleText = item.getAttrByPath<string>('title/text');
      if (titleText !== '') {
        textValue = titleText;
      }
    }
    return {
      label: textValue,
      value: item.id,
    };
  });
  const outgoing: Cell[] = state.sopGraph!.getNeighbors(currentNode, {
    outgoing: true,
  });
  const outgoingValue = outgoing.map((item: Cell) => {
    const sopPartsCD = item.getProp<string>('sopPartsCD');
    const itemCdFlag: boolean = checkPartCode(
      state.SOPSetData.partCds,
      sopPartsCD,
    );
    let textValue = item.getAttrByPath<string>('text/text');
    if (itemCdFlag) {
      const titleText = item.getAttrByPath<string>('title/text');
      if (titleText !== '') {
        textValue = titleText;
      }
    }
    return {
      label: textValue,
      value: item.id,
    };
  });
  return {
    incomingList: incomingValue,
    outgoingList: outgoingValue,
  };
};
/**
 * Current node Neighbors
 * @param {*} currentNode - データ
 * @remarks
 * <<呼び出しメソッド>>
 * currentNodeSetting
 */
const getCurrentPartNeighbors = (currentNode: Cell) => {
  const incoming: Cell[] = state.sopGraph!.getNeighbors(currentNode, {
    incoming: true,
  });
  let incomingCell: SelectOption[] = [];
  incoming.forEach((item) => {
    const subIncoming = state.sopGraph!.getNeighbors(item, {
      incoming: true,
    });
    const incomingValue = subIncoming.map((subItem: Cell) => {
      const sopPartsCD = subItem.getProp<string>('sopPartsCD');
      const itemCdFlag: boolean = checkPartCode(
        state.SOPSetData.partCds,
        sopPartsCD,
      );
      let textValue = subItem.getAttrByPath<string>('text/text');
      if (itemCdFlag) {
        const titleText = subItem.getAttrByPath<string>('title/text');
        if (titleText !== '') {
          textValue = titleText;
        }
      }
      return {
        label: textValue,
        value: subItem.id,
      };
    });
    incomingCell = incomingCell.concat(incomingValue);
  });
  const outgoing: Cell[] = state
    .sopGraph!.getNeighbors(currentNode, {
      outgoing: true,
    })
    .sort((a, b) => {
      const positionA = a.getProp<PositionOption>('position');
      const positionB = b.getProp<PositionOption>('position');
      return positionA.x - positionB.x;
    });
  let outgoingCell: SelectOption[] = [];
  outgoing.forEach((item) => {
    const subOutgoing = state.sopGraph!.getNeighbors(item, {
      outgoing: true,
    });
    const outgoingValue = subOutgoing.map((sunItem: Cell) => {
      const sopPartsCD = sunItem.getProp<string>('sopPartsCD');
      const itemCdFlag: boolean = checkPartCode(
        state.SOPSetData.partCds,
        sopPartsCD,
      );
      let textValue = sunItem.getAttrByPath<string>('text/text');
      if (itemCdFlag) {
        const titleText = sunItem.getAttrByPath<string>('title/text');
        if (titleText !== '') {
          textValue = titleText;
        }
      }
      return {
        label: textValue,
        value: sunItem.id,
      };
    });
    outgoingCell = outgoingCell.concat(outgoingValue);
  });
  return {
    incomingList: incomingCell,
    outgoingList: outgoingCell,
  };
};
/**
 * getDestinationPartNames
 * @param {*} currentNode - データ
 * @remarks
 * <<呼び出しメソッド>>
 * currentNodeSetting
 */
const getDestinationPartNames = () => {
  const allParts = state.sopGraph!.getNodes();
  const partCell: SelectOption[] = [];
  const receiveConCell: SelectOption[] = [];
  allParts.forEach((item) => {
    const partsCd = item.getProp<string>('sopPartsCD');
    const sameCdFlag = checkPartCode(state.SOPSetData.partCds, partsCd);
    const controlFlag = checkPartCode(state.SOPSetData.controlPartCds, partsCd);
    if (
      sameCdFlag ||
      controlFlag ||
      partsCd === SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_CD
    ) {
      let textValue = item.getAttrByPath<string>('text/text');
      if (sameCdFlag) {
        const titleText = item.getAttrByPath<string>('title/text');
        if (titleText !== '') {
          textValue = titleText;
        }
      }
      textValue = `${item.getProp<string>('dispNodeId')} / ${textValue}`;
      partCell.push({ label: textValue, value: item.id });

      if (partsCd === SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD) {
        receiveConCell.push({ label: textValue, value: item.id });
      }
    }
  });
  return { allParts: partCell, receiveConParts: receiveConCell };
};
/**
 * 分岐の場合、分岐パーツを探して、-1のではない場合、分岐内のパーツです。
 * <<呼び出しメソッド>>
 * nodeDelete
 */
const branchNodeIndex = (array: Node[] | Cell[]) => {
  let skipCount = 0;
  const conditionBranchArr = [
    SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD,
    SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_CD,
    SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD,
    SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_CD,
    SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD,
    SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD,
    SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD,
    SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD,
    SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD,
    SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD,
  ];
  for (let i = 0; i < array.length; i++) {
    const partsName = array[i].getProp<string>('sopPartsCD');
    const sameCdFlag = checkPartCode(state.SOPSetData.partCds, partsName);
    let nodeBranchCount = '0';
    if (
      sameCdFlag &&
      // @ts-expect-error 既にこのリストを定義しました。
      (array[i].getProp('individualPara')!.conditionBranch ||
        // @ts-expect-error 既にこのリストを定義しました。
        array[i].getProp('conditionProps')!.conditionBranch ||
        (Object.prototype.hasOwnProperty.call(
          array[i].getProp('individualPara')!,
          'branchMenu1',
        ) &&
          // @ts-expect-error 既にこのリストを定義しました。
          array[i].getProp('individualPara')!.branchMenu1 !== ''))
    ) {
      nodeBranchCount =
        // @ts-expect-error 既にこのリストを定義しました。
        array[i].getProp('individualPara')!.conditionBranch ||
        // @ts-expect-error 既にこのリストを定義しました。
        array[i].getProp('conditionProps')!.conditionBranch;
    }
    if (
      partsName === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD ||
      (conditionBranchArr.includes(
        partsName as (typeof conditionBranchArr)[number],
      ) &&
        nodeBranchCount === '0')
    ) {
      skipCount += 1;
    }
    if (
      conditionBranchArr.includes(
        partsName as (typeof conditionBranchArr)[number],
      )
    ) {
      if (skipCount === 0) {
        return i;
      }
      skipCount -= 1;
    }
  }
  return -1;
};
/**
 * 指定ノードが分岐設定状況を判定する
 * @param {Cell} node - 判定対象ノード
 * @returns {boolean} 分岐設定がある場合はtrue、ない場合はfalse
 */
const hasConditionBranch = (node: Cell): boolean => {
  const sopPartsCd = node.getProp<string>('sopPartsCD');
  const individualPara = node.getProp('individualPara');
  const conditionProps = node.getProp('conditionProps');
  // システム分岐・ボタン分岐
  if (
    sopPartsCd === SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD ||
    sopPartsCd === SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD
  ) {
    // branchNumSettingが2以上なら分岐あり
    if (individualPara && Number(individualPara.branchNumSetting) >= 2) {
      return true;
    }
  }

  // 指示内容確認・SOPタイマー・外部機器通信・計測器点検
  if (
    sopPartsCd === SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_CD ||
    sopPartsCd === SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD ||
    sopPartsCd === SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD ||
    sopPartsCd === SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_CD
  ) {
    if (
      (individualPara && individualPara.conditionBranch === '1') ||
      (conditionProps && conditionProps.conditionBranch === '1')
    ) {
      return true;
    }
  }

  // 数値文字入力、在庫消費、実績確定、受入投入
  if (
    sopPartsCd === SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD ||
    sopPartsCd === SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD ||
    sopPartsCd === SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD ||
    sopPartsCd === SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD
  ) {
    if (conditionProps && conditionProps.conditionBranch === '1') {
      return true;
    }
  }

  return false;
};
/**
 * 描画されたノードへのパラメータセット
 * @param {*} nodeId - データのId
 * @remarks
 * <<呼び出しメソッド>>
 * setSopChartMain
 * setPartNodeToFlow
 * editPartNode
 */
const currentNodeSetting = (nodeId: string) => {
  const currentNode = state.sopGraph!.getCellById(nodeId);
  if (currentNode.isNode()) {
    // 編集開始時点のSOPPartSetDataをディープコピーで保存
    state.oldSOPPartSetData = JSON.parse(JSON.stringify(state.SOPPartSetData));

    state.sopNodeNmList = [];
    const sopPartsCD = currentNode.getProp<string>('sopPartsCD');
    const currentNeighbors = getCurrentPartNeighbors(currentNode);
    const { allParts, receiveConParts } = getDestinationPartNames();
    const currentNodePosition = currentNode.getProp<PositionOption>('position');
    const allNodes = state.sopGraph!.getNodes();

    const conditionPropsKey = 'conditionProps';
    const individualParaKey = 'individualPara';
    const deviationBranchNodeIdKey = 'deviationBranchNodeId';

    const deviationBranchNodeIdList: string[] = [];
    let deviationBranchNode: string;
    allNodes.forEach((nodeItem) => {
      const conditionProps = nodeItem.getProp(conditionPropsKey);
      const individualPara = nodeItem.getProp(individualParaKey);
      if (
        nodeItem.getProp<string>('sopPartsCD') ===
        SOP_PARTS_VARIABLES.PART_LABEL_OUTPUT_CD
      ) {
        individualPara!.timeoutSeconds = String(individualPara!.timeoutSeconds);
      }
      if (conditionProps || individualPara) {
        const conditionBranch =
          conditionProps?.conditionBranch || individualPara?.conditionBranch;
        if (conditionBranch === '1') {
          deviationBranchNodeIdList.push(
            conditionProps?.[deviationBranchNodeIdKey] ||
              individualPara?.[deviationBranchNodeIdKey],
          );
          if (nodeItem.id === currentNode.id) {
            deviationBranchNode =
              conditionProps?.[deviationBranchNodeIdKey] ||
              individualPara?.[deviationBranchNodeIdKey];
          }
        }
      }
    });
    const conditionIndex = deviationBranchNodeIdList.findIndex(
      (item) => item === deviationBranchNode,
    );
    if (conditionIndex !== -1) {
      deviationBranchNodeIdList.splice(conditionIndex, 1);
    }
    // --[移動先ノードの制限]
    // ・自ノードより下にあるノードは移動先ノードから除外
    const limitedDestinationNodeList = allParts.filter((item) => {
      const cell = state.sopGraph!.getCellById(item.value);
      const cellPosition = cell.getProp<PositionOption>('position');
      return cellPosition.y < currentNodePosition.y || item.value === nodeId;
    });
    state.SOPPartSetData.errorDestinationNodeList = limitedDestinationNodeList;
    state.SOPPartSetData.destinationNodeNames = limitedDestinationNodeList;
    state.SOPPartSetData.receiveConsumptionNodes = receiveConParts;
    state.SOPPartSetData.incomingList = currentNeighbors.incomingList;
    state.SOPPartSetData.outgoingList = currentNeighbors.outgoingList;
    state.SOPPartSetData.commonSetting = currentNode.getProp('commonSetting');
    oldSopNodeNmJp = currentNode.getProp('commonSetting').sopNodeNmJp;
    state.SOPPartSetData.conditionProps = currentNode.getProp('conditionProps');
    state.SOPPartSetData.sopCondition = currentNode.getProp('sopCondition');
    state.SOPPartSetData.upperLowerSetting =
      currentNode.getProp('upperLowerSetting');
    const individualPara = currentNode.getProp('individualPara');
    state.SOPPartSetData.dispNodeId = currentNode.getProp('dispNodeId');
    state.SOPPartSetData.sopNodeNo = currentNode.getProp('sopNodeNo');
    state.SOPPartSetData.instUnitTxt = currentNode.getProp('instUnitTxt');
    state.SOPPartSetData.individualPara = individualPara;
    state.SOPPartSetData.settingDecisionFlg =
      currentNode.getProp('settingConfirmFlg');

    // --- confluenceNodeIdをセット ---
    const flowData = state.flowList.find(
      (item) => item.sopNodeNo === currentNode.id,
    );
    if (flowData && flowData.confluenceNodeId) {
      state.SOPPartSetData.confluenceNodeId = flowData.confluenceNodeId;
    } else if (!hasConditionBranch(currentNode)) {
      state.SOPPartSetData.confluenceNodeId = '';
    }

    // [課題管理表][No.119] ST ボタン分岐グラフに描画したパーツが消える場合がある
    // branchNodeIdとoutgoingは、PartSystemBranchまたはPartButtonBranchの下でのみ同期的に更新されます
    // 「課題外」 MOD ST 新しいノード移動先変更は必要がないです。
    if (
      (sopPartsCD === SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD) &&
      state.SOPPartSetData?.commonSetting?.sopNodeNmJp !== undefined &&
      state.SOPPartSetData?.commonSetting?.sopNodeNmJp !== ''
    ) {
      // 「課題外」 MOD ED
      // ノード編集ポップアップをオーペンする時、また元の移動先ですので、移動先ノードIDを正しいノードに付けます。
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      state.SOPPartSetData.outgoingList.forEach((outgoingItem, index) => {
        if (
          outgoingItem.label !== undefined &&
          outgoingItem.label !== t('SOP.Menu.txtEnd')
        ) {
          // TODO:any削除して型指定予定
          // eslint-disable-next-line
          if (sopPartsCD === SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD) {
            const incoming: Cell[] = state.sopGraph!.getNeighbors(
              state.sopGraph!.getCellById(outgoingItem.value),
              {
                incoming: true,
              },
            );
            if (index + 1 !== state.SOPPartSetData.outgoingList.length) {
              // eslint-disable-next-line
              (state.SOPPartSetData.individualPara as any)[
                `branchNodeId${index + 1}`
              ] = outgoingItem.value;
            }
            incoming.forEach((incomingItem) => {
              if (
                incomingItem
                  .getProp<SelectBranchOption>('branchOption')
                  ?.label.includes('False')
              ) {
                // eslint-disable-next-line
                (state.SOPPartSetData.individualPara as any)[
                  // eslint-disable-next-line
                  `branchNodeIdDefault`
                ] = outgoingItem.value;
              }
            });
          } else {
            // eslint-disable-next-line
            (state.SOPPartSetData.individualPara as any)[
              `branchNodeId${index + 1}`
            ] = outgoingItem.value;
          }
        }
      });
      // [課題管理表][No.119] ED
    }
    const conditionBranchArr = [
      SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD,
      SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_CD,
      SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD,
      SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_CD,
      SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD,
      SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD,
      SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD,
      SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD,
    ];
    if (conditionBranchArr.includes(sopPartsCD)) {
      // 「課題外」 MOD ED
      // ノード編集ポップアップをオーペンする時、また元の移動先ですので、移動先ノードIDを正しいノードに付けます。
      state.SOPPartSetData.outgoingList.forEach((outgoingItem, index) => {
        if (
          outgoingItem.label !== undefined &&
          outgoingItem.label !== t('SOP.Menu.txtEnd')
        ) {
          if (index === 1) {
            if (state.SOPPartSetData.conditionProps?.conditionBranch === '1') {
              state.SOPPartSetData.conditionProps.deviationBranchNodeId =
                outgoingItem.value;
            }
            if (state.SOPPartSetData.individualPara?.conditionBranch === '1') {
              state.SOPPartSetData.individualPara.deviationBranchNodeId =
                outgoingItem.value;
            }
          }
        }
      });
    }
    // [課題管理表] 課題[No.190] ADD ST 指示内容確認(分岐なし)をパーツ追加時、既存パーツが消える
    // 条件分岐はありからなしに変更するかどうかを確認するために、以前の状態を保存する
    if (state.SOPPartSetData.individualPara !== undefined) {
      state.oldSOPPartSetData = JSON.parse(
        JSON.stringify(state.SOPPartSetData),
      );
    }
    state.SOPPartSetData.id = currentNode.id;
    state.SOPPartSetData.settingNodeCd = sopPartsCD;
    state.isSopInfoSettingVisible = true;
    if (
      sopPartsCD !== SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD &&
      sopPartsCD !== SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD &&
      sopPartsCD !== SOP_PARTS_VARIABLES.PART_SPEC_TEMPLATE_CD &&
      sopPartsCD !== SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_CD &&
      !state.isSopInfoSettingDialogVisible
    ) {
      state.isSopInfoSettingVisible = true;
    }
    if (
      currentNode.getAttrByPath<string>('text/text') === null ||
      currentNode.getAttrByPath<string>('text/text') === undefined
    ) {
      state.SOPPartSetData.nodeClassName = '';
    } else {
      state.SOPPartSetData.nodeClassName =
        currentNode.getAttrByPath<string>('text/text');
    }
    // ノード名一覧を更新する
    const sameCdFlag = checkPartCode(state.SOPSetData.partCds, sopPartsCD);
    if (sameCdFlag) {
      const lstNodeNm: string[] = [];
      const nodeList: Node[] = state.sopGraph!.getNodes();
      nodeList.forEach((item: Node) => {
        const commonSetting = item.getProp('commonSetting');
        const nodeTitle = commonSetting.sopNodeNmJp;
        if (nodeTitle !== '' && nodeTitle) {
          lstNodeNm.push(nodeTitle);
        }
      });
      state.sopNodeNmList = lstNodeNm;
    }
  }
};
/**
 * 二つのノード間連結パス上の全体ノード（目標ノードが含む、ソースノードが含まない）を取得する
 * 注意：検索方向はパスの逆方向（incoming）である
 * @param {*} initId - 最初のソースノードID
 * @param {*} sourceId - 今回のソースノードID
 * @param {*} targetId - 目標ノードID
 * @param {*} res - 結果ノードIDの配列
 * @param {*} paths - 経過ノードIDの配列（内部判定用で外部から呼び出す時指定不要）
 * @remarks
 * <<呼び出しメソッド>>
 * getPointsBetweenNodes
 */
const getPointsBetweenNodes = (
  initId: string,
  sourceId: string,
  targetId: string,
  res: string[],
  path: string[] = [],
): boolean => {
  const sourceNode = state.sopGraph!.getCellById(sourceId);
  const incomings: Cell[] = state.sopGraph!.getNeighbors(sourceNode, {
    incoming: true,
  });
  let reachTarget: boolean = false;
  for (let i = 0; i < incomings.length; i++) {
    const incoming = incomings[i];
    if (!reachTarget && incoming.id === targetId) {
      res.push(targetId);
      reachTarget = true;
    } else if (!reachTarget && incoming.id === initId) {
      reachTarget = false;
    } else if (path.includes(incoming.id)) {
      reachTarget = false;
    } else if (!reachTarget) {
      reachTarget = getPointsBetweenNodes(initId, incoming.id, targetId, res, [
        ...path,
        ...[incoming.id],
      ]);
    }
  }
  if (reachTarget && sourceId !== initId) {
    res.push(sourceId);
  }
  return reachTarget;
};

/**
 * 指定する二つのノードを含むブランチ全体のBBox領域情報を取得する
 * @param {*} sourceId - ソースノードID
 * @param {*} targetId - 目標ノードID
 * @return {*} ブランチ全体のBBox領域情報（{minX: , minY: , maxX: , maxY:}）
 * @remarks
 * <<呼び出しメソッド>>
 * getPointsBetweenNodes
 */
const getBranchBoxInfo = (sourceId: string, targetId: string) => {
  const graphArea = state.sopGraph!.getGraphArea();
  const boxInfo = {
    minX: graphArea.width,
    maxX: 0,
    minY: graphArea.height,
    maxY: 0,
  };
  const sourceNode = state.sopGraph!.getCellById(sourceId);
  const incomings: Cell[] = state.sopGraph!.getNeighbors(sourceNode, {
    incoming: true,
  });
  const allNodesOfBranch: string[] = [sourceId];
  getPointsBetweenNodes(sourceId, sourceId, targetId, allNodesOfBranch);
  incomings.forEach((incoming: Cell) => {
    getPointsBetweenNodes(sourceId, sourceId, incoming.id, allNodesOfBranch);
  });
  allNodesOfBranch.forEach((nodeId: string) => {
    const anode = state.sopGraph!.getCellById(nodeId);
    boxInfo.minX = Math.min(boxInfo.minX, anode.getBBox().left);
    boxInfo.maxX = Math.max(boxInfo.maxX, anode.getBBox().right);
    boxInfo.minY = Math.min(boxInfo.minY, anode.getBBox().top);
    boxInfo.maxY = Math.max(boxInfo.minX, anode.getBBox().bottom);
  });
  return boxInfo;
};
// [課題外No.2] ADD ED ループの場合、Edgeは分岐ノードの中心を通過する問題対応
/**
 * part add iconの作成
 * @param {*} parent - ブロック
 * @remarks
 * <<呼び出しメソッド>>
 * setSopChartMain
 * getSOPFlowData
 * setBranchForPart
 * createConvergenceAddpart
 */
const setAddPartIcon = (parent: Node) => {
  const parentSize = parent.getProp<SizeOption>('size');
  // 12 = 24 / 2 (buttonAdd.width / 2)
  const refXVal = parentSize.width / 2 - 12;
  // 20(button.height) 4 = 2(button.ry) + 2(buttonAdd.ry)
  const refYVal = (parentSize.height - 20) / 2 - 4;
  parent.prop('size', { width: parentSize.width, height: parentSize.height });
  parent.prop('branchOption', { label: '', value: '', type: '' });
  parent.setAttrs({
    buttonNodeAdd: {
      refX: refXVal,
      refY: refYVal,
    },
    button: {
      width: 24,
      height: 24,
      refX: 1,
      refY: 1,
      rx: 2,
      ry: 2,
      fill: 'transparent',
      stroke: '#000000',
      strokeWidth: 0,
      cursor: 'pointer',
      event: 'node:sopPartAdd',
    },
    buttonAdd: {
      'xlink:href': state.imageMap.get('add'),
      width: 24,
      height: 24,
      refX: 1,
      refY: 1,
      rx: 2,
      ry: 2,
    },
    buttonCopy: {
      'xlink:href': state.imageMap.get('menu1'),
      width: 24,
      height: 24,
      refX: 1,
      refY: 1,
      rx: 2,
      ry: 2,
    },
  });
  return parent;
};
/**
 * getItemValueByKey
 * @param {*} parent - ブロック
 * @remarks
 * <<呼び出しメソッド>>
 * createAddPart
 * getSOPFlowData
 * sopRectPartSetting
 * sopControlPartSetting
 */
const getItemValueByKey = <T extends object, K extends keyof T>(
  item: T,
  key: K,
): T[K] => item[key];

/**
 * ボタン分岐の分岐ラベル名称を取得
 * @param {*} parent - ボタン分岐ノード
 * @param {*} index - 分岐ラベルのインデックス
 * @remarks
 * <<呼び出しメソッド>>
 * getSOPFlowData
 */
const getMenuButtonName = (node: Node | Cell, index: number) => {
  let branchMenuName = '';
  if (!node.isNode()) {
    return branchMenuName;
  }
  // ボタン名称を取得します。
  const cellIndividualPara = node.getProp(
    'individualPara',
  ) as PartButtonBranchProps;
  if (cellIndividualPara === undefined) {
    return branchMenuName;
  }
  switch (index) {
    case 1:
      branchMenuName = cellIndividualPara.branchMenu1;
      break;
    case 2:
      branchMenuName = cellIndividualPara.branchMenu2;
      break;
    case 3:
      branchMenuName = cellIndividualPara.branchMenu3;
      break;
    case 4:
      branchMenuName = cellIndividualPara.branchMenu4;
      break;
    case 5:
      branchMenuName = cellIndividualPara.branchMenu5;
      break;
    case 6:
      branchMenuName = cellIndividualPara.branchMenu6;
      break;
    case 7:
      branchMenuName = cellIndividualPara.branchMenu7;
      break;
    case 8:
      branchMenuName = cellIndividualPara.branchMenu8;
      break;
    default:
      branchMenuName = '';
      break;
  }
  branchMenuName = `${t('SOP.Menu.txtBranch')}${index}:${branchMenuName}`;
  return branchMenuName;
};
/**
 * 条件分岐の数を取得
 * @param {*} parent - ブロック
 * @remarks
 * <<呼び出しメソッド>>
 * nodeDelete
 * closeSopInfoSetting
 */
const getBranchOptions = (
  parent: SopBranchOption,
  flag: boolean = false,
  confluenceNodeId?: string, // ★収束ノードIDを追加
) => {
  const individualPara = flag
    ? JSON.parse(parent.individualPara || '{}').IndividualSetting
    : parent.getProp('individualPara');
  const conditionProps = flag
    ? JSON.parse(parent.individualPara || '{}').ConditionSetting
    : parent.getProp('conditionProps');
  const partsCD = flag
    ? parent.sopPartsNm || parent.sopPartsCd
    : parent.getProp<string>('sopPartsCD');
  let branchOptions: SelectBranchOption[] = [];

  // メニュー分岐(システム分岐)
  if (partsCD === SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD) {
    if (individualPara.branchNumSetting !== 0) {
      for (let count = 1; count < individualPara.branchNumSetting; count++) {
        const branch = t('SOP.Menu.txtConditionBranch');
        branchOptions.push({
          label: `${branch}${count + 1}`,
          value:
            confluenceNodeId ||
            individualPara[state.destinationParts[count - 1]] ||
            '',
          branchName:
            individualPara[state.destinationMessages[count - 1]] || '',
          type: 'partId',
        });
      }
      branchOptions.push({
        label: `${t('SOP.Chr.SOPDetailSetting.PartSystemBranch.txtExceptionNode')}`,
        branchName: individualPara.branchMessageDefault,
        value: confluenceNodeId || individualPara.branchNodeIdDefault,
        type: 'partId',
      });
    }
  }
  // メニュー分岐(ボタン分岐)
  if (partsCD === SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD) {
    if (individualPara.branchNumSetting !== 0) {
      for (let count = 0; count < individualPara.branchNumSetting; count++) {
        const method = t('SOP.Chr.SOPDetailSetting.txtMenuSelection');
        const labelName =
          individualPara[`branchMenu${count + 1}`] !== undefined
            ? individualPara[`branchMenu${count + 1}`]
            : `${method}${count + 1}`;
        branchOptions.push({
          label: labelName,
          value:
            confluenceNodeId || individualPara[state.destinationParts[count]],
          branchName: individualPara[state.destinationMenus[count]],
          type: 'partId',
        });
      }
    }
  }
  // 指示内容確認
  if (partsCD === SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_CD) {
    if (individualPara.conditionBranch === '1') {
      const nodeName =
        individualPara.deviationBranchNodeId !== ''
          ? individualPara.deviationBranchNodeId
          : '';
      branchOptions = [
        {
          label: 'true',
          value: '',
          type: 'partId',
          branchName: nodeName,
        },
        {
          label: 'false',
          value: confluenceNodeId || nodeName || '',
          type: 'partId',
          branchName: nodeName,
        },
      ];
    }
  }
  // SOPタイマー
  if (partsCD === SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD) {
    if (individualPara.conditionBranch === '1') {
      const nodeName =
        individualPara.deviationBranchNodeId !== ''
          ? individualPara.deviationBranchNodeId
          : '';
      branchOptions = [
        {
          label: 'true',
          value: '',
          type: 'partId',
          branchName: nodeName,
        },
        {
          label: 'false',
          value: confluenceNodeId || nodeName || '',
          type: 'partId',
          branchName: nodeName,
        },
      ];
    }
  }
  // 外部機器通信
  if (partsCD === SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD) {
    if (individualPara.conditionBranch === '1') {
      const nodeName =
        individualPara.deviationBranchNodeId !== ''
          ? individualPara.deviationBranchNodeId
          : '';
      branchOptions = [
        {
          label: 'true',
          value: '',
          type: 'partId',
          branchName: nodeName,
        },
        {
          label: 'false',
          value: confluenceNodeId || nodeName || '',
          type: 'partId',
          branchName: nodeName,
        },
      ];
    }
    // }
  }
  // 計測器点検
  if (partsCD === SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_CD) {
    if (individualPara.conditionBranch === '1') {
      const nodeName =
        individualPara.deviationBranchNodeId !== ''
          ? individualPara.deviationBranchNodeId
          : '';
      branchOptions = [
        {
          label: 'true',
          value: '',
          type: 'partId',
          branchName: nodeName,
        },
        {
          label: 'false',
          value: confluenceNodeId || nodeName || '',
          type: 'partId',
          branchName: nodeName,
        },
      ];
    }
  }

  // 数値文字入力、受入投入、実績確定、在庫消費
  if (
    partsCD === SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD ||
    partsCD === SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD ||
    partsCD === SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD ||
    partsCD === SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD
  ) {
    const nodeName =
      conditionProps.deviationBranchNodeId !== ''
        ? conditionProps.deviationBranchNodeId
        : '';
    if (conditionProps.conditionBranch === '1') {
      branchOptions = [
        {
          label: 'true',
          value: '',
          type: 'partId',
          branchName: nodeName,
        },
        {
          label: 'false',
          value: confluenceNodeId || nodeName || '',
          type: 'partId',
          branchName: nodeName,
        },
      ];
    }
  }
  return branchOptions;
};
/**
 * 分岐情報を取得する
 * @param {*} parent - 分岐親パーツ
 * @remarks
 * <<呼び出しメソッド>>
 * closeSopInfoSetting
 */
const getBranchOptionsValue = (parent: SopBranchOption) => {
  const branchOption: SelectBranchOption[] = [];

  // 分岐パーツのプロパティ(branchOption)を取得する
  const addPartNodes = state
    .sopGraph!.getNeighbors(parent, { outgoing: true })
    .filter(
      (n) =>
        n.getProp<string>('sopPartsCD') ===
        SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD,
    )
    .sort((a, b) => {
      const positionA = a.getProp<PositionOption>('position');
      const positionB = b.getProp<PositionOption>('position');
      return positionA.x - positionB.x;
    });
  addPartNodes.forEach((addPartNode) => {
    const option = addPartNode.getProp('branchOption');
    branchOption.push({
      label: option ? option.label : '',
      value: option ? option.value : '',
    });
  });

  return branchOption;
};
/**
 * 条件分岐各ブランチにラベル設定する
 * @param {*} parent - 分岐親パーツ
 * @remarks
 * <<呼び出しメソッド>>
 * closeSopInfoSetting
 */
const setBranchOptionsLabel = (parent: SopBranchOption) => {
  const individualPara = parent.getProp('individualPara');
  const conditionProps = parent.getProp('conditionProps');
  const partsCD = parent.getProp<string>('sopPartsCD');
  let branchOptions: SelectBranchOption[] = [];

  const isIndividualBranch = (cd: string) =>
    cd === SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_CD ||
    cd === SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD ||
    cd === SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD ||
    cd === SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_CD;

  const isConditionBranch = (cd: string) =>
    cd === SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD ||
    cd === SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD ||
    cd === SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD ||
    cd === SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD;

  // メニュー分岐(ボタン分岐)
  if (partsCD === SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD) {
    if (individualPara.branchNumSetting !== 0) {
      for (let count = 0; count < individualPara.branchNumSetting; count++) {
        const method = t('SOP.Chr.SOPDetailSetting.txtMenuSelection');
        const labelName =
          individualPara[`branchMenu${count + 1}`] !== undefined
            ? individualPara[`branchMenu${count + 1}`]
            : `${method}${count + 1}`;
        branchOptions.push({
          label: labelName,
          value: '',
        });
      }
    }
  }
  // メニュー分岐(システム分岐)
  if (partsCD === SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD) {
    if (individualPara.branchNumSetting !== 0) {
      for (let count = 1; count < individualPara.branchNumSetting; count++) {
        const branch = t('SOP.Menu.txtConditionBranch');
        branchOptions.push({
          label: `${branch}${count}`,
          value: '',
        });
      }
      branchOptions.push({
        label: `${t('SOP.Chr.SOPDetailSetting.PartSystemBranch.txtExceptionNode')}`,
        value: '',
      });
    }
  }
  // 個別設定分岐
  if (isIndividualBranch(partsCD)) {
    if (individualPara.conditionBranch === '1') {
      branchOptions = [
        {
          label: 'true',
          value: '',
        },
        {
          label: 'false',
          value: '',
        },
      ];
    }
  }
  // 条件設定分岐
  if (isConditionBranch(partsCD)) {
    if (conditionProps.conditionBranch === '1') {
      branchOptions = [
        {
          label: 'true',
          value: '',
        },
        {
          label: 'false',
          value: '',
        },
      ];
    }
  }
  return branchOptions;
};
/**
 * 条件分岐各ブランチに分岐先情報を設定する
 * @param {*} parent - 分岐親パーツ
 * @remarks
 * <<呼び出しメソッド>>
 * closeSopInfoSetting
 */
const setBranchOptionsValue = (
  branchOption: SelectBranchOption[],
  parent: SopBranchOption,
) => {
  const individualPara = parent.getProp('individualPara');
  const conditionProps = parent.getProp('conditionProps');
  const partsCD = parent.getProp<string>('sopPartsCD');

  const isIndividualBranch = (cd: string) =>
    cd === SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_CD ||
    cd === SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD ||
    cd === SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD ||
    cd === SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_CD;

  const isConditionBranch = (cd: string) =>
    cd === SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD ||
    cd === SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD ||
    cd === SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD ||
    cd === SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD;

  // メニュー分岐(ボタン分岐)
  if (partsCD === SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD) {
    const branchPara: PartButtonBranchProps = state
      .sopGraph!.getCellById(parent.id)
      .getProp('individualPara');
    if (branchPara.branchNumSetting !== 0) {
      for (let count = 0; count < branchPara.branchNumSetting; count++) {
        const key = `branchNodeId${count + 1}` as keyof PartButtonBranchProps;
        const branchValue = individualPara[key];
        // 移動先ノードを設定
        branchOption.forEach((item, index) => {
          // item.value = state.SOPPartSetData.confluenceNodeId;
          if (index === count) {
            // eslint-disable-next-line no-param-reassign
            item.value =
              branchValue !== ''
                ? branchValue
                : state.SOPPartSetData.confluenceNodeId;
          }
        });
      }
    }
  }
  // メニュー分岐(システム分岐)
  if (partsCD === SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD) {
    if (individualPara.branchNumSetting !== 0) {
      const branchPara: PartSystemBranchProps = state
        .sopGraph!.getCellById(parent.id)
        .getProp('individualPara');

      if (branchPara.branchNumSetting !== 0) {
        for (let count = 0; count < branchPara.branchNumSetting; count++) {
          let key: keyof PartSystemBranchProps;
          if (count === branchPara.branchNumSetting - 1) {
            key = `branchNodeIdDefault` as keyof PartSystemBranchProps;
          } else {
            key = `branchNodeId${count + 1}` as keyof PartSystemBranchProps;
          }
          const branchValue = individualPara[key];
          // 移動先ノードを設定
          branchOption.forEach((item, index) => {
            // item.value = state.SOPPartSetData.confluenceNodeId;
            if (index === count) {
              // eslint-disable-next-line no-param-reassign
              item.value =
                branchValue !== ''
                  ? branchValue
                  : state.SOPPartSetData.confluenceNodeId;
            }
          });
        }
      }
    }
  }
  // 個別設定分岐
  if (isIndividualBranch(partsCD)) {
    if (individualPara.conditionBranch === '1') {
      const branchValue = individualPara.deviationBranchNodeId;
      // 異状時飛び先ノードを設定
      branchOption.forEach((item, index) => {
        // eslint-disable-next-line no-param-reassign
        item.value = state.SOPPartSetData.confluenceNodeId || '';
        if (index === 1 && branchValue !== '') {
          // eslint-disable-next-line no-param-reassign
          item.value = branchValue;
        }
      });
    }
  }
  // 条件設定分岐
  if (isConditionBranch(partsCD)) {
    if (conditionProps.conditionBranch === '1') {
      const branchValue = conditionProps.deviationBranchNodeId;
      // 異状時飛び先ノードを設定
      branchOption.forEach((item, index) => {
        // eslint-disable-next-line no-param-reassign
        item.value = state.SOPPartSetData.confluenceNodeId || '';
        if (index === 1 && branchValue !== '') {
          // eslint-disable-next-line no-param-reassign
          item.value = branchValue;
        }
      });
    }
  }

  // 分岐パーツのプロパティを更新する
  const addPartNodes = state
    .sopGraph!.getNeighbors(parent, { outgoing: true })
    .filter(
      (n) =>
        n.getProp<string>('sopPartsCD') ===
        SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD,
    )
    .sort((a, b) => {
      const positionA = a.getProp<PositionOption>('position');
      const positionB = b.getProp<PositionOption>('position');
      return positionA.x - positionB.x;
    });
  addPartNodes.forEach((addPartNode, idx) => {
    if (branchOption[idx]) {
      addPartNode.prop('branchOption', branchOption[idx]);
    }
  });

  return addPartNodes;
};
/**
 * ロケーションの取得
 * @param {*} node - ブロック
 * @param {*} currentCell - ブロック
 * @remarks
 * <<呼び出しメソッド>>
 * nodeDelete
 * setPartPosition
 * setPartNodeToFlow
 * setSelectionDragPart
 * removeDragPart
 * setBranchForPart
 */
// [課題管理表][No.179] ST パラメータにノード追加を表記用フラグを追加
// const getTranslatePosition = (currentCell: Node, node: Node) => {
const getTranslatePosition = (
  // MOD ST Nodeの親クラスCellを追加する
  currentCell: Node | Cell,
  node: Node | Cell,
  // MOD ED
  nodeAddFlg: boolean = false,
) => {
  // [課題管理表][No.179] ED
  const { sopPartConst } = state.SOPSetData;
  const nodePosition = node.getProp<PositionOption>('position');
  const nodeSize = node.getProp<SizeOption>('size');
  const cellPosition = currentCell.getProp<PositionOption>('position');
  const cellSize = currentCell.getProp<SizeOption>('size');
  const transX: number =
    cellPosition.x - (nodeSize.width / 2 - cellSize.width / 2) - nodePosition.x;
  let transY: number =
    cellPosition.y + cellSize.height + sopPartConst!.marginTop - nodePosition.y;
  const partsCD = currentCell.getProp<string>('sopPartsCD');
  const sameCdFlag = checkPartCode(state.SOPSetData.addPartCds, partsCD);
  if (sameCdFlag) {
    transY += 5;
  }
  // [課題管理表][No.179] ST ノード追加且つ、終了ノードと開始ノードの縦距離は追加ノードを収容できる場合、終了ノードの縦座標を調整不要
  if (
    nodeAddFlg &&
    nodePosition.y - cellPosition.y >= cellSize.height + sopPartConst!.marginTop
  ) {
    transY = 0;
  }
  // [課題管理表][No.179] ED
  return {
    translateX: transX,
    translateY: transY,
  };
};

/**
 * ノード接続の作成
 * @param {*} nodeId - ブロック
 * @param {*} cellId - ブロック
 * @remarks
 * <<呼び出しメソッド>>
 * setPartNodeToFlow
 * getSOPFlowData
 * setBranchForPart
 */
const addPathTargetEdge = (cellId: string, nodeId: string) => {
  // [課題外] ADD ST ノード間複数Edge存在問題対応
  state.sopGraph!.getEdges().forEach((anEdge) => {
    if (
      anEdge.getSourceCellId() === cellId &&
      anEdge.getTargetCellId() === nodeId
    ) {
      state.sopGraph!.removeEdge(anEdge);
    }
  });
  // [課題外] ADD ED ノード間複数Edge存在問題対応
  const nodePorts = getCellPortsId(nodeId);
  const cellPorts = getCellPortsId(cellId);
  state.sopGraph!.addEdge({
    source: { cell: cellId, port: cellPorts.bottomId },
    target: { cell: nodeId, port: nodePorts.topId },
    attrs: {
      line: {
        stroke: '#A2B1C3',
        strokeWidth: 2,
        targetMarker: {
          name: 'path',
          d: '',
        },
      },
    },
    zIndex: 0,
  });
};
/**
 * ノード接続の作成(テンプレート)
 * @param {*} nodeId - ブロック
 * @param {*} cellId - ブロック
 */
const addBlockTargetEdge4Temp = (cellId: string, nodeId: string) => {
  state.sopGraph!.getEdges().forEach((anEdge) => {
    if (
      anEdge.getSourceCellId() === cellId &&
      anEdge.getTargetCellId() === nodeId
    ) {
      state.sopGraph!.removeEdge(anEdge);
    }
  });
  const nodePorts = getCellPortsId(nodeId);
  const cellPorts = getCellPortsId(cellId);
  const edgeOption = {
    source: { cell: cellId, port: cellPorts.bottomId },
    target: { cell: nodeId, port: nodePorts.topId },
    connector: {
      name: 'jumpover',
      args: {
        type: 'arc',
        size: 5,
        radius: 20,
      },
    },
    attrs: {
      line: {
        stroke: '#A2B1C3',
        strokeWidth: 2,
        targetMarker: {
          name: 'block',
          width: 12,
          height: 8,
        },
      },
    },
    zIndex: 0,
  };
  state.sopGraph!.addEdge(edgeOption);
};
/**
 * ノード接続の作成
 * @param {*} nodeId - ブロック
 * @param {*} cellId - ブロック
 * @param {*} pathPointFlg - 経過ポイントを指定するか
 * @remarks
 * <<呼び出しメソッド>>
 * nodeDelete
 * setPartNodeToFlow
 * setSelectionDragPart
 * removeDragPart
 * setSOPFlowChart
 * setBranchForPart
 */
// [課題管理表][No.120] MOD ST
// ループの場合、分岐の連結線のパスは不正の場合があり、経過ポイントの指定が必要になる
// const addBlockTargetEdge = (cellId: string, nodeId: string) => {
const addBlockTargetEdge = (
  cellId: string,
  nodeId: string,
  pathPointFlg: boolean = false,
) => {
  // [課題管理表][No.120] MOD ED
  // [課題外No.2] ADD ST ノード間複数Edge存在問題対応
  const { sopPartConst } = state.SOPSetData;
  state.sopGraph!.getEdges().forEach((anEdge) => {
    if (
      anEdge.getSourceCellId() === cellId &&
      anEdge.getTargetCellId() === nodeId
    ) {
      state.sopGraph!.removeEdge(anEdge);
    }
  });
  // [課題外No.2] ADD ED ノード間複数Edge存在問題対応
  const nodePorts = getCellPortsId(nodeId);
  const cellPorts = getCellPortsId(cellId);
  // [課題管理表][No.120] ADD ST
  const cellPart = state.sopGraph!.getCellById(cellId);
  const nodePart = state.sopGraph!.getCellById(nodeId);
  let pathPointPosBegin;
  let pathPointPosEnd;
  let pathPointSizeBegin;
  // [課題管理表][No.120] ADD ED

  if (pathPointFlg) {
    pathPointPosBegin = cellPart.getProp<PositionOption>('position');
    pathPointPosEnd = nodePart.getProp<PositionOption>('position');
    pathPointSizeBegin = cellPart.getProp<SizeOption>('size');
  }

  // [課題管理表][No.120] MOD ST
  let edgeOption = {
    source: { cell: cellId, port: cellPorts.bottomId },
    target: { cell: nodeId, port: nodePorts.topId },
    connector: {
      name: 'jumpover',
      args: {
        type: 'arc',
        size: 5,
        radius: 20,
      },
    },
    attrs: {
      line: {
        stroke: '#A2B1C3',
        strokeWidth: 2,
        targetMarker: {
          name: 'block',
          width: 12,
          height: 8,
        },
      },
    },
    zIndex: 0,
  };
  if (pathPointFlg) {
    let factor: number = 0;
    if (pathPointPosBegin && pathPointPosEnd && pathPointSizeBegin) {
      if (pathPointPosBegin.x < pathPointPosEnd.x) {
        // 経過ポイントを開始ノードの左側にする
        factor = -1;
      } else if (pathPointPosBegin.x > pathPointPosEnd.x) {
        // 経過ポイントを開始ノードの右側にする
        factor = 1;
      }
      // [課題外No.2] MOD ST ループの場合、Edgeは分岐ノードの中心を通過する問題対応
      // 所属するブランチの最小・最大X座標を取得する
      const branchBoxInfo = getBranchBoxInfo(cellId, nodeId);
      const adjustXVal = 20;
      const fromX =
        (factor > 0 ? branchBoxInfo.maxX : branchBoxInfo.minX) +
        factor * adjustXVal;
      // [課題外] MOD ED ループの場合、Edgeは分岐ノードの中心を通過する問題対応
      const fromY = pathPointPosBegin.y + 20;
      const toX = fromX;
      const toY = pathPointPosEnd.y - 20;
      const verticesJson = {
        vertices: [
          {
            x: fromX,
            y: fromY,
          },
          {
            x: toX,
            y: toY,
          },
        ],
      };
      edgeOption = { ...edgeOption, ...verticesJson };
    }
  } else {
    const cellPosition = cellPart.getProp<PositionOption>('position');
    const nodePosition = nodePart.getProp<PositionOption>('position');
    if (
      cellPosition.x !==
        nodePosition.x +
          sopPartConst!.partWidth / 2 -
          sopPartConst!.addPartWidth / 2 &&
      cellPosition.x !==
        nodePosition.x +
          sopPartConst!.startPartWidth / 2 -
          sopPartConst!.addPartWidth / 2 &&
      cellPosition.x !== nodePosition.x
    ) {
      const verticesJson = {
        vertices: [
          {
            x: cellPosition.x,
            y: nodePosition.y - 20,
          },
        ],
      };
      edgeOption = { ...edgeOption, ...verticesJson };
    }
  }

  state.sopGraph!.addEdge(edgeOption);
  // [課題管理表][No.120] MOD ED
};
/**
 * ノードデータの取得
 * @param {*} node - ブロック
 * @remarks
 * <<呼び出しメソッド>>
 * getBlockDetail
 */
const getNodeCellData = (nodes: Node.Properties['data']) =>
  nodes.map((item: Node.Properties['data']) => item.store.data);
/**
 * エッジデータの取得
 * @param {*} node - ブロック
 * @remarks
 * <<呼び出しメソッド>>
 * getBlockDetail
 */
const getEdgesCellData = (edges: Edge.Properties['data']) =>
  edges.map((item: Edge.Properties['data']) => item.store.data);
/**
 * ノードデータの取得
 * @param {*} node - ブロック
 * @remarks
 * <<呼び出しメソッド>>
 * sopPartDragStart
 */
const getNodeData = (node: Node.Properties['data']) => node.store.data;
// /**
//  * ノード座標の取得
//  * @param {*} cellPosition - 親ノードの座標
//  * @param {*} branchNum - 分岐数
//  * @param {*} count - count
//  * @remarks
//  * <<呼び出しメソッド>>
//  * setCurrentRowPosition
//  * setChartPosition
//  * setBranchForPart
//  */
// const getPartPosition = (cellPosition: PositionOption, count: number) => {
//   const { sopPartConst } = state.SOPSetData;
//   const positionX =
//     cellPosition.x +
//     (sopPartConst!.defaultWidth + sopPartConst!.marginLeft) * count;
//   const positionY = cellPosition.y;
//   return {
//     x: positionX,
//     y: positionY,
//   };
// };
/**
 * ノードIDの採番処理
 * @param なし
 * @remarks
 * 採番範囲：1～9999
 * <<呼び出しメソッド>>
 * editPartNode
 */
const setNodeId = () => {
  countUpId.value++;
  const strCountUpId = String(countUpId.value);
  return strCountUpId.padStart(4, '0');
};
/**
 * パーツの作成
 * @param {*} positionX - 座標 X
 * @param {*} positionY - 座標 Y
 * @remarks
 * <<呼び出しメソッド>>
 * createConvergenceAddpart
 * setPartPosition
 */
const createAddPart = (
  positionX: number,
  positionY: number,
  flag: boolean = false,
) => {
  const sopCommonSetting = state.SOPSetData.commonSetting;
  const sopIndividualPara = state.SOPSetData.individualPara;
  const sopPartCondition = state.SOPSetData.sopCondition;
  const controls: SopControlPartOption[] = state.SOPSetData.startParts;
  const rectItem = {
    ...controls[1],
    ports: { ...state.SOPSetData.addPartPorts },
  };
  const sopPartsCD = rectItem.sopPartsCD as keyof typeof sopIndividualPara;
  const individualPara = getItemValueByKey(sopIndividualPara, sopPartsCD);
  rectItem.zIndex = 4;
  rectItem.x = positionX;
  rectItem.y = positionY;
  rectItem.cloneFlag = false;
  rectItem.commonSetting = sopCommonSetting;
  rectItem.individualPara = individualPara;
  rectItem.sopCondition = sopPartCondition;
  if (flag) {
    rectItem.id = setNodeId();
  }
  // --- ここで属性を統一 ---
  rectItem.attrs = {
    ...rectItem.attrs,
    button: {
      width: 24,
      height: 24,
      refX: 1,
      refY: 1,
      rx: 2,
      ry: 2,
      fill: 'transparent',
      stroke: '#000000',
      strokeWidth: 0,
      cursor: 'pointer',
      event: 'node:sopPartAdd',
    },
  };
  const addPart = state.sopGraph!.addNode(rectItem);
  return addPart;
};
/**
 * ノードを削除
 * @param {*} nodeId - データのId
 * @remarks
 * 呼び出しメソッド
 * deleteNoIncomingEdgeNode
 */
// const removeSelectedPart = (nodeId: string) => {
//   if (nodeId !== '') {
//     const selectedCell = state.sopGraph!.getCellById(nodeId);
//     if (selectedCell) {
//       const partsCD = selectedCell.getProp<string>('sopPartsCD');
//       const sameCdFlag = checkPartCode(state.SOPSetData.partCds, partsCD);
//       const controlFlag = checkPartCode(
//         state.SOPSetData.controlPartCds,
//         partsCD,
//       );
//       const blockFlag = checkPartCode(state.SOPSetData.blockPartCds, partsCD);
//       if (sameCdFlag || controlFlag || blockFlag) {
//         const partNeighbors = getCurrentNeighbors(selectedCell);
//         partNeighbors.outgoingList.forEach((outgoing) => {
//           state.sopGraph!.removeNode(outgoing.value);
//         });
//       }
//       state.sopGraph!.removeNode(nodeId);
//     }
//   }
// };
/**
 * 入力側にエッジが接続されていないノードは削除する
 * @remarks
 * 呼び出しメソッド
 * setBranchForPart
 */
// const deleteNoIncomingEdgeNode = () => {
//   const allParts = state.sopGraph!.getNodes();
//   allParts.forEach((item) => {
//     const partsCD = item.getProp<string>('sopPartsCD');
//     const sameCdFlag = checkPartCode(state.SOPSetData.partCds, partsCD);
//     const controlFlag = checkPartCode(state.SOPSetData.controlPartCds, partsCD);
//     const blockFlag = checkPartCode(state.SOPSetData.blockPartCds, partsCD);
//     const addpartFlag = checkPartCode(state.SOPSetData.addPartCds, partsCD);

//     if (sameCdFlag || controlFlag || blockFlag || addpartFlag) {
//       const incoming: Cell[] = state.sopGraph!.getNeighbors(item, {
//         incoming: true,
//       });
//       if (incoming.length === 0) {
//         removeSelectedPart(item.id);
//         deleteNoIncomingEdgeNode();
//       }
//     }
//   });
// };
/**
 * 収束ノード作成
 * @param {*} node - ノード
 * @param {*} maxDeps - 最終パーツ位置
 * @remarks
 * 呼び出しメソッド
 * setBranchForPart
 */
const createConvergenceAddpart = (node: Node) => {
  const { sopPartConst } = state.SOPSetData;
  const branchNodePosition = node.getProp<PositionOption>('position');
  const cellSize = node.getProp<SizeOption>('size');
  const positionX =
    branchNodePosition.x + cellSize.width / 2 - sopPartConst!.addPartWidth / 2;
  const partSpecConfluence = createAddPart(positionX, 0, true);
  const parent = setAddPartIcon(partSpecConfluence);
  parent.prop('sopPartsCD', SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD);
  parent.prop('confluenceNodeId', node.id);
  node.prop('parentNodeId', parent.id);
  return parent;
};

// function createConfluenceNodeMap(
//   matrix: Matrix,
//   nodeAttrMap: Record<string, { sopPartsCD: string, parentNodeId?: string }>,
//   confluencePartsCd: string // 収束ノードのパーツ種別CD
// ): Record<string, { parentNodeId: string }> {
// console.log(matrix, nodeAttrMap, confluencePartsCd);
//   const confluenceNodeMap: Record<string, { parentNodeId: string }> = {};

//   for (let y = 0; y < matrix.length; y++) {
//     for (let x = 0; x < matrix[y].length; x++) {
//       const nodeId = matrix[y][x];
//       if (!nodeId) continue;
//       const attr = nodeAttrMap[nodeId];
//       if (!attr) continue;
//       // 収束ノードか判定
//       const cell = state.sopGraph!.getCellById(nodeId);
//       if (!cell || !cell.isNode()) continue;
// console.log("id:", nodeId , cell.getProp('confluenceNodeId'), cell.getProp('parentNodeId'));
//       if (attr.sopPartsCD === confluencePartsCd && attr.parentNodeId) {
//         confluenceNodeMap[nodeId] = { parentNodeId: attr.parentNodeId };
//       }
//     }
//   }
//   return confluenceNodeMap;
// }
// function isBelongToOtherBranch(
//   matrix: Matrix, // 2次元配列
//   targetX: number,
//   targetY: number,
//   confluenceNodeMap: Record<string, { parentNodeId: string }>, // 収束ノードID→親パーツ情報
//   branchParentNodeId: string // 今見ている分岐親パーツID
// ): boolean {
//   // 上方向に探索
//   for (let y = targetY - 1; y >= 0; y--) {
//     const nodeId = matrix[y][targetX];
//     if (!nodeId) continue;
//     // 収束ノードか判定
//     if (confluenceNodeMap[nodeId]) {
//       // 収束ノードの親が他の分岐パーツならtrue
//       if (confluenceNodeMap[nodeId].parentNodeId !== branchParentNodeId) {
//         return true;
//       }
//     }
//   }
//   return false;
// }

/**
 * 接続線の編集
 * @param {*} node - ブロック
 * @param {*} partId - ブロック ID
 * @remarks
 * 呼び出しメソッド
 * setSopChartMain
 * setEdgeForClonePart
 */
const sopEdgeEdit = (node: Node, partId: string) => {
  const clonePartPorts = getCellPortsId(partId);
  const outgoingEdges = state.sopGraph!.getOutgoingEdges(node);
  if (outgoingEdges) {
    outgoingEdges.forEach((edge) => {
      edge.setProp('source', { cell: partId, port: clonePartPorts.bottomId });
    });
  }
  const incomingEdges = state.sopGraph!.getIncomingEdges(node);
  if (incomingEdges) {
    incomingEdges.forEach((edge) => {
      edge.setProp('target', { cell: partId, port: clonePartPorts.topId });
    });
  }
};
/**
 * 分歧ノードの確認
 * @param {*} node - ブロック
 * @remarks
 * 呼び出しメソッド
 * setSopChartMain
 * getDataForSelectedCells
 */
const checkNeighborPart = (node: Node) => {
  let partFlag = false;
  const outgoing: Cell[] = state.sopGraph!.getNeighbors(node, {
    outgoing: true,
  });
  if (outgoing) {
    outgoing.forEach((item) => {
      const partCd = item.getProp<string>('sopPartsCD');
      if (partCd === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD) {
        partFlag = true;
      }
    });
  }
  return partFlag;
};
/**
 * 選択ノードの情報取得
 * @remarks
 * 呼び出しメソッド
 * checkSelectedCellsMoveFlag
 */
const getDataForSelectedCells = () => {
  let startCellId = '';
  let endCellId = '';
  let startCellCd = '';
  let endCellCd = '';
  let condBranchFlag = false;
  const selCells = state.sopGraph!.getSelectedCells();
  if (selCells && selCells.length > 1) {
    let startCell = selCells[0];
    let endCell = selCells[0];
    selCells.forEach((item, index) => {
      if (item.isNode()) {
        if (index > 0) {
          // 選択したすべてのノードの開始ノードを取得
          if (state.sopGraph!.isPredecessor(item, endCell)) {
            endCell = item;
          }
          // 選択したすべてのノードの終了ノードを取得
          if (!state.sopGraph!.isPredecessor(item, startCell)) {
            startCell = item;
          }
        }
        // 分歧ノードの確認
        const partFlag = checkNeighborPart(item);
        if (partFlag) {
          condBranchFlag = true;
        }
      }
    });
    startCellCd = startCell.getProp<string>('sopPartsCD');
    startCellId = startCell.id;
    endCellCd = endCell.getProp<string>('sopPartsCD');
    endCellId = endCell.id;
  }
  return {
    startPartCd: startCellCd,
    startPartId: startCellId,
    endPartCd: endCellCd,
    endPartId: endCellId,
    condBranch: condBranchFlag,
  };
};
/**
 * パーツ上の装飾名取得
 * @remarks
 * 呼び出し先メソッド
 * addNodeTools
 */
const getToolByName = (node: Node, toolName: string) => {
  const tools = node.getTools();
  if (tools == null) {
    return 0;
  }
  // TODO: パーツ上でフォーカス中、アイコンが濃くなる
  return tools.items.find((tool) => tool === toolName);
};
/**
 * パーツ装飾を削除
 * @param {*} node - ノード
 * @remarks
 * 呼び出し先メソッド
 * addNodeTools
 * datasourceChange
 */
const removeNodeTools = (node: Node) => {
  const itemProp = node.getProp('commonSetting');
  if (itemProp && node.getProp<string>('sopPartsCD') !== 'PartSpecJoin') {
    node.removeTool('boundary');
    if (itemProp.scnShowFlg === '0') {
      node.addTools({
        name: 'boundary',
        args: {
          zIndex: 0,
          attrs: {
            fill: '#f0f8ff',
            stroke: '#4169e1',
            'stroke-dasharray': '7, 8',
            strokeWidth: 3,
            fillOpacity: 0,
          },
        },
      });
    }
  } else {
    node.removeTool('boundary');
  }
};
/**
 * パーツの装飾
 * @remarks
 * 呼び出し先メソッド
 * datasourceChange
 */
const addNodeTools = (node: Node) => {
  const focusTools = getToolByName(node, 'boundary');
  if (focusTools) {
    removeNodeTools(node);
  }
  const partsCD = node.getProp<string>('sopPartsCD');
  const sameCdFlag = checkPartCode(state.SOPSetData.partCds, partsCD);
  const controlFlag = checkControlPartCode(state.SOPSetData, partsCD);
  const blockFlag = checkBlockPartCode(state.SOPSetData, partsCD);

  if (sameCdFlag) {
    node.addTools({
      name: 'boundary',
      args: {
        zIndex: 0,
        padding: -2,
        attrs: {
          fill: '#00ffff',
          stroke: '#4169e1',
          'stroke-dasharray': '1, 0',
          strokeWidth: 1,
          fillOpacity: 0.2,
        },
      },
    });
  } else if (controlFlag || blockFlag) {
    node.addTools({
      name: 'boundary',
      args: {
        zIndex: 0,
        padding: '0',
        attrs: {
          fill: '#00ffff',
          stroke: '#4169e1',
          'stroke-dasharray': '1, 0',
          strokeWidth: 2,
          fillOpacity: 0.2,
        },
      },
    });
  } else if (
    partsCD === SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD ||
    partsCD === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD ||
    partsCD === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD
  ) {
    node.addTools({
      name: 'boundary',
      args: {
        zIndex: 0,
        padding: '0',
        attrs: {
          fill: '#f0f8ff',
          stroke: '#4169e1',
          'stroke-dasharray': '1, 0',
          strokeWidth: 2,
          fillOpacity: 0.2,
        },
      },
    });
  }
};
// /**
//  * エッジの作成
//  * @param {*} edgeList - エッジ一覧
//  * @remarks
//  * 呼び出し先メソッド
//  * setSOPFlowChart
//  */
// const createSOPFlowEdges = (edgeList: SopFlowEdgeOption[]) => {
//   if (edgeList) {
//     const nodes = state.sopGraph!.getNodes();
//     edgeList.forEach((item: SopFlowEdgeOption) => {
//       // [課題管理表][No.120] MOD ST
//       // let fromPortId: string = '';
//       let fromNodeId: string = '';
//       // let toPortId: string = '';
//       let toNodeId: string = '';
//       nodes.forEach((nodeItem: Node) => {
//         if (nodeItem.id === item.fromId) {
//           fromNodeId = nodeItem.id;
//           // const fromPort = getCellPortsId(nodeItem.id);
//           // fromPortId = fromPort.bottomId;
//         }
//         if (nodeItem.id === item.toId) {
//           toNodeId = nodeItem.id;
//           // const toPort = getCellPortsId(nodeItem.id);
//           // toPortId = toPort.topId;
//         }
//       });
//       if (toNodeId !== '' && fromNodeId !== '') {
//         const currentCell = state.sopGraph!.getCellById(toNodeId);
//         const sopPartsCD = currentCell.getProp<string>('sopPartsCD');
//         const sameCdFlag = checkPartCode(state.SOPSetData.partCds, sopPartsCD);
//         if (
//           sameCdFlag ||
//           [
//             SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_CD,
//             SOP_PARTS_VARIABLES.PART_SPEC_END_CD,
//           ].includes(sopPartsCD)
//         ) {
//           // edgeTargetはaddPartの上側である場合、連結線の経過ポイントを指定する必要
//           const goback =
//             state
//               .sopGraph!.getCellById(fromNodeId)
//               .getProp<PositionOption>('position').y >
//             currentCell.getProp<PositionOption>('position').y;
//           addBlockTargetEdge(fromNodeId, toNodeId, goback);
//         } else {
//           addPathTargetEdge(fromNodeId, toNodeId);
//         }
//       }
//       // [課題管理表][No.120] MOD ED
//     });
//   }
// };
// 課題外 DEL ST
// /**
//  * パーツの結合
//  * @remarks
//  * 呼び出し先メソッド
//  * setAddPartForBranch
//  */
// const SopChartAdaptive = () => {
//   const allNodes = state.sopGraph!.getNodes();
//   const nodeIds: string[] = [];
//   const flowData: PartItemOption[] = [];
//   let startPartId = '';
//   allNodes.forEach((item) => {
//     const itemObj = {
//       itemId: '',
//       itemChilds: [''],
//     };
//     itemObj.itemId = item.id;
//     const childs = getSopPartChilds(item);
//     itemObj.itemChilds = childs;
//     const partsCD = item.getProp<string>('sopPartsCD');
//     if (partsCD === 'controlPartStart') {
//       startPartId = item.id;
//     }
//     // スタートを含めた各Nodeのoutgoing
//     flowData.push(itemObj);
//   });
//   if (startPartId !== '') {
//     const nodeItem = state.sopGraph!.getCellById(startPartId);
//     if (nodeItem !== null && nodeItem.isNode()) {
//       const successors = state.sopGraph!.getSuccessors(nodeItem, {
//         deep: true,
//       });
//       successors?.forEach((successorItem) => {
//         // スタート以外のノードの配列
//         nodeIds.push(successorItem.id);
//       });
//     }
//   }
//   if (flowData.length > 0 && nodeIds.length > 0) {
//     const flowPartsList = flowToData();
//     const rowData = formatChartData(flowPartsList, nodeIds);

//     setChartPosition(rowData.flowRowData, rowData.flowRowNum);
//   }
// };
// 課題外 DEL ED

// [課題外No.2] ADD ST 複数分岐にループがある場合、レイアウト不正問題対応
/**
 * ノードはループパスにあるかどうかを判定する
 * @param {*} node - 対象ノード
 * @return {*} true:ループパスである false:ループパスではない
 */
const isNodeInLoop = (node: Cell) => {
  let isLoop = false;
  const successors = state.sopGraph!.getSuccessors(node, {
    deep: true,
  });
  if (successors.length > 0) {
    const incoming: Cell[] = state.sopGraph!.getNeighbors(node, {
      incoming: true,
    });
    if (incoming.length > 0) {
      isLoop = successors.includes(incoming[0]);
    }
  }
  return isLoop;
};

/**
 * node1からnode2へのパスがあるかを判定する
 * @param {*} node1 - ノード１
 * @param {*} node2 - ノード２
 * @return {*} true: node1からnode2へのパスがある false: node1からnode2へのパスがない
 */
const isNodesInSamePath = (node1: Cell, node2: Cell) => {
  const successors = state.sopGraph!.getSuccessors(node1, {
    deep: true,
  });
  const ret = successors.includes(node2);
  return ret;
};

/**
 * 指定した二つのノードは分岐ノードの同一ブランチにあるかを判定する
 * @param {*} sourceId - 今回のソースノードID
 * @param {*} targetId - 目標ノードID
 * @return {*} 判定結果boolean
 * @remarks
 * <<呼び出しメソッド>>
 * isNodesInSamePath
 * isNodeInLoop
 * getPointsBetweenNodes
 */
const isNodesInSameBranch = (sourceId: string, targetId: string) => {
  if (sourceId === targetId) {
    return true;
  }
  if (
    !state.sopGraph!.hasCell(sourceId) ||
    !state.sopGraph!.hasCell(targetId)
  ) {
    return false;
  }
  const sourceNode = state.sopGraph!.getCellById(sourceId);
  const targetNode = state.sopGraph!.getCellById(targetId);
  const canSource2Target = isNodesInSamePath(sourceNode, targetNode);
  const canTarget2Source = isNodesInSamePath(targetNode, sourceNode);
  if (!canSource2Target && !canTarget2Source) {
    // 二つのノード間パスがない場合、falseを返す
    return false;
  }
  const isLoop1 = isNodeInLoop(sourceNode);
  const isLoop2 = isNodeInLoop(targetNode);
  if (!isLoop1 && !isLoop2) {
    // 二つのノード間パスがある且つ、ループではない場合、trueを返す
    return true;
  }

  // 二つのノード間パスがある且つ、ループである場合、sourceId⇒targetIdの経過ポイントとtargetId⇒sourceIdの経過ポイントに重複のものがあるかをチェックする
  let res: boolean = true;
  const nodesFromSource2Target: string[] = [];
  const nodesFromTarget2Source: string[] = [];
  getPointsBetweenNodes(sourceId, sourceId, targetId, nodesFromSource2Target);
  getPointsBetweenNodes(targetId, targetId, sourceId, nodesFromTarget2Source);
  let judgeObject;
  if (canSource2Target) {
    // sourceId⇒targetIdのパスがあるので、パスの逆方向検索結果nodesFromSource2Targetを判定対象とする
    judgeObject = nodesFromSource2Target;
  } else {
    judgeObject = nodesFromTarget2Source;
  }
  if (judgeObject.length === 0) {
    // ループがある且つ、逆方向で通じない場合、falseとする
    res = false;
  } else {
    // A⇒BとB⇒Aの経過パスに重複のノードがあるかをチェックする
    nodesFromSource2Target.forEach((nodeId: string) => {
      if (nodesFromTarget2Source.includes(nodeId)) {
        // 同一ノード（分岐ノード）を経過する場合、falseとする
        res = false;
      }
    });
  }
  return res;
};

// [課題外]ノードの表示重なる問題対策 ADD ST
/**
 * 指定ノードの配下に全体子ノードの階層インデックス値を取得する
 * @param {*} rootNode - 対象ノード
 * @param {*} curDepth - 対象ノードの階層インデックス
 * @param {*} depthMap - 結果格納マップ
 * @param {*} paths - 経過ノードIDの配列
 *  * @remarks
 * <<呼び出しメソッド>>
 * getAllSubNodesDepth
 */
const getAllSubNodesDepth = (
  rootNode: Cell,
  curDepth: number,
  depthMap: object,
  paths: string[] = [],
) => {
  const localDepthMap = depthMap;
  if (rootNode.id in depthMap) {
    // ノードが複数のパスにて経過する場合、最大値にする
    // TODO:any削除して型指定予定
    // eslint-disable-next-line
    (localDepthMap as any)[rootNode.id] = Math.max(
      // TODO:any削除して型指定予定
      // eslint-disable-next-line
      (depthMap as any)[rootNode.id],
      curDepth,
    );
  } else {
    // TODO:any削除して型指定予定
    // eslint-disable-next-line
    (localDepthMap as any)[rootNode.id] = curDepth;
  }

  const outgoings: Cell[] = state.sopGraph!.getNeighbors(rootNode, {
    outgoing: true,
  });
  // TODO:any削除して型指定予定
  // eslint-disable-next-line
  const orderedArr: any[] = [];
  outgoings.forEach((outgoing) => {
    if (!paths.includes(outgoing.id)) {
      if (orderedArr.length === 0) {
        orderedArr.push(outgoing);
      } else {
        for (let i = 0; i < orderedArr.length; i++) {
          if (orderedArr[i].getBBox().left > outgoing.getBBox().left) {
            orderedArr.splice(i, 0, outgoing);
            break;
          }
          if (!orderedArr.includes(outgoing)) {
            orderedArr.push(outgoing);
          }
        }
      }
    }
  });
  orderedArr.forEach((outgoing) => {
    getAllSubNodesDepth(outgoing, curDepth + 1, localDepthMap, [
      ...paths,
      ...[outgoing.id],
    ]);
  });
};

// [課題420] DEL ST
// /**
//  * 指定した二つのノードの共通親ブランチノードを探して、それぞれ分岐と配下ノード全体の水平位置を指定値に移動する
//  * @param {*} node1 - 対象ノード1
//  * @param {*} node2 - 対象ノード2
//  * @param {*} offset1 - 分岐１の水平移動距離
//  * @param {*} offset2 - 分岐２の水平移動距離
//  */
// const FindAndAdjustBranchXPosition = (
//   node1: Cell,
//   node2: Cell,
//   offset1: number,
//   offset2: number,
// ) => {
//   const predecessors1 = state.sopGraph!.getPredecessors(node1, {
//     deep: true,
//   });
//   const predecessors2 = state.sopGraph!.getPredecessors(node2, {
//     deep: true,
//   });
//   // 二つノードの共通分岐ノード配下のaddPartBranchノードを探す
//   for (let index = 0; index < predecessors1.length - 1; index++) {
//     const predecessor = predecessors1[index];
//     if (predecessor.getProp<string>('sopPartsCD') === 'addPartBranch') {
//       const partBranch = predecessors1[index + 1];
//       if (
//         predecessors2.includes(partBranch) &&
//         isNodesInSameBranch(node2.id, partBranch.id) &&
//         isNodesInSameBranch(node1.id, partBranch.id)
//       ) {
//         const addPartBranch1 = predecessor;
//         const addPartBranch2 =
//           predecessors2[predecessors2.indexOf(partBranch) - 1];
//         let successors1 = state.sopGraph!.getSuccessors(addPartBranch1, {
//           deep: true,
//         });
//         successors1 = [...[addPartBranch1], ...successors1];
//         // 分岐１の配下全体サブノードの水平位置を移動する
//         for (let i = 0; i < successors1.length; i++) {
//           if (
//             successors1[i].id !== partBranch.id &&
//             isNodesInSameBranch(addPartBranch1.id, successors1[i].id)
//           ) {
//             successors1[i].translate(offset1, 0);
//             // 連結線の経過ポイントを指定した場合、経過ポイントのX座標も調整する
//             state
//               .sopGraph!.getConnectedEdges(successors1[i])
//               .forEach((edge) => {
//                 const vertices = edge.getProp('vertices');
//                 if (vertices) {
//                   // TODO:any削除して型指定予定
//                   // eslint-disable-next-line
//                   vertices.forEach((point: any) => {
//                     const apoint = point;
//                     apoint.x += offset1;
//                   });
//                   const edgeOption = JSON.parse(JSON.stringify(edge.toJSON()));
//                   state.sopGraph!.removeEdge(edge);
//                   state.sopGraph!.addEdge(edgeOption);
//                 }
//               });
//           }
//         }
//         // 分岐２の配下全体サブノードの水平位置を移動する
//         let successors2 = state.sopGraph!.getSuccessors(addPartBranch2, {
//           deep: true,
//         });
//         successors2 = [...[addPartBranch2], ...successors2];
//         for (let i = 0; i < successors2.length; i++) {
//           if (
//             successors2[i].id !== partBranch.id &&
//             isNodesInSameBranch(addPartBranch2.id, successors2[i].id)
//           ) {
//             successors2[i].translate(offset2, 0);
//             // 連結線の経過ポイントを指定した場合、経過ポイントのX座標も調整する
//             state
//               .sopGraph!.getConnectedEdges(successors2[i])
//               .forEach((edge) => {
//                 const vertices = edge.getProp('vertices');
//                 if (vertices) {
//                   // TODO:any削除して型指定予定
//                   // eslint-disable-next-line
//                   vertices.forEach((point: any) => {
//                     const apoint = point;
//                     apoint.x += offset2;
//                   });
//                   const edgeOption = JSON.parse(JSON.stringify(edge.toJSON()));
//                   state.sopGraph!.removeEdge(edge);
//                   state.sopGraph!.addEdge(edgeOption);
//                 }
//               });
//           }
//         }
//         break;
//       }
//     }
//   }
// };
// [課題420] DEL ED
/**
 * 分岐に存在する収束点を見つけるため
 * @param array
 * 呼び出し先メソッド
 * setBranchForPart
 */
const findCoverIndex = (array: Node[] | Cell[]) => {
  let skipCount = 0;
  const branchPartsArr = [
    SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD,
    SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_CD,
    SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD,
    SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_CD,
    SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD,
    SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD,
    SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD,
    SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD,
    SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD,
    SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD,
  ];
  for (let i = 0; i < array.length; i++) {
    const partsCd = array[i].getProp<string>('sopPartsCD');
    // const sameCdFlag = checkPartCode(state.SOPSetData.partCds, partsName);
    // let nodeBranchCount = '0';
    // if (sameCdFlag) {
    //   nodeBranchCount =
    //     // @ts-expect-error 既にこのリストを定義しました。
    //     array[i].getProp('individualPara')!.conditionBranch ||
    //     // @ts-expect-error 既にこのリストを定義しました。
    //     array[i].getProp('conditionProps')!.conditionBranch;
    // }
    // @ts-expect-error 既にこのリストを定義しました。
    if (branchPartsArr.includes(partsCd)) {
      // if (nodeBranchCount && nodeBranchCount !== '0') {
      skipCount += 1;
      // }
    }
    if (partsCd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD) {
      if (skipCount === 0) {
        return i;
      }
      skipCount -= 1;
    }
  }
  return -1;
};
// [課題420] MOD ST 分岐点後のすべて対象を移動します。
/**
 * 分岐後のノードを移動します。
 * @param outgoing
 * @param partBranch
 * @param offset
 * 呼び出しメソッド adjustOverlap
 */
const moveTheNodes = (outgoing: Cell, partBranch: Cell, offset: number) => {
  let successors = state.sopGraph!.getSuccessors(outgoing, { deep: true });
  const index = successors.findIndex((item) => item.id === partBranch.id);
  if (index !== -1) {
    successors = successors.slice(0, index);
  }
  const coverIndex = findCoverIndex(successors);
  if (coverIndex !== -1) {
    const successorsCoverIndex = successors.findIndex(
      (successorsItem) => successorsItem.id === successors[coverIndex].id,
    );
    const nextBranchIndex = successors
      .slice(successorsCoverIndex + 1)
      .findIndex(
        (successorsCoverIndexItem) =>
          successorsCoverIndexItem.getProp('sopPartsCD') ===
          SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD,
      );
    if (nextBranchIndex !== -1) {
      successors.splice(successorsCoverIndex, nextBranchIndex + 1);
    } else {
      successors.splice(successorsCoverIndex);
    }
  }
  successors.push(outgoing);
  const connectEdgeList: string[] = [];
  for (let i = 0; i < successors.length; i++) {
    if (successors[i].id !== partBranch.id) {
      successors[i].translate(offset, 0);
      // 連結線の経過ポイントを指定した場合、経過ポイントのX座標も調整する
      state.sopGraph!.getConnectedEdges(successors[i]).forEach((edge) => {
        if (!connectEdgeList.includes(edge.id)) {
          connectEdgeList.push(edge.id);
          const vertices = edge.getProp('vertices');
          if (vertices) {
            // TODO:any削除して型指定予定
            // eslint-disable-next-line
            vertices.forEach((point: any) => {
              const apoint = point;
              apoint.x += offset;
            });
            const edgeOption = JSON.parse(JSON.stringify(edge.toJSON()));
            state.sopGraph!.removeEdge(edge);
            state.sopGraph!.addEdge(edgeOption);
          }
        }
      });
    }
  }
};
/**
 * 指定した二つのノードの共通親ブランチノードを探する
 * @param {*} node1 - 対象ノード1
 * @param {*} node2 - 対象ノード2
 */
const FindCommonPartAndBranchAddPart = (node1: Cell, node2: Cell) => {
  const predecessors1 = state.sopGraph!.getPredecessors(node1, {
    deep: true,
  });
  const predecessors2 = state.sopGraph!.getPredecessors(node2, {
    deep: true,
  });
  // 二つノードの共通分岐ノード配下のPartSpecBranchノードを探す
  for (let index = 0; index < predecessors1.length - 1; index++) {
    const predecessor = predecessors1[index];
    if (
      predecessor.getProp<string>('sopPartsCD') ===
      SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD
    ) {
      const partBranch = predecessors1[index + 1];
      if (
        predecessors2.includes(partBranch) &&
        isNodesInSameBranch(node2.id, partBranch.id) &&
        isNodesInSameBranch(node1.id, partBranch.id)
      ) {
        const addPartBranch1 = predecessor;
        const addPartBranch2 =
          predecessors2[predecessors2.indexOf(partBranch) - 1];
        return [addPartBranch1, addPartBranch2, partBranch];
      }
    }
  }
  return [];
};
// [課題420] MOD ED

/**
 * 表示領域の重複した二つノードと関連のノードの水平位置を調整する
 * @param {*} nodeLeft - 左側の対象ノード
 * @param {*} nodeRight - 右側の対象ノード
 * @param {*} distance - 重複した長さ
 * @param {*} isLeftFirst - nodeLeftはその行の一番左側のノードであるか
 * @param {*} isRightLast - nodeRightはその行の一番右側のノードであるか
 *  * @remarks
 * <<呼び出しメソッド>>
 * FindAndAdjustBranchXPosition
 */
const adjustOverlap = (nodeLeft: Cell, nodeRight: Cell, distance: number) => {
  const moveDistance = (distance + 20) / 2;
  const baseInfo4Adjust = FindCommonPartAndBranchAddPart(nodeLeft, nodeRight);
  if (baseInfo4Adjust.length === 3) {
    // const leftBranchAddPart = baseInfo4Adjust[0];
    // const rightBranchAddPart = baseInfo4Adjust[1];
    const partBranch = baseInfo4Adjust[2];
    const outgoings: Cell[] = state.sopGraph!.getNeighbors(partBranch, {
      outgoing: true,
    });
    // X軸の位置順に左から右に並べ替える
    const orderedOutgoings: Cell[] = [];
    outgoings.forEach((outgoing) => {
      if (orderedOutgoings.length === 0) {
        orderedOutgoings.push(outgoing);
      } else {
        for (let i = 0; i < orderedOutgoings.length; i++) {
          if (orderedOutgoings[i].getBBox().left > outgoing.getBBox().left) {
            orderedOutgoings.splice(i, 0, outgoing);
            break;
          }
          if (!orderedOutgoings.includes(outgoing)) {
            orderedOutgoings.push(outgoing);
          }
        }
      }
    });
    const rightNodes = orderedOutgoings.slice(1);
    // leftNodes.forEach((outgoing) => {
    //   moveTheNodes(outgoing, partBranch, 0);
    // });
    rightNodes.forEach((outgoing, index) => {
      moveTheNodes(outgoing, partBranch, moveDistance * 2 * (index + 1));
    });
    // if (outgoings.length % 2 === 0) {
    //   const leftEndNodeIndex = orderedOutgoings.length / 2;
    //   if (orderedOutgoings.indexOf(rightBranchAddPart) <= leftEndNodeIndex) {
    //     const leftNodes = orderedOutgoings.slice(
    //       0,
    //       orderedOutgoings.indexOf(leftBranchAddPart) + 1,
    //     );
    //     const rightNodes = orderedOutgoings.slice(
    //       orderedOutgoings.length -
    //         1 -
    //         orderedOutgoings.indexOf(leftBranchAddPart),
    //     );

    //     leftNodes.forEach((outgoing) => {
    //       moveTheNodes(outgoing, partBranch, 0);
    //     });
    //     rightNodes.forEach((outgoing) => {
    //       moveTheNodes(outgoing, partBranch, moveDistance *2);
    //     });
    //   } else if (
    //     orderedOutgoings.indexOf(leftBranchAddPart) >=
    //     leftEndNodeIndex + 1
    //   ) {
    //     const rightNodes = orderedOutgoings.slice(
    //       orderedOutgoings.indexOf(rightBranchAddPart),
    //     );
    //     const leftNodes = orderedOutgoings.slice(
    //       0,
    //       orderedOutgoings.length -
    //         1 -
    //         orderedOutgoings.indexOf(rightBranchAddPart),
    //     );
    //     leftNodes.forEach((outgoing) => {
    //       moveTheNodes(outgoing, partBranch, 0 - moveDistance);
    //     });
    //     rightNodes.forEach((outgoing) => {
    //       moveTheNodes(outgoing, partBranch, moveDistance);
    //     });
    //   }
    // } else if (outgoings.length % 2 === 1) {
    //   const middleIndex = Math.floor(orderedOutgoings.length / 2);
    //   if (orderedOutgoings.indexOf(rightBranchAddPart) <= middleIndex) {
    //     const leftNodes = orderedOutgoings.slice(
    //       0,
    //       orderedOutgoings.indexOf(leftBranchAddPart) + 1,
    //     );
    //     const rightNodes = orderedOutgoings.slice(
    //       orderedOutgoings.length -
    //         1 -
    //         orderedOutgoings.indexOf(leftBranchAddPart),
    //     );
    //     leftNodes.forEach((outgoing) => {
    //       moveTheNodes(outgoing, partBranch, 0 - moveDistance);
    //     });

    //     rightNodes.forEach((outgoing) => {
    //       moveTheNodes(outgoing, partBranch, moveDistance);
    //     });
    //   } else if (orderedOutgoings.indexOf(leftBranchAddPart) >= middleIndex) {
    //     const rightNodes = orderedOutgoings.slice(
    //       orderedOutgoings.indexOf(rightBranchAddPart),
    //     );
    //     const leftNodes = orderedOutgoings.slice(
    //       0,
    //       orderedOutgoings.length -
    //         1 -
    //         orderedOutgoings.indexOf(rightBranchAddPart),
    //     );
    //     leftNodes.forEach((outgoing) => {
    //       moveTheNodes(outgoing, partBranch, 0 - moveDistance);
    //     });
    //     rightNodes.forEach((outgoing) => {
    //       moveTheNodes(outgoing, partBranch, moveDistance);
    //     });
    //   }
    // }
  }
};
// [課題420] MOD ED

/**
 * 各行のノードの中に、表示領域重複のノードがあるかをチェックして、ある場合、1番目の重複箇所を調整する
 * @param {*} allRows - 各行ノードIDの配列
 * @param {*} history - 処理済ノードペアの配列
 * @return {*} true:表示領域の重複箇所がある false:表示領域の重複箇所がない
 *  * @remarks
 * <<呼び出しメソッド>>
 * adjustOverlap
 */
const checkAndAdjustOverlap = (allRows: string[][], history: string[] = []) => {
  let haveOverlap = false;
  for (let rowIndex = 0; rowIndex < allRows.length; rowIndex++) {
    for (
      let colIndex = 0;
      colIndex < allRows[rowIndex].length - 1;
      colIndex++
    ) {
      // overlapがあるかをチェックする
      const nodeLeft = state.sopGraph!.getCellById(allRows[rowIndex][colIndex]);
      const nodeRight = state.sopGraph!.getCellById(
        allRows[rowIndex][colIndex + 1],
      );
      const distance = nodeLeft.getBBox().right - nodeRight.getBBox().left;
      if (distance >= 0) {
        const nodePareStr = `${nodeLeft.id}:${nodeRight.id}`;
        if (!history.includes(nodePareStr)) {
          history.push(nodePareStr);
          haveOverlap = true;
          // 検出した1番目の重複ノードの水平位置を調整して、処理終了
          adjustOverlap(nodeLeft, nodeRight, distance);
          break;
        }
      }
    }
    if (haveOverlap) {
      break;
    }
  }
  return haveOverlap;
};

/**
 * 全体ノードの水平位置調整要否をチェックして、調整必要な場合、調整する
 *  * @remarks
 * <<呼び出しメソッド>>
 * getAllSubNodesDepth
 * checkAndAdjustOverlap
 */
const adjustHorizenPosition = () => {
  // 全体ノードの階層インデックスを取得する
  const allNodes = state.sopGraph!.getNodes();
  const nodeDepthMap = {};
  getAllSubNodesDepth(allNodes[0], 0, nodeDepthMap);
  // 行ごとにノードを配列に格納する
  const allRows: string[][] = [];
  Object.keys(nodeDepthMap).forEach((nodeId) => {
    // TODO:any削除して型指定予定
    // eslint-disable-next-line
    const depth = (nodeDepthMap as any)[nodeId];
    if (allRows[depth]) {
      allRows[depth].push(nodeId);
    } else {
      allRows[depth] = [nodeId];
    }
  });
  // 表示領域を重複した箇所があるかをチェックして、全部解消するまでループする
  let haveOverlap: boolean = false;
  const history: string[] = [];
  do {
    // 一つの表示領域を重複した箇所を検出して、解消する
    haveOverlap = checkAndAdjustOverlap(allRows, history);
  } while (haveOverlap);
};
// [課題外]ノードの表示重なる問題対策 ADD ED

/**
 * 分岐条件の追加
 * @param {*} branchOption - 分岐オプション
 * @param {*} node - ノード
 * @param {*} matrix - 分岐内のノード
 * @remarks
 * 呼び出し先メソッド
 * setAddPartForBranch
 */
const addBranchLabels = (
  branchOption: SelectBranchOption[],
  node: Node,
  matrix: Matrix,
) => {
  // 1. addPartノードを作成
  const addPartIds: string[] = [];
  const nodeRow = matrix.findIndex((row) => row.includes(node.id));
  const nodeCol = matrix[nodeRow].findIndex((cell) => cell === node.id);
  let existingAddParts: string[] = [];
  existingAddParts = matrix[nodeRow + 1]
    .slice(nodeCol, nodeCol + branchOption.length - 1)
    .filter((cell) => cell !== '') as string[];

  // 分岐インデックスで判定
  if (branchOption.length > existingAddParts.length) {
    branchOption.forEach((item, index) => {
      if (index >= existingAddParts.length) {
        // 既存のaddPartノードがない場合のみ新規作成
        const addPart = createAddPart(0, 0);
        const addPartNode = setAddPartIcon(addPart);
        addPartNode.setAttrs({
          text: {
            text: `${t('SOP.Menu.txtBranch')}${index + 1}:${item.label}`,
          },
        });
        addPartNode.prop('sopPartsCD', SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD);
        addPartNode.prop('branchOption', item);
        addPartNode.prop('parentNodeId', node.id); // 親ノードIDを保存
        addPartNode.prop('branchIndex', index); // 分岐インデックスを保存
        addPartIds.push(addPartNode.id);
      }
    });
  }

  return addPartIds;
};
/**
 * 分岐ラベル・addPartノードの再作成と収束ノードの自動生成・接続のみを行う簡易メソッド
 * @param branchOption - 分岐オプション
 * @param cellPosition - ノード位置
 * @param node - 親ノード
 */
const addBranchLabelsAndConfluence = (
  branchOption: SelectBranchOption[],
  node: Node,
  matrix: Matrix,
) => {
  state.sopPartAddType = 'addBranchLabelsAndConfluenceAdd';
  // 1. 既存addPartノードを削除
  const addPartIds: string[] = [];
  const removePartIds: string[] = [];
  const nodeRow = matrix.findIndex((row) => row.includes(node.id));
  const nodeCol = matrix[nodeRow].findIndex((cell) => cell === node.id);
  let existingAddParts: string[] = [];
  existingAddParts = matrix[nodeRow + 1]
    .slice(nodeCol, nodeCol + branchOption.length - 1)
    .filter((cell) => cell !== '') as string[];
  existingAddParts.forEach((addPart) => {
    state.sopGraph!.removeNode(addPart);
    removePartIds.push(addPart);
  });

  // 2. addPartノードを作成
  branchOption.forEach((item, index) => {
    const addPart = createAddPart(0, 0);
    const addPartNode = setAddPartIcon(addPart);
    addPartNode.setAttrs({
      text: { text: `${t('SOP.Menu.txtBranch')}${index + 1}:${item.label}` },
    });
    addPartNode.prop('sopPartsCD', SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD);
    addPartNode.prop('branchOption', item);
    addPartNode.prop('parentNodeId', node.id); // 親ノードIDを保存
    addPartNode.prop('branchIndex', index); // 分岐インデックスを保存
    addPartIds.push(addPartNode.id);
  });

  // 3. 収束ノード（confluence）の自動生成
  const confluenceNode = createConvergenceAddpart(node);
  confluenceNode.prop('parentNodeId', node.id);
  state.SOPPartSetData.confluenceNodeId = confluenceNode.id;
  addPartIds.push(confluenceNode.id);
  branchOption.forEach((item) => {
    // eslint-disable-next-line no-param-reassign
    item.value = confluenceNode.id;
  });

  return [addPartIds, removePartIds];
};
/**
 * 分岐ノードの分岐数(branchNumSetting)が減った場合に、不要なaddPartノードと収束ノードを削除する
 * @param branchOption - 現在の分岐オプション
 * @param node - 分岐元ノード
 * @returns 削除したaddPartノードID配列
 */
const removeBranchLabelsAndConfluence = (
  removeCount: number,
  node: Node,
  matrix: Matrix,
  isCheckMode: boolean = false,
): string[] => {
  const removedPartIds: string[] = [];
  // 現在のaddPartノード一覧を取得
  const nodeRow = matrix.findIndex((row) => row.includes(node.id));
  let existingAddParts: string[] = [];
  existingAddParts = matrix[nodeRow + 1].filter(
    (cell) => cell !== '',
  ) as string[];
  // 余分なaddPartノードを削除
  // xIndex（位置）で降順ソート
  const addPartsWithX = existingAddParts.map((id, idx) => ({
    id,
    x: idx,
  }));
  addPartsWithX.sort((a, b) => b.x - a.x); // xが大きい順

  for (let i = 0; i < removeCount; i++) {
    const removeId = addPartsWithX[i].id;
    if (!isCheckMode) {
      // チェックモードONの場合は削除しない
      state.sopGraph!.removeNode(removeId);
    }
    removedPartIds.push(removeId);
  }
  return removedPartIds;
};
/**
 * 指定された分岐パーツ（addPart）と、その分岐の収束ノード（confluenceNodeId）を削除する
 * @param {string[]} removedPartIds - 削除対象の分岐パーツID配列
 * @param {Node} parentNode - 分岐元ノード
 * @returns {string[]} 実際に削除したノードID配列
 */
const removeBranchPartsAndConfluence = (
  removedPartIds: string[],
  parentNode: Node,
): string[] => {
  const deletedIds: string[] = [];
  removedPartIds.forEach((addPartId) => {
    // 分岐パーツ（addPart）を削除
    state.sopGraph!.removeNode(addPartId);
    deletedIds.push(addPartId);

    // 親ノードのconfluenceNodeIdを取得
    const confluenceNodeId = parentNode.getProp('confluenceNodeId');
    if (confluenceNodeId && !deletedIds.includes(confluenceNodeId)) {
      // 収束ノードが他の分岐から参照されていなければ削除
      const stillExists = state
        .sopGraph!.getNodes()
        .some(
          (node) =>
            node.id !== parentNode.id &&
            node.getProp('confluenceNodeId') === confluenceNodeId,
        );
      if (!stillExists) {
        state.sopGraph!.removeNode(confluenceNodeId);
        deletedIds.push(confluenceNodeId);
      }
    }
    if (
      confluenceNodeId === '' &&
      state.oldSOPPartSetData.confluenceNodeId !== '' &&
      state.oldSOPPartSetData.confluenceNodeId !== undefined
    ) {
      // ループ設定後に解除した場合は、設定変更前のconfluenceNodeIdを削除対象とする
      deletedIds.push(state.oldSOPPartSetData.confluenceNodeId);
      state.oldSOPPartSetData.confluenceNodeId = '';
      parentNode.prop('confluenceNodeId', '');
    }
  });
  return deletedIds;
};
/**
 * 選択されたパーツの削除処理
 * @param {*} nodeId - データのId
 * @remarks
 * <<呼び出しメソッド>>
 * nodeDeleteNoIncomingEdgeNode
 * nodeDelete
 */
const nodeDeleteSelectedPart = (nodeId: string) => {
  if (nodeId !== '') {
    const selectedCell = state.sopGraph!.getCellById(nodeId);
    if (selectedCell) {
      const partsCD = selectedCell.getProp<string>('sopPartsCD');
      const sameCdFlag = checkPartCode(state.SOPSetData.partCds, partsCD);
      const controlFlag = checkPartCode(
        state.SOPSetData.controlPartCds,
        partsCD,
      );
      const blockFlag = checkPartCode(state.SOPSetData.blockPartCds, partsCD);
      if (sameCdFlag || controlFlag || blockFlag) {
        const partNeighbors = getCurrentNeighbors(selectedCell);
        partNeighbors.outgoingList.forEach((outgoing) => {
          const deleteNode = state.sopGraph!.getCellById(outgoing.value);
          const deletePartsCD = deleteNode.getProp<string>('sopPartsCD');
          if (!notDeleteParts.includes(deletePartsCD)) {
            state.sopGraph!.removeNode(outgoing.value);
          }
        });
      }
      if (!notDeleteParts.includes(partsCD)) {
        state.sopGraph!.removeNode(nodeId);
      }
    }
  }
};

/**
 * 入力側にエッジが接続されていないノードを削除
 * @param {*} cell - 対象セル
 * @remarks
 * <<呼び出しメソッド>>
 * nodeDelete
 */
const nodeDeleteNoIncomingEdgeNode = () => {
  const allParts = state.sopGraph!.getNodes();
  allParts.forEach((item) => {
    const partsCD = item.getProp<string>('sopPartsCD');
    const sameCdFlag = checkPartCode(state.SOPSetData.partCds, partsCD);
    const controlFlag = checkPartCode(state.SOPSetData.controlPartCds, partsCD);
    const blockFlag = checkPartCode(state.SOPSetData.blockPartCds, partsCD);
    const addpartFlag = checkPartCode(state.SOPSetData.addPartCds, partsCD);

    if (sameCdFlag || controlFlag || blockFlag || addpartFlag) {
      const incoming: Cell[] = state.sopGraph!.getNeighbors(item, {
        incoming: true,
      });
      if (incoming.length === 0) {
        nodeDeleteSelectedPart(item.id);
        nodeDeleteNoIncomingEdgeNode();
      }
    }
  });
};
const coverAddVertices = () => {
  const allEdges = state.sopGraph!.getEdges();
  const { sopPartConst } = state.SOPSetData;
  allEdges.forEach((edgeItem) => {
    // @ts-expect-error 配列のサイズを取得する
    if (edgeItem.vertices.length !== 0) {
      const edgeSourcePosition = state
        // @ts-expect-error cellを取得します。
        .sopGraph!.getCellById(edgeItem.source.cell)
        .getProp<PositionOption>('position');
      const edgeTargetPosition = state
        // @ts-expect-error cellを取得します。
        .sopGraph!.getCellById(edgeItem.target.cell)
        .getProp<PositionOption>('position');
      const edgeSourcePartCd = state
        // @ts-expect-error cellを取得します。
        .sopGraph!.getCellById(edgeItem.source.cell)
        .getProp('sopPartsCD');
      const distance =
        edgeSourcePartCd !== SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD
          ? sopPartConst!.defaultWidth / 2
          : 0;
      if (
        edgeSourcePosition.x !== edgeTargetPosition.x &&
        edgeSourcePartCd !== SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD
      ) {
        // eslint-disable-next-line no-param-reassign
        edgeItem.vertices = [
          {
            x: edgeSourcePosition.x + sopPartConst!.addPartWidth * 2 + distance,
            y: edgeSourcePosition.y + sopPartConst!.addPartHeight,
          },
          {
            x: edgeSourcePosition.x + sopPartConst!.addPartWidth * 2 + distance,
            y: edgeTargetPosition.y - sopPartConst!.addPartHeight,
          },
        ];
      }
    }
  });
  const allConfluence = state
    .sopGraph!.getNodes()
    .filter(
      (item) =>
        item.getProp('sopPartsCD') ===
        SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD,
    );
  allConfluence.forEach((item) => {
    const incomingEdge = state.sopGraph!.getConnectedEdges(item, {
      incoming: true,
    });
    const confluencePosition = item.getProp<PositionOption>('position');
    incomingEdge.forEach((edgeItem) => {
      const edgeItemPosition = state
        // @ts-expect-error cellを取得します。
        .sopGraph!.getCellById(edgeItem.source.cell)
        .getProp<PositionOption>('position');
      if (edgeItemPosition.x !== confluencePosition.x) {
        // eslint-disable-next-line no-param-reassign
        edgeItem.vertices = [
          {
            x: edgeItemPosition.x,
            y: confluencePosition.y - sopPartConst!.addPartHeight,
          },
        ];
      }
    });
  });
};
/**
 * 親の右側のブランチを移動させる
 * @param currentNodeNextBranch
 * @param coverId
 * @param previousBranchMostRightX
 * 呼び出しメソッド moveBranchShrink
 */
const moveRightBranch = (
  currentNodeNextBranch: Cell,
  coverId: string,
  previousBranchMostRightX: number,
  previousBranchSuccessors: Cell[],
) => {
  const { sopPartConst } = state.SOPSetData;
  let currentBranchSuccessors = state.sopGraph!.getSuccessors(
    currentNodeNextBranch,
    {
      deep: true,
    },
  );
  const itemPosition =
    currentNodeNextBranch.getProp<PositionOption>('position').x;
  const coverSuccessors = state.sopGraph!.getSuccessors(
    state.sopGraph!.getCellById(coverId),
    { deep: true },
  );
  currentBranchSuccessors = currentBranchSuccessors.filter(
    (currentVal) =>
      !coverSuccessors.some(
        (currentCoverVal) => currentCoverVal.id === currentVal.id,
      ) && currentVal.id !== coverId,
  );
  currentBranchSuccessors = currentBranchSuccessors.filter(
    (item) =>
      item.getProp<PositionOption>('position').x - itemPosition >=
      -(sopPartConst!.partWidth / 2 - sopPartConst!.addPartWidth / 2),
  );
  currentBranchSuccessors = currentBranchSuccessors.filter(
    (rightItem) =>
      !previousBranchSuccessors.some(
        (currentSuccessorsVal) => currentSuccessorsVal.id === rightItem.id,
      ),
  );
  const distance =
    previousBranchMostRightX +
    (sopPartConst!.partWidth + sopPartConst!.marginLeft) -
    currentNodeNextBranch.getProp('position').x;
  currentNodeNextBranch.translate(distance, 0);
  currentBranchSuccessors.forEach((itemBranch) => {
    itemBranch.translate(distance, 0);
  });
};
/**
 * 現状によって、ノードの分岐調整する
 * @param currentNode
 * 呼び出しメソッド shrinkOrExpandBranch、getSOPFlowData、closeSopInfoSetting
 */
const shrinkOrExpandBranch = (currentNode: Cell) => {
  const { sopPartConst } = state.SOPSetData;
  const currentNodeOutgoing = state.sopGraph!.getNeighbors(currentNode, {
    outgoing: true,
  });
  const currentNodeSuccessors = state.sopGraph!.getSuccessors(currentNode, {
    deep: true,
  });
  if (findCoverIndex(currentNodeSuccessors) === -1) {
    return;
  }
  const currentNodeCoverId =
    currentNodeSuccessors[findCoverIndex(currentNodeSuccessors)].id;
  let currentBranchMostRightX = 0;
  const coverSuccessors = state.sopGraph!.getSuccessors(
    currentNodeSuccessors[findCoverIndex(currentNodeSuccessors)],
    { deep: true },
  );
  currentNodeOutgoing.forEach((item, index) => {
    let currentBranchSuccessors = state.sopGraph!.getSuccessors(item, {
      deep: true,
    });
    let beforeBranchSuccessors: Cell[] = [];
    if (index !== 0) {
      beforeBranchSuccessors = state.sopGraph!.getSuccessors(
        currentNodeOutgoing[index - 1],
        {
          deep: true,
        },
      );
    }
    const itemPosition = item.getProp<PositionOption>('position').x;
    if (currentBranchSuccessors.length >= 0) {
      currentBranchSuccessors = currentBranchSuccessors.filter(
        (currentVal) =>
          !coverSuccessors.some(
            (currentCoverVal) => currentCoverVal.id === currentVal.id,
          ),
      );
      currentBranchSuccessors = currentBranchSuccessors.filter(
        (currentItem) =>
          currentItem.getProp<PositionOption>('position').x - itemPosition >=
            -(sopPartConst!.partWidth / 2 - sopPartConst!.addPartWidth / 2) &&
          !currentNodeOutgoing.includes(currentItem),
      );
      currentBranchSuccessors = currentBranchSuccessors.filter(
        (val) =>
          !beforeBranchSuccessors.some(
            (beforeSuccessorsVal) => beforeSuccessorsVal.id === val.id,
          ),
      );
    }
    currentBranchMostRightX = item.getProp<PositionOption>('position').x;
    for (let i = 0; i < currentBranchSuccessors.length; i++) {
      const currentBranchItemPosition =
        currentBranchSuccessors[i].getProp<PositionOption>('position');
      if (currentBranchItemPosition.x > currentBranchMostRightX) {
        currentBranchMostRightX = currentBranchItemPosition.x;
      }
    }
    if (currentNodeOutgoing[index + 1]) {
      const nxetRightOfCurrentNode = currentNodeOutgoing[index + 1];
      moveRightBranch(
        nxetRightOfCurrentNode,
        currentNodeCoverId,
        currentBranchMostRightX,
        currentBranchSuccessors,
      );
    }
  });
  const nodeCellPredecessors = state
    .sopGraph!.getPredecessors(currentNode, {
      deep: true,
    })
    .filter(
      (item) => item.getProp('position').y < currentNode.getProp('position').y,
    );
  const branchIndex = branchNodeIndex(nodeCellPredecessors);
  if (branchIndex !== -1) {
    shrinkOrExpandBranch(nodeCellPredecessors[branchIndex]);
  }
};
/**
 * ブロック中のノードをフローに接続する
 * @param array
 * 呼び出しメソッド getSOPFlowData
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const flowListUpdate = (array: any[]) => {
  let returnData = [];
  returnData = array.reduce(
    (cleanList, current) => {
      const key = `${current.sopNodeNo}-${current.sopJoin.nextCondSeq}-${current.sopJoin.nextNodeNo}`;
      if (!cleanList.seen[key]) {
        cleanList.result.push(current);
        // eslint-disable-next-line no-param-reassign
        cleanList.seen[key] = true;
      }
      return cleanList;
    },
    { result: [], seen: {} },
  ).result;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  returnData = returnData.reduce(
    (
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      dataItem: any[],
      current: {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        sopNodeNo: any;
        sopJoin: {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          nextCondSeq: any;
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          nextNodeNo: any;
          nextCondLeft: string;
          nextCondOpe: string;
          nextCondRight: string;
        };
        sopCondition: SopConditions;
      },
    ) => {
      // 条件分岐のnextNodeNoを結合します。
      const existingItem = dataItem.find(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (item: { sopNodeNo: any }) => item.sopNodeNo === current.sopNodeNo,
      );
      if (existingItem) {
        if (!existingItem.sopCondition) {
          existingItem.sopCondition = {
            condition1: {
              nextCondLeft: '',
              nextCondOpe: '',
              nextCondRight: '',
            },
            condition2: {
              nextCondLeft: '',
              nextCondOpe: '',
              nextCondRight: '',
            },
            condition3: {
              nextCondLeft: '',
              nextCondOpe: '',
              nextCondRight: '',
            },
            condition4: {
              nextCondLeft: '',
              nextCondOpe: '',
              nextCondRight: '',
            },
            condition5: {
              nextCondLeft: '',
              nextCondOpe: '',
              nextCondRight: '',
            },
            condition6: {
              nextCondLeft: '',
              nextCondOpe: '',
              nextCondRight: '',
            },
          };
        }
        const conditionLength = Object.keys(existingItem.sopCondition).length;
        const conditionKey = `condition${current.sopJoin.nextCondSeq}`;
        const FIRST_CONDITION_INDEX = 2;
        let systemBranchNumSetting = 0;
        if (
          existingItem.sopPartsCd === SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD
        ) {
          const systemBranchData = JSON.parse(existingItem.individualPara);
          systemBranchNumSetting =
            systemBranchData.IndividualSetting.branchNumSetting;
        }

        if (systemBranchNumSetting === 2) {
          if (current.sopJoin.nextCondSeq === 9) {
            existingItem.sopCondition.condition1.nextCondLeft = `${existingItem.sopJoin.nextCondLeft}`;
            existingItem.sopCondition.condition1.nextCondOpe = `${existingItem.sopJoin.nextCondOpe}`;
            existingItem.sopCondition.condition1.nextCondRight = `${existingItem.sopJoin.nextCondRight}`;
          }
          if (existingItem.sopJoin.nextCondSeq < current.sopJoin.nextCondSeq) {
            existingItem.sopJoin.nextNodeNo = `${existingItem.sopJoin.nextNodeNo},${current.sopJoin.nextNodeNo}`;
          }
        } else {
          if (current.sopJoin.nextCondSeq === FIRST_CONDITION_INDEX) {
            existingItem.sopCondition.condition1.nextCondLeft = `${existingItem.sopJoin.nextCondLeft}`;
            existingItem.sopCondition.condition1.nextCondOpe = `${existingItem.sopJoin.nextCondOpe}`;
            existingItem.sopCondition.condition1.nextCondRight = `${existingItem.sopJoin.nextCondRight}`;
          }
          if (conditionLength >= current.sopJoin.nextCondSeq) {
            existingItem.sopCondition[conditionKey].nextCondLeft =
              `${current.sopJoin.nextCondLeft}`;
            existingItem.sopCondition[conditionKey].nextCondOpe =
              `${current.sopJoin.nextCondOpe}`;
            existingItem.sopCondition[conditionKey].nextCondRight =
              `${current.sopJoin.nextCondRight}`;
          }
          if (existingItem.sopJoin.nextCondSeq < current.sopJoin.nextCondSeq) {
            existingItem.sopJoin.nextNodeNo = `${existingItem.sopJoin.nextNodeNo},${current.sopJoin.nextNodeNo}`;
          } else {
            existingItem.sopJoin.nextNodeNo = `${current.sopJoin.nextNodeNo},${existingItem.sopJoin.nextNodeNo}`;
          }
        }
      } else {
        dataItem.push(current);
      }
      return dataItem;
    },
    [],
  );
  returnData.forEach((item: { sopJoin: { nextNodeNo: string } }) => {
    const branchNodeIdList = item.sopJoin.nextNodeNo.split(',');
    // @ts-expect-error nextNodeNoを初期化
    // eslint-disable-next-line no-param-reassign
    item.sopJoin.nextNodeNo = {};
    branchNodeIdList.forEach(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (branchItem: { toString: () => any }, index: number) => {
        if (branchItem) {
          // @ts-expect-error この形でnextNodeNoの値を取得します
          // eslint-disable-next-line no-param-reassign
          item.sopJoin.nextNodeNo[`condBrDst${index + 1}`] =
            branchItem.toString();
        } else {
          // eslint-disable-next-line no-param-reassign
          item.sopJoin.nextNodeNo = '';
        }
      },
    );
    // eslint-disable-next-line no-param-reassign
    item.sopJoin.nextNodeNo = JSON.stringify(item.sopJoin.nextNodeNo);
  });
  return returnData;
};

/**
 * 親パーツIDをキーに収束ノードIDを持つオブジェクトを作成する
 * @param confluenceNodes 収束ノード(Node型)の配列
 * @returns Record<親ノードID, 収束ノードID>
 */
function createParentToConfluenceNodeMap(
  confluenceNodes: SopFlowGetDataOption[] | Node[],
): Record<string, string> {
  const map: Record<string, string> = {};

  // 型を判定
  const fromDBFlag =
    confluenceNodes.length > 0 &&
    typeof (confluenceNodes[0] as SopFlowGetDataOption).nodeId === 'string';

  // idの昇順でソート(最上位の親ノードが必ず先頭となる)
  const sortedNodes = confluenceNodes.slice().sort((a, b) => {
    const aId = fromDBFlag
      ? (a as SopFlowGetDataOption).nodeId
      : (a as Node).id;
    const bId = fromDBFlag
      ? (b as SopFlowGetDataOption).nodeId
      : (b as Node).id;
    if (aId < bId) return -1;
    if (aId > bId) return 1;
    return 0;
  });

  sortedNodes.forEach((confluenceNode) => {
    const parentNodeId = fromDBFlag
      ? // @ts-expect-error parentSopNodeNoある
        confluenceNode.parentSopNodeNo
      : // @ts-expect-error parentSopNodeNoある
        confluenceNode.getProp<string>('parentNodeId');
    if (parentNodeId) {
      map[parentNodeId] = fromDBFlag
        ? // @ts-expect-error nodeIdある
          confluenceNode.nodeId
        : // @ts-expect-error nodeIdある
          confluenceNode.id;
    }
  });
  return map;
}

/**
 * 全ての列（x）で親ノードから収束ノードまでのy範囲に含まれるパーツID一覧を取得する
 * @param matrix 2次元配列（[y][x]でノードIDが入っている）
 * @param parentNodeId 親ノードID
 * @param confluenceNodeId 収束ノードID
 * @returns パーツID一覧（親ノードID～収束ノードIDまで、両端含む、全x列対象）
 */
function getPartsBetweenParentAndConfluence(
  matrix: Matrix,
  parentNodeId: string,
  confluenceNodeId: string,
  branchNodeIds: string[],
): string[] {
  let parentY: number | null = null;
  let confluenceY: number | null = null;

  // 親ノードと収束ノードのy位置を特定（最初に見つかったものを採用）
  for (let y = 0; y < matrix.length; y++) {
    for (let x = 0; x < matrix[y].length; x++) {
      if (matrix[y][x] === parentNodeId && parentY === null) {
        parentY = y;
      }
      if (matrix[y][x] === confluenceNodeId && confluenceY === null) {
        confluenceY = y;
      }
    }
  }
  if (parentY === null || confluenceY === null) return [];

  const yStart = Math.min(parentY, confluenceY);
  const yEnd = Math.max(parentY, confluenceY);

  // 分岐パーツIDリストのx位置を取得
  const branchXPositions: number[] = [];
  for (let y = 0; y < matrix.length; y++) {
    for (let x = 0; x < matrix[y].length; x++) {
      if (
        Array.isArray(branchNodeIds) &&
        branchNodeIds.includes(matrix[y][x])
      ) {
        branchXPositions.push(x);
      }
    }
  }
  if (branchXPositions.length === 0) return [];

  const xStart = Math.min(...branchXPositions);
  const xEnd = Math.max(...branchXPositions);

  const parts: string[] = [];
  for (let y = yStart; y <= yEnd; y++) {
    for (let x = xStart; x <= xEnd; x++) {
      const id = matrix[y][x];
      // parentNodeIdは除外
      if (id && id !== parentNodeId) {
        parts.push(id);
      }
    }
  }
  return parts;
}

/**
 * 分岐パーツと収束ノードのマッピング情報を作成する
 * @param sopGraph グラフインスタンス
 * @param allNodesMatrix ノードの2次元配列
 * @returns { branchNodeId: string; partIds: string[] }[]
 */
// function createBranchList(allNodesMatrix: Matrix) {
//   const branchList: { branchNodeId: string; partIds: string[] }[] = [];
//   const confluenceNodes = state.sopGraph!.getNodes().filter(
//     node => node.getProp<string>('sopPartsCD') === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_NAME
//   );
//   const parentToConfluenceMap = createParentToConfluenceNodeMap(confluenceNodes);

//   Object.entries(parentToConfluenceMap).forEach(([parentNodeId, confluenceNodeId]) => {
//     const partsList = getPartsBetweenParentAndConfluence(allNodesMatrix, parentNodeId, confluenceNodeId);
//     branchList.push({
//       branchNodeId: parentNodeId,
//       partIds: partsList,
//     });
//   });
//   return branchList;
// }

/**
 * 全ての列（x）で親ノードから収束ノードまでのy範囲に含まれるパーツID一覧を取得する
 * @param matrix 2次元配列（[y][x]でノードIDが入っている）
 * @param parentNodeId 親ノードID
 * @param confluenceNodeId 収束ノードID
 * @returns パーツID一覧（親ノードID～収束ノードIDまで、両端含む、全x列対象）
 */
function getPartsBetweenParentAndConfluenceFromDB(
  matrix: (string | null)[][],
  parentNodeId: string,
  confluenceNodeId: string,
): string[] {
  let parentY: number | null = null;
  let confluenceY: number | null = null;

  // 親ノードと収束ノードのy位置を特定（最初に見つかったものを採用）
  for (let y = 0; y < matrix.length; y++) {
    for (let x = 0; x < matrix[y].length; x++) {
      if (matrix[y][x] === parentNodeId && parentY === null) {
        parentY = y;
      }
      if (matrix[y][x] === confluenceNodeId && confluenceY === null) {
        confluenceY = y;
      }
    }
  }
  if (parentY === null || confluenceY === null) return [];

  const yStart = Math.min(parentY, confluenceY);
  const yEnd = Math.max(parentY, confluenceY);

  const parts: string[] = [];
  for (let y = yStart; y <= yEnd; y++) {
    for (let x = 0; x < matrix[y].length; x++) {
      const id = matrix[y][x];
      if (id) parts.push(id);
    }
  }
  return parts;
}

type BranchListResult = {
  branchList: BranchList;
  parentToConfluenceMap: Record<string, string>;
};
function createBranchList(
  allNodesMatrix: Matrix,
  flowList: SopFlowGetDataOption[] = [],
): BranchListResult {
  console.log('createBranchList', allNodesMatrix);
  const branchList: BranchList = {};
  let confluenceNodes: SopFlowGetDataOption[] | Node[] = [];
  if (flowList.length) {
    confluenceNodes = flowList.filter(
      (node: SopFlowGetDataOption) =>
        node.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD,
    );
  } else {
    confluenceNodes = [];
    for (let y = 0; y < allNodesMatrix.length; y++) {
      for (let x = 0; x < allNodesMatrix[y].length; x++) {
        const nodeId = allNodesMatrix[y][x];
        if (nodeId) {
          const node = state.sopGraph!.getCellById(nodeId);
          if (!node) return { branchList: {}, parentToConfluenceMap: {} };
          const sopPartsCD = node.getProp<string>('sopPartsCD');
          if (
            sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD &&
            node.isNode()
          ) {
            (confluenceNodes as Node[]).push(node as Node);
          }
        }
      }
    }
  }
  const parentToConfluenceMap =
    createParentToConfluenceNodeMap(confluenceNodes);
  Object.entries(parentToConfluenceMap).forEach(
    ([parentNodeId, confluenceNodeId]) => {
      // 分岐パーツIDリストを作成
      const branchNodeIds: string[] = [];
      // parentNodeIdの位置を特定
      let parentRow = -1;
      let parentCol = -1;
      for (let y = 0; y < allNodesMatrix.length; y++) {
        for (let x = 0; x < allNodesMatrix[y].length; x++) {
          if (allNodesMatrix[y][x] === parentNodeId) {
            parentRow = y;
            parentCol = x;
            break;
          }
        }
        if (parentRow !== -1) break;
      }

      // 親分岐の分岐数を取得する
      const parentNode = state.sopGraph!.getCellById(parentNodeId);
      const sopPartsCd = parentNode?.getProp<string>('sopPartsCD');
      let branchNum = 0;
      if (sopPartsCd === SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD) {
        const individualPara = parentNode?.getProp(
          'individualPara',
        ) as PartButtonBranchProps;
        branchNum = individualPara.branchNumSetting || 0;
      } else if (sopPartsCd === SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD) {
        const individualPara = parentNode?.getProp(
          'individualPara',
        ) as PartSystemBranchProps;
        branchNum = individualPara.branchNumSetting || 0;
      } else {
        branchNum = 2;
      }
      // 分岐パーツIDを取得
      if (parentRow !== -1 && allNodesMatrix[parentRow + 1]) {
        // parentNodeIdのひとつ下の行、かつparentCol以降を検索
        const nextRow = allNodesMatrix[parentRow + 1];
        for (let x = parentCol; x < nextRow.length; x++) {
          if (branchNodeIds.length >= branchNum) break; // 範囲外チェック
          const id = nextRow[x];
          const node = state.sopGraph!.getCellById(id);
          const sopPartsCD = node?.getProp<string>('sopPartsCD');
          if (sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD) {
            branchNodeIds.push(id);
          }
        }
      }
      const partsList = flowList.length
        ? getPartsBetweenParentAndConfluenceFromDB(
            allNodesMatrix,
            parentNodeId,
            confluenceNodeId,
          )
        : getPartsBetweenParentAndConfluence(
            allNodesMatrix,
            parentNodeId,
            confluenceNodeId,
            branchNodeIds,
          );
      branchList[parentNodeId] = partsList;
    },
  );
  return { branchList, parentToConfluenceMap };
}

/**
 * 2次元配列に変換（X/Y座標グルーピング時に許容誤差を指定可能）
 * @param {Cell[]} nodes - ノードの配列
 * @returns {Matrix} 2次元配列形式のノードマトリックス
 */
const createNodesMatrix = (nodes: Cell[]): Matrix => {
  const nodesMatrix: Matrix = [];
  const usedIds = new Set<string>();

  const xTolerance: number = 0;
  const yTolerance: number = 10;

  // Y座標グループ化（許容誤差対応）
  const yGroupCenters: number[] = [];
  const yGroups: { [y: number]: Cell[] } = {};
  nodes.forEach((node) => {
    const pos = node.getProp<PositionOption>('position');
    if (pos.y === 0) return;
    // 許容誤差内の既存y中心を探す
    let groupY = yGroupCenters.find((yc) => Math.abs(yc - pos.y) <= yTolerance);
    if (groupY === undefined) {
      yGroupCenters.push(pos.y);
      groupY = pos.y;
    }
    if (!yGroups[groupY]) yGroups[groupY] = [];
    yGroups[groupY].push(node);
  });

  // Y座標の昇順で処理
  const sortedY = yGroupCenters.slice().sort((a, b) => a - b);

  // パーツの中央X座標でグルーピング（許容誤差あり）
  const centerXList: number[] = [];
  nodes.forEach((node) => {
    const pos = node.getProp<PositionOption>('position');
    if (pos.y === 0) return;
    const size = node.getProp<SizeOption>('size');
    const centerX = pos.x + (size?.width ?? 0) / 2;
    // 許容誤差内の既存centerXがあればそれを使う
    const found = centerXList.find((x) => Math.abs(x - centerX) <= xTolerance);
    if (found === undefined) {
      centerXList.push(centerX);
    }
  });
  centerXList.sort((a, b) => a - b);

  // 中央X座標→indexのマップを作成（許容誤差対応）
  const xIndexMap = new Map<number, number>();
  centerXList.forEach((x, idx) => {
    xIndexMap.set(x, idx);
  });
  const maxLen = centerXList.length;

  // 各行を中央X座標のindexでセット（全行同じ長さ）
  sortedY.forEach((yVal, yIdx) => {
    nodesMatrix[yIdx] = Array(maxLen).fill('');
    yGroups[yVal].forEach((node) => {
      if (usedIds.has(node.id)) return;
      const pos = node.getProp<PositionOption>('position');
      const size = node.getProp<SizeOption>('size');
      const centerX = pos.x + (size?.width ?? 0) / 2;
      // 許容誤差内のcenterXを探す
      let xIdx = -1;
      for (let i = 0; i < centerXList.length; i++) {
        if (Math.abs(centerXList[i] - centerX) <= xTolerance) {
          xIdx = i;
          break;
        }
      }
      if (xIdx === -1) xIdx = 0;
      nodesMatrix[yIdx][xIdx] = node.id;
      usedIds.add(node.id);
    });
  });

  return nodesMatrix;
};

/**
 * 削除ボタンアイコン
 * クリック処理
 * @param {*} cell - 対象セル
 * @remarks
 * 呼び出し先メソッド
 * editPartNode
 */
const cellRemove = () => {
  messageBoxCellRemoveRef.value.title = `${t('SOP.Menu.txtSopTitle')}`;
  messageBoxCellRemoveRef.value.content = `${t('SOP.Msg.cellRemove')}`;
  openDialog('cellRemoveRef');
};
/**
 * コピーボタンアイコン
 * クリック処理
 * @param {*} nodeId - コピー元ノードID
 * @remarks
 * 呼び出し先メソッド
 * editPartNode
 */
const cellCopy = (nodeId: string) => {
  const cell = state.sopGraph!.getCellById(nodeId);
  if (cell !== null && cell.isNode()) {
    if (nodeId === cell.id) {
      copyNode.value = cell;
      messageBoxSingleButtonRef.value.title = `${t('SOP.Menu.txtSopTitle')}`;
      messageBoxSingleButtonRef.value.content = `${t('SOP.Msg.copySucceeded')}`;
      messageBoxSingleButtonRef.value.type = 'info';
      openDialog('singleButtonRef');
      state.copyPartId = nodeId;
    } else {
      messageBoxSingleButtonRef.value.title = `${t('SOP.Menu.txtSopTitle')}`;
      messageBoxSingleButtonRef.value.content = `${t('SOP.Msg.selectNode')}`;
      messageBoxSingleButtonRef.value.type = 'info';
      openDialog('singleButtonRef');
    }
  }
};
// /**
//  * フォーカス削除
//  * @remarks
//  * <<呼び出しメソッド>>
//  * setSopChartMain
//  * editPartNode
//  */
// const removeFocus = () => {
//   const allParts = state.sopGraph!.getNodes();
//   allParts.forEach((item) => {
//     if (item && item.isNode()) {
//       removeNodeTools(item);
//       state.sopGraph!.unselect(item);
//     }
//   });
// };
const setIncomingNodes = (cell: Cell) => {
  const inComingNodes = state.sopGraph!.getPredecessors(cell);

  editIncomingNodesRef.value.length = 0;
  editIncomingNodesRef.value.push(cell.id);
  inComingNodes.forEach((node) => {
    const partsCD = node.getProp<string>('sopPartsCD');
    const sameCdFlag = checkPartCode(state.SOPSetData.partCds, partsCD);
    if (sameCdFlag) {
      editIncomingNodesRef.value.push(node.id);
    }
  });
};
/**
 * 値により2次元配列内のインデックスを検索
 * @param {T[][]} array - 2次元配列
 * @param {T} value - 検索する値
 * @returns {[number, number] | null} - 値が見つかった場合はそのインデックスの配列、見つからなかった場合はnull
 * @template T - 検索する値の型
 */
const findIndexIn2DArray = <T,>(
  array: T[][],
  value: T,
): [number, number] | null => {
  for (let i = 0; i < array.length; i++) {
    for (let j = 0; j < array[i].length; j++) {
      if (array[i][j] === value) {
        return [i, j];
      }
    }
  }
  return null;
};
/**
 * SOPパーツロード後、改めて作成
 * @param {*} parent - ブロック
 * @remarks
 * 呼び出し先メソッド
 * setSopChartMain
 * setSOPFlowChart
 */
const editPartNode = (parent: Node, nodeId = 0) => {
  const { sopPartConst } = state.SOPSetData;
  let nodeTypeText: string | undefined = '';
  parent.prop('size', {
    width: sopPartConst!.partWidth,
    height: sopPartConst!.partHeight,
  });
  if (nodeId === 0) {
    // [課題外] DEL ST Idを再度計算しない
    // const id = setNodeId();
    // [課題外] DEL ED
    // [課題27] ADD ST 「sop_node_id」を表示IDに置き換える
    parent.prop('dispNodeId', parent.id);
    parent.prop('sopNodeNo', parent.id);
    // [課題27] ADD ED
    parent.prop('zIndex', 2);
  }
  const titleText = parent.getAttrByPath<string>('title/text');
  // [課題外]
  const setedNodeId = parent.getProp<string>('dispNodeId');
  const parentCommonSetting = parent.getProp('commonSetting');
  // パーツ種毎の設定
  const partsCD = parent.getProp<string>('sopPartsCD');
  nodeTypeText = parent.getAttrByPath<string>('text/text');
  if (nodeTypeText === '') {
    nodeTypeText = state.partNameList.find(
      (node) => node.partsCD === partsCD,
    )?.text;
  }
  const sameCdFlag = checkPartCode(state.SOPSetData.partCds, partsCD);
  if (sameCdFlag) {
    const rectPartsData: SopRectPartsOption[] = state.SOPSetData.rectParts;
    const rectItem = rectPartsData.filter((v) => v.sopPartsCD === partsCD)[0];
    parent.setAttrs({
      line: rectItem.attrs.line,
    });
  }
  parent.setAttrs({
    body: {
      stroke: '#a8b0c2',
      fill: '#f0ffff',
    },
    image: {
      refX: 5,
      refY: 5,
    },
    title: {
      text: titleText,
      refX: 55,
      refY: 40,
      fill: '#000000',
      fontSize: 16,
      fontWeight: 'bold',
      textAnchor: 'left',
    },
    text: {
      text: nodeTypeText,
    },
    nodeId: {
      // [課題外]
      text: `ID:${setedNodeId}`,
      refX: 55,
      refY: 18,
      fontSize: 12,
      'text-anchor': 'start',
    },
    wCheck: {
      display:
        parentCommonSetting.dcheckFlg === '1' &&
        !['PartExternalDevice', 'PartSystemBranch'].includes(partsCD)
          ? 'block'
          : 'none',
      refX: 150,
      refY: 3,
      width: 20,
      height: 20,
      x: 5,
      y: 5,
    },
    abnormalityLevel1: {
      display: parentCommonSetting.devCorrLv === '1' ? 'block' : 'none',
      refX: 180,
      refY: 3,
      width: 20,
      height: 20,
      x: 5,
      y: 5,
    },
    abnormalityLevel2: {
      display: parentCommonSetting.devCorrLv === '2' ? 'block' : 'none',
      refX: 180,
      refY: 3,
      width: 20,
      height: 20,
      x: 5,
      y: 5,
    },
    abnormalityLevel3: {
      display: parentCommonSetting.devCorrLv === '3' ? 'block' : 'none',
      refX: 180,
      refY: 3,
      width: 20,
      height: 20,
      x: 5,
      y: 5,
    },
    write: {
      display: parentCommonSetting.recFillFlg === '1' ? 'block' : 'none',
      refX: 120,
      refY: 3,
      width: 20,
      height: 20,
      x: 5,
      y: 5,
    },
  });
  // if (!parent.getTools()) {
  parent.addTools({
    name: 'button',
    args: {
      zIndex: 1000,
      markup: [
        {
          tagName: 'image',
          selector: 'copy',
          attrs: {
            'xlink:href': state.imageMap.get('copy'),
            width: 20,
            height: 20,
            cursor: 'pointer',
            'pointer-events': 'all',
          },
        },
      ],
      x: '100%',
      y: 0,
      offset: { x: -25, y: 8 },
      onClick() {
        cellCopy(parent.id);
      },
    },
  });
  parent.addTools({
    name: 'button',
    args: {
      zIndex: 1000,
      markup: [
        {
          tagName: 'image',
          selector: 'trash',
          attrs: {
            'xlink:href': state.imageMap.get('trash'),
            width: 20,
            height: 20,
            cursor: 'pointer',
            'pointer-events': 'all',
          },
        },
      ],
      x: '100%',
      y: 0,
      offset: { x: -55, y: 8 },
      onClick({ view }: { view: NodeView }) {
        state.removeNodeId = view.cell.id;
        cellRemove();
      },
    },
  });
  parent.addTools({
    name: 'button',
    args: {
      zIndex: 1000,
      markup: [
        {
          tagName: 'image',
          selector: 'edit',
          attrs: {
            'xlink:href': state.imageMap.get('edit'),
            width: 20,
            height: 20,
            cursor: 'pointer',
            'pointer-events': 'all',
          },
        },
      ],
      x: '100%',
      y: 0,
      offset: { x: -85, y: 8 },
      onClick({ view }: { view: NodeView }) {
        // removeFocus();
        setIncomingNodes(view.cell);
        state.isSopInfoSettingDialogVisible = true;
        currentNodeSetting(view.cell.id);
      },
    },
  });
  if (parentCommonSetting.scnShowFlg === '0') {
    parent.addTools({
      name: 'boundary',
      args: {
        zIndex: 0,
        attrs: {
          fill: '#f0f8ff',
          stroke: '#4169e1',
          'stroke-dasharray': '7, 8',
          strokeWidth: 3,
          fillOpacity: 0,
        },
      },
    });
  }
  return parent;
};
/**
 * ブロック ノード データの編集
 * @param {*} parent - ブロック
 * @remarks
 * 呼び出し先メソッド
 * setSopChartMain
 */
const editSopBlock = (parent: Node, item?: SopFlowGetDataOption) => {
  const { sopPartConst } = state.SOPSetData;
  parent.prop('size', {
    width: sopPartConst!.partWidth,
    height: sopPartConst!.partHeight,
  });
  // [課題外] DEL ST Idを再度計算しない
  // const id = setNodeId();
  parent.prop('dispNodeId', parent.id);
  parent.prop('sopNodeNo', parent.id);
  // [課題外] DEL ED
  parent.prop('blkSopSeqNo', item?.blkSopSeqNo);
  parent.prop('blkFlowNo', item?.blkFlowNo);
  parent.setAttrs({
    body: {
      stroke: '#a8b0c2',
      fill: '#f0ffff',
    },
    line: {
      width: 8,
      height: 75,
      refX: 10,
      refY: 0,
      rx: 0,
      ry: 0,
      fill: '#ff0000',
      stroke: '#a8b0c2',
      strokeWidth: 0,
    },
    line2: {
      width: 8,
      height: 75,
      refX: 358,
      refY: 0,
      rx: 0,
      ry: 0,
      fill: '#ff0000',
      stroke: '#a8b0c2',
      strokeWidth: 0,
    },
    buttonBlock: {
      refX: 342,
      refY: 1,
    },
    button: {
      width: 15,
      height: 15,
      refX: -20,
      refY: 10,
      rx: 2,
      ry: 2,
      fill: '#f5f5f5',
      stroke: '#000000',
      cursor: 'pointer',
      event: 'node:blockDetailCollapse',
    },
    buttonSign: {
      href: state.imageMap.get('question'),
      width: 10,
      height: 10,
      refX: -17,
      refY: 12,
      rx: 2,
      ry: 2,
    },
  });
  if (!parent.getTools()) {
    parent.addTools({
      name: 'button',
      args: {
        zIndex: 1000,
        markup: [
          {
            tagName: 'image',
            selector: 'trash',
            attrs: {
              'xlink:href': state.imageMap.get('trash'),
              width: 20,
              height: 20,
              cursor: 'pointer',
              'pointer-events': 'all',
            },
          },
        ],
        x: '100%',
        y: 0,
        offset: { x: -90, y: 8 },
        onClick({ view }: { view: NodeView }) {
          state.removeNodeId = view.cell.id;
          cellRemove();
        },
      },
    });
  }

  return parent;
};
/**
 * 2次元配列に変換
 * @param {SopBlockPartOption[]} nodes - ノードの配列
 * @returns {Matrix} 2次元配列形式のノードマトリックス
 */
const createNodesMatrixFromDb = (nodeLists: SopBlockPartOption[]): Matrix => {
  let rowNumber = 0;
  let columnNumber = 0;
  nodeLists.forEach((node) => {
    if (node.y && rowNumber < node.y) {
      rowNumber = node.y;
    }
    if (node.x && columnNumber < node.x) {
      columnNumber = node.x;
    }
  });
  const nodesMatrix = new Array(rowNumber + 1)
    .fill(null)
    .map(() => new Array(columnNumber + 1).fill(null));
  nodeLists.forEach((node) => {
    const nodeY = node.y ? node.y : 0;
    const nodeX = node.x ? node.x : 0;
    nodesMatrix[nodeY][nodeX] = node.id;
  });
  return nodesMatrix;
};
/**
 * flow chartの作成
 * @param {*} flowList - flowのデータ
 * @remarks
 * 呼び出し先メソッド
 * getSOPFlowData
 */
const setSOPFlowChart = (flowList: SopFlowGetDataOption[]) => {
  state.sopGraph!.disableHistory();
  // const { sopPartConst } = state.SOPSetData;
  const startCell: SopFlowGetDataOption[] = [];
  const otherCell: SopFlowGetDataOption[] = [];
  const endCell: SopFlowGetDataOption[] = [];
  // const startPosition = graphWidth.value / 2 - sopPartConst!.startPartWidth / 2;
  // interface MaxYMap {
  //   [key: string]: { maxY: number } | undefined;
  // }
  // const maxYMap: MaxYMap = {};
  // const partsXSpace = sopPartConst!.partWidth + sopPartConst!.marginLeft;
  // const partsYSpace =
  //   sopPartConst!.partHeight +
  //   sopPartConst!.marginTop +
  //   sopPartConst!.blockSpace;
  // flowList.forEach((item) => {
  //   // eslint-disable-next-line no-param-reassign
  //   item.sopCieY = 0;
  // });
  // flowList.forEach((node, _index, flowListArr) => {
  //   if (_index === 0) {
  //     // eslint-disable-next-line no-param-reassign
  //     node.sopCieX = startPosition;
  //     // eslint-disable-next-line no-param-reassign
  //     node.sopCieY = 10;
  //   }
  //   const nextNodeList = Object.values(JSON.parse(node.sopJoin.nextNodeNo));
  //   let nextNode: SopFlowGetDataOption;
  //   const partsList: string[] = [
  //     SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD,
  //     SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD,
  //     SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD,
  //   ];
  //   const distance = partsList.includes(node.sopPartsCd)
  //     ? 0
  //     : sopPartConst!.partHeight + sopPartConst!.marginTop;
  //   if (nextNodeList.length === 1) {
  //     nextNode = flowListArr.find(
  //       (arrItem) => arrItem.sopNodeNo === nextNodeList[0],
  //     )!;
  //     if (!nextNode) {
  //       // ここでガード
  //       return;
  //     }
  //     if (
  //       node.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_START_CD &&
  //       nextNode.sopPartsCd !== SOP_PARTS_VARIABLES.PART_SPEC_END_CD
  //     ) {
  //       nextNode.sopCieX =
  //         startPosition +
  //         sopPartConst!.startPartWidth / 2 -
  //         sopPartConst!.partWidth / 2;
  //       nextNode.sopCieY =
  //         node.sopCieY +
  //         sopPartConst!.blockSpace +
  //         sopPartConst!.marginTop +
  //         sopPartConst!.startPartHeight;
  //     } else if (nextNode.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_END_CD) {
  //       nextNode.sopCieX = startPosition;
  //       nextNode.sopCieY = node.sopCieY + sopPartConst!.blockSpace + distance;
  //     } else if (
  //       nextNode.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD
  //     ) {
  //       if (
  //         !maxYMap[nextNode.sopNodeNo] ||
  //         node.sopCieY > maxYMap[nextNode.sopNodeNo]!.maxY
  //       ) {
  //         maxYMap[nextNode.sopNodeNo] = { maxY: node.sopCieY };
  //       }
  //       const parentNode = flowListArr.find(
  //         (arrItem) => arrItem.sopNodeNo === nextNode.parentSopNodeNo,
  //       )!;
  //       if (!parentNode) {
  //         // parentNodeが見つからない場合もスキップ
  //         return;
  //       }
  //       nextNode.sopCieX =
  //         parentNode.sopCieX +
  //         sopPartConst!.partWidth / 2 -
  //         sopPartConst!.addPartWidth / 2;
  //       nextNode.sopCieY =
  //         maxYMap[nextNode.sopNodeNo]!.maxY + partsYSpace + distance;
  //     } else if (nextNode.sopCieY === 0) {
  //       const isConfluence =
  //         node.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD;
  //       nextNode.sopCieX =
  //         node.sopCieX -
  //         (isConfluence
  //           ? sopPartConst!.partWidth / 2 - sopPartConst!.addPartWidth / 2
  //           : 0);
  //       nextNode.sopCieY =
  //         node.sopCieY +
  //         (isConfluence ? sopPartConst!.blockSpace : partsYSpace);
  //     }
  //   } else if (nextNodeList.length > 1) {
  //     const nextNodeNoList = nextNodeList
  //       .map((nextNodeItem) =>
  //         flowListArr.find((arrItem) => arrItem.sopNodeNo === nextNodeItem),
  //       )
  //       .filter(Boolean);
  //     nextNodeNoList.forEach((nodeItem, index) => {
  //       if (!nodeItem) return;
  //       if (
  //         nodeItem.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD
  //       ) {
  //         // eslint-disable-next-line no-param-reassign
  //         nodeItem.sopCieX =
  //           node.sopCieX +
  //           sopPartConst!.partWidth / 2 -
  //           sopPartConst!.addPartWidth / 2;
  //         // eslint-disable-next-line no-param-reassign
  //         nodeItem.sopCieY = maxYMap[nodeItem.sopNodeNo]
  //           ? maxYMap[nodeItem.sopNodeNo]!.maxY + partsYSpace
  //           : node.sopCieY +
  //             partsYSpace +
  //             sopPartConst!.partHeight +
  //             sopPartConst!.addPartHeight +
  //             sopPartConst!.marginTop;
  //       } else if (nodeItem.sopCieY === 0) {
  //         // eslint-disable-next-line no-param-reassign
  //         nodeItem.sopCieX = node.sopCieX + partsXSpace * index;
  //         // eslint-disable-next-line no-param-reassign
  //         nodeItem.sopCieY = node.sopCieY + partsYSpace;
  //       }
  //     });
  //   }
  //   const addpartObj = {
  //     nodeId: '',
  //     sopNodeNo: '',
  //     sopCieX: 0,
  //     sopCieY: 0,
  //     sopPartsCd: '',
  //     sopPartsNm: '',
  //     helpSetting: {
  //       helpFileType: 'N',
  //     },
  //     sopJoin: {
  //       nextCondSeq: 1,
  //       nextNodeNo: '',
  //     },
  //   };
  //   /**
  //    * sopPartsCd 91: controlPartStart
  //    * sopPartsCd 94: controlPartEnd
  //    * sopPartsCd 92: PartSpecConfluence
  //    * sopPartsCd 02: PartSopTimer
  //    * sopPartsCd 01: PartInstructionConfirm
  //    * sopPartsCd 03: PartNumericTextInput
  //    * sopPartsCd 08: PartButtonBranch
  //    * sopPartsCd 09: PartSystemBranch
  //    * sopPartsCd 10: PartExternalDevice
  //    * sopPartsCd 15: PartWeightCalibration
  //    */
  //   const sopPartsCdList = [
  //     SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD,
  //     SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_CD,
  //     SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD,
  //     SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_CD,
  //     SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD,
  //     SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD,
  //     SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD,
  //     SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD,
  //     SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD,
  //     SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD,
  //   ];
  //   if (node.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_START_CD) {
  //     const addPart = createAddPart(node.sopCieX, node.sopCieY);
  //     addpartObj.nodeId = addPart.id;
  //     addpartObj.sopNodeNo = addPart.id;
  //     addpartObj.sopCieX =
  //       node.sopCieX +
  //       sopPartConst!.startPartHeight / 2 -
  //       sopPartConst!.addPartWidth / 2;
  //     addpartObj.sopCieY =
  //       node.sopCieY +
  //       sopPartConst!.startPartHeight +
  //       sopPartConst!.addPartHeight / 2 +
  //       sopPartConst!.blockSpace / 2;
  //     addpartObj.sopPartsCd = SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD;
  //     addpartObj.sopPartsNm = SOP_PARTS_VARIABLES.PART_SPEC_JOIN_NAME;
  //     addpartObj.sopJoin.nextNodeNo = JSON.stringify({
  //       condBrDst1: JSON.parse(node.sopJoin.nextNodeNo).condBrDst1,
  //     });
  //     // eslint-disable-next-line no-param-reassign
  //     node.sopJoin.nextNodeNo = JSON.stringify({ condBrDst1: addPart.id });
  //     // @ts-expect-error 簡潔なobjectをflowListに保存します。
  //     flowList.push(addpartObj);
  //     // @ts-expect-error 動的に変数判断
  //   } else if (sopPartsCdList.includes(node.sopPartsCd)) {
  //     const nodeConditionBranch =
  //       JSON.parse(node.individualPara).IndividualSetting?.conditionBranch ||
  //       JSON.parse(node.individualPara).ConditionSetting?.conditionBranch;
  //     const branchNextNodes = JSON.parse(node.sopJoin.nextNodeNo);
  //     Object.keys(branchNextNodes).forEach((item, index) => {
  //       if (branchNextNodes[item] !== '') {
  //         const findItem = flowListArr.find(
  //           (flowListArrItem) =>
  //             flowListArrItem.sopNodeNo === branchNextNodes[item],
  //         );
  //         // @ts-expect-error 既にこのリストを定義しました。
  //         const findItemSopPartCd = findItem.sopPartsCd;
  //         const positionX =
  //           node.sopCieX +
  //           sopPartConst!.blockWidth +
  //           sopPartConst!.addPartWidth;
  //         const positionY =
  //           node.sopCieY +
  //           sopPartConst!.addPartHeight / 2 +
  //           sopPartConst!.partHeight +
  //           sopPartConst!.blockSpace / 2;
  //         const position = getPartPosition(
  //           { x: positionX, y: positionY },
  //           index,
  //         );
  //         const addPart = createAddPart(position.x, position.y);
  //         const diff =
  //           // @ts-expect-error 既にこのリストを定義しました。
  //           findItem.sopCieX +
  //           sopPartConst!.partWidth / 2 -
  //           sopPartConst!.addPartWidth / 2;
  //         if (findItem === undefined) {
  //           return;
  //         }
  //         const branchAddpart = {
  //           nodeId: addPart.id,
  //           sopNodeNo: addPart.id,
  //           // もしパーツが重なっていますし、ループがありますし、収束ノードではないし、分岐点を移動します。
  //           sopCieX:
  //             findItem.sopCieY > position.y &&
  //             findItemSopPartCd !== SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD
  //               ? diff
  //               : position.x,
  //           sopCieY: position.y,
  //           sopPartsCd:
  //             nodeConditionBranch === '0'
  //               ? SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD
  //               : SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD,
  //           helpSetting: {
  //             helpFileType: 'N',
  //           },
  //           sopPartsNm:
  //             nodeConditionBranch === '0'
  //               ? SOP_PARTS_VARIABLES.PART_SPEC_JOIN_NAME
  //               : SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_NAME,
  //           sopJoin: {
  //             nextNodeNo: JSON.stringify({
  //               condBrDst1: JSON.parse(node.sopJoin.nextNodeNo)[item],
  //             }),
  //           },
  //         };
  //         // eslint-disable-next-line no-param-reassign
  //         node.sopJoin.nextNodeNo = JSON.parse(node.sopJoin.nextNodeNo);
  //         Object.assign(node.sopJoin.nextNodeNo, { [`${item}`]: addPart.id });
  //         // eslint-disable-next-line no-param-reassign
  //         node.sopJoin.nextNodeNo = JSON.stringify(node.sopJoin.nextNodeNo);
  //         // @ts-expect-error 簡潔なobjectをflowListに保存します。
  //         flowList.push(branchAddpart);
  //         // }
  //       }
  //     });
  //   } else if (
  //     node.sopPartsCd !== SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD &&
  //     node.sopPartsCd !== SOP_PARTS_VARIABLES.PART_SPEC_END_CD
  //   ) {
  //     const addPart = createAddPart(node.sopCieX, node.sopCieY);
  //     addpartObj.nodeId = addPart.id;
  //     addpartObj.sopNodeNo = addPart.id;
  //     addpartObj.sopCieX =
  //       node.sopCieX +
  //       sopPartConst!.defaultWidth / 2 -
  //       sopPartConst!.addPartWidth / 2;
  //     addpartObj.sopCieY =
  //       node.sopCieY + sopPartConst!.marginTop + sopPartConst!.blockSpace;
  //     addpartObj.sopPartsCd = SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD;
  //     addpartObj.sopPartsNm = SOP_PARTS_VARIABLES.PART_SPEC_JOIN_NAME;
  //     addpartObj.sopJoin.nextNodeNo = JSON.stringify({
  //       condBrDst1: JSON.parse(node.sopJoin.nextNodeNo).condBrDst1,
  //     });
  //     // eslint-disable-next-line no-param-reassign
  //     node.sopJoin.nextNodeNo = JSON.stringify({ condBrDst1: addPart.id });
  //     // @ts-expect-error 簡潔なobjectをflowListに保存します。
  //     flowList.push(addpartObj);
  //   }
  // });
  flowList.forEach((node) => {
    const cell = node;
    cell.nodeId = node.sopNodeNo.replace(`${node.sopFlowNo}-`, '');
    cell.sopPartsCd = node.sopPartsCd;
    switch (cell.helpSetting.helpFileType) {
      case 'N':
        cell.helpSetting.helpFileType = 'N';
        break;
      case 'I':
        cell.helpSetting.helpFileType = 'I';
        break;
      case 'M':
        cell.helpSetting.helpFileType = 'M';
        break;
      case 'P':
        cell.helpSetting.helpFileType = 'P';
        break;
      default:
        break;
    }
    cell.commonSetting = { ...cell.commonSetting, ...cell.helpSetting };

    // 外部機器通信の子パーツ情報を親パーツの個別設定に変換
    if (
      cell.sopPartsCd === SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD &&
      cell.childNodeFlg === '0'
    ) {
      const jsonIndiv = JSON.parse(cell.individualPara);
      const individualPara =
        jsonIndiv.IndividualSetting as PartExternalDeviceProps;
      individualPara.upperLowerLimitCheck = cell.upperLowerSetting.thJudgeFlg;
      individualPara.numericTextInput = [];
      state.childNodeList.forEach((childItem) => {
        if (cell.sopNodeNo !== childItem.parentSopNodeNo) {
          return;
        }
        const childJsonIndiv = JSON.parse(childItem.individualPara);
        const childindividualPara =
          childJsonIndiv.IndividualSetting as NumericTextInputProps;
        const numericItem: NumericTextInputProps = {
          itemText: childindividualPara.itemText,
          instructionValueSetting: childindividualPara.instructionValueSetting,
          instructionValue: childindividualPara.instructionValue,
          sourceItemNode: childindividualPara.sourceItemNode,
          sourceItem: childindividualPara.sourceItem,
          sourceItemId: childindividualPara.sourceItem,
          sourceItemTag: childindividualPara.sourceItemTag,
          outputSetting: childindividualPara.outputSetting,
          outputValue: childindividualPara.outputValue,
          instructionValueType: childindividualPara.instructionValueType,
          judgeType: childItem.upperLowerSetting.thValType,
          deviationLowerLimit:
            childItem.upperLowerSetting.thValLlmt === null
              ? ''
              : String(childItem.upperLowerSetting.thValLlmt),
          deviationUpperLimit:
            childItem.upperLowerSetting.thValUlmt === null
              ? ''
              : String(childItem.upperLowerSetting.thValUlmt),
        };
        individualPara.numericTextInput?.push(numericItem);
      });
      const externalIndiv = {
        IndividualSetting: individualPara,
        ConditionSetting: '',
      };
      cell.individualPara = JSON.stringify(externalIndiv);
    }
    if (node.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_START_CD) {
      startCell.push(cell);
    } else if (node.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_END_CD) {
      endCell.push(cell);
    } else {
      otherCell.push(cell);
    }
  });
  // --- 収束ノードIDを分岐親パーツに追加 ---
  const confluenceList = flowList.filter(
    (node) => node.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD,
  );
  confluenceList.forEach((confluenceNode) => {
    const parentNodeId = confluenceNode.parentSopNodeNo;
    if (!parentNodeId) return;
    // 分岐親パーツを型安全に取得
    const parentNode = flowList.find((node) => node.sopNodeNo === parentNodeId);
    if (parentNode) {
      (
        parentNode as SopFlowGetDataOption & { confluenceNodeId?: string }
      ).confluenceNodeId = confluenceNode.sopNodeNo;
    }
  });
  // [課題外] MOD ST ノードの番号計算方法を修正します。
  // ノードIDの取得
  // const dspSeqs: number[] = [];
  // otherCell.forEach((item) => {
  //   const sameCdFlag = checkPartCode(state.SOPSetData.partCds, item.sopPartsCd);
  //   const blockNodeFlag = checkBlockPartCode(state.SOPSetData, item.sopPartsCd);
  //   if ((sameCdFlag || blockNodeFlag) && item.blkFlowNo === '_') {
  //     dspSeqs.push(item.dspSeq);
  //   }
  // });
  // const maxIndex = dspSeqs.indexOf(Math.max(...dspSeqs));
  // --
  const getAllNodeList = [...flowList, ...state.blockExpandList];
  const findMaxId = getAllNodeList.filter(
    (item) => item.sopNodeNo.length === 4,
  );
  const maxNum = Math.max(
    ...findMaxId.map((item) => parseInt(item.sopNodeNo, 10)),
  );
  countUpId.value = maxNum;
  // [課題外] MOD ED
  // const FlowObj = formatSOPFlowEdges(startCell, otherCell, endCell);
  const nodeData = getSOPFlowChartData(
    flowList,
    state.SOPSetData,
    state.imageMap,
    t,
  );
  // @ts-expect-error 一旦assignします
  const newNodeMatrix = createNodesMatrixFromDb(nodeData);

  // 分岐パーツと収束ノードのマッピング情報を作成する（行の昇順で作成）
  const { branchList, parentToConfluenceMap } = createBranchList(
    newNodeMatrix,
    flowList,
  );

  // state.sopGraph!.fromJSON({ nodes: nodeData });
  // createSOPFlowEdges(FlowObj.edges);
  // eslint-disable-next-line @typescript-eslint/no-use-before-define
  redrawGraphFromMatrix(
    newNodeMatrix,
    branchList,
    parentToConfluenceMap,
    // @ts-expect-error 一旦assignします
    nodeData,
  );

  // --- 収束ノードのプロパティに親ノードIDを設定し、親ノードのプロパティに収束ノードIDを設定（x6 Node型オブジェクトに対して） ---
  const confluenceNodes = state
    .sopGraph!.getNodes()
    .filter(
      (node) =>
        node.getProp<string>('sopPartsCD') ===
        SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD,
    );
  confluenceNodes.forEach((confluenceNode) => {
    // 収束ノードの親ノードIDを取得
    const confluenceItem = confluenceList.find(
      (item) => item.sopNodeNo === confluenceNode.id,
    );
    const parentNodeId = confluenceItem
      ? confluenceItem.parentSopNodeNo
      : undefined;
    if (!parentNodeId) return;
    // 親ノード（Node型）を取得
    const parentNode = state.sopGraph!.getCellById(parentNodeId);
    if (parentNode && parentNode.isNode()) {
      // 親ノードに収束ノードIDをセット
      parentNode.prop('confluenceNodeId', confluenceNode.id);
      // 収束ノードに親ノードIDをセット
      confluenceNode.prop('parentNodeId', parentNode.id);
    }
  });

  state.sopFlowChartType = 'EDIT';

  // 各パーツデザイン調整
  otherCell.forEach((item) => {
    const sameCdFlag = checkPartCode(state.SOPSetData.partCds, item.sopPartsCd);
    const blockFlag = checkPartCode(
      state.SOPSetData.blockPartCds,
      item.sopPartsCd,
    );
    if (sameCdFlag) {
      const currentCell = state.sopGraph!.getCellById(item.nodeId);
      if (currentCell.isNode()) {
        currentCell.prop('settingConfirmFlg', true);
      }
      if (currentCell.isNode() && item.blkFlowNo === '_') {
        // --
        editPartNode(currentCell, item.dspSeq);
      }
    } else if (blockFlag) {
      const currentCell = state.sopGraph!.getCellById(item.nodeId);
      if (currentCell.isNode()) {
        // --
        editSopBlock(currentCell, item);
      }
    }
  });

  // TODO:一次対応 後で消す
  // endノードの直前がaddPart（join/branch）の場合、addPartのx位置をendノードと同じにする
  const allNodes = state.sopGraph!.getNodes();
  // y座標で降順ソートし、最初の要素が一番下
  const sorted = allNodes.sort((a, b) => {
    const ay = a.getProp<PositionOption>('position').y;
    const by = b.getProp<PositionOption>('position').y;
    return by - ay;
  });
  sorted[1].setProp('position', {
    x: sorted[sorted.length - 2].getProp<PositionOption>('position').x,
    y: sorted[1].getProp<PositionOption>('position').y,
  });

  // state.sopGraph!.zoomToFit({ maxScale: 0.9 });
  // state.sopGraph!.centerContent();
  // フローの高さはそのままで、左右を中央に
  const contentBBox = state.sopGraph!.getContentBBox();
  const containerWidth = state.sopGraph!.container.clientWidth;
  const scale = state.sopGraph!.zoom();
  const currentTranslate = state.sopGraph!.translate();
  const dx =
    (containerWidth - contentBBox.width * scale) / 2 / scale - contentBBox.x;
  state.sopGraph!.translate(dx, currentTranslate.ty);

  state.sopGraph!.enableHistory();
};
/**
 * ブロックの詳細ページの拡張
 * @param {*} node - テンプレート or ブロック
 * @remarks
 * <<呼び出しメソッド>>
 * setSopChartMain
 */
const getBlockDetail = (node: Node) => {
  state.SOPChartType = 'BE-PREVIEW';
  const commonSetting = node.getProp('commonSetting');
  const blkFlowNo = node.getProp('blkFlowNo');
  const blockExpandList = state.blockExpandList
    .filter((blockItem) => blockItem.parentSopNodeNo === node.id)
    .map((item) => JSON.parse(JSON.stringify(item)));
  const blockDataSet = {
    sopFlowNo: blkFlowNo,
    sopFlowNmJp: commonSetting.sopNodeNmJp,
    sopCatTxtJp: '',
    untSopCat: 'B',
    updDts: '',
    flowList: blockExpandList,
  };
  const sopPartsCD = node.getProp<string>('sopPartsCD');
  state.chartOptions = {
    cellData: {
      nodes: [],
      edges: [],
    },
    blockData: blockDataSet,
    blockNo: sopPartsCD,
  };
  state.sopGraph!.cleanClipboard();
  state.sopGraph!.cleanSelection();
  state.SOPAddFlag = true;
  state.blockSeleckValue = blkFlowNo;
};
/**
 * add Partの作成
 * @param {*} node - ブロック
 * @remarks
 * 呼び出しメソッド
 * setSopChartMain
 */
const setPartPosition = (node: Node) => {
  state.sopGraph!.batchUpdate(() => {
    if (state.checkedAddPartId !== '') {
      const currentCell = state.sopGraph!.getCellById(state.checkedAddPartId);
      if (currentCell !== null && currentCell.isNode()) {
        const allParts = state.sopGraph!.getNodes();
        allParts.forEach((item) => {
          const partsCD = item.getProp<string>('sopPartsCD');
          const sameCdFlag = checkPartCode(
            state.SOPSetData.addPartCds,
            partsCD,
          );
          if (sameCdFlag) {
            item.setAttrs({
              body: {
                stroke: '#FFFFFF',
                strokeWidth: 0,
                fill: 'transparent',
              },
            });
          }
        });
        // Partを挿入する箇所
        const translateVal = getTranslatePosition(currentCell, node);
        node.translate(translateVal.translateX, translateVal.translateY);
        state.currentPartId = node.id;
        const cellPosition = currentCell.getProp<PositionOption>('position');
        createAddPart(cellPosition.x, cellPosition.y);
        return node;
      }
    }
    state.sopGraph!.removeNode(node.id);
    return false;
  });
};
/**
 * 接続線の編集
 * @param {*} node - ブロック
 * @remarks
 * 呼び出しメソッド
 * setSopChartMain
 */
const setEdgeForClonePart = (node: Node) => {
  const currentPart = state.sopGraph!.getCellById(state.currentPartId);
  if (currentPart && currentPart.isNode()) {
    const zIndex = currentPart.getProp<number>('zIndex');
    currentPart.prop('cloneFlag', true);
    currentPart.prop('zIndex', zIndex + 1);
    sopEdgeEdit(currentPart, node.id);
    return currentPart;
  }
  return null;
};
/**
 * 選択したノードを移動できるかどうかを決定
 * @remarks
 * 呼び出しメソッド
 * setSopChartMain
 */
const checkSelectedCellsMoveFlag = () => {
  const cellData = getDataForSelectedCells();
  state.currentPartId = cellData.startPartId;
  state.selectedStartPartId = cellData.startPartId;
  state.selectedEndPartId = cellData.endPartId;
  // 分岐ノードはドラッグ不可
  if (cellData.condBranch) {
    return false;
  }
  // 初期ノードが追加される ノードはドラッグできない
  const startCdFlag = checkPartCode(
    state.SOPSetData.addPartCds,
    cellData.startPartCd,
  );
  if (startCdFlag) {
    return false;
  }
  // 終了ノードは追加ノードでなければならない。
  const sameCdFlag = checkPartCode(
    state.SOPSetData.partCds,
    cellData.endPartCd,
  );
  const controlFlag = checkPartCode(
    state.SOPSetData.controlPartCds,
    cellData.endPartCd,
  );
  const blockFlag = checkPartCode(
    state.SOPSetData.blockPartCds,
    cellData.endPartCd,
  );
  if (sameCdFlag || controlFlag || blockFlag) {
    return false;
  }
  return true;
};
/**
 * 切り取るノードを選択
 * @remarks
 * 呼び出しメソッド
 * setSopChartMain
 */
const setSelectionPart = () => {
  if (state.selectedStartPartId !== '') {
    const startCell = state.sopGraph!.getCellById(state.selectedStartPartId);
    if (startCell.isNode()) {
      const incomingEdges = state.sopGraph!.getIncomingEdges(startCell);
      if (incomingEdges) {
        incomingEdges.forEach((edge) => {
          state.sopGraph!.removeEdge(edge.id);
        });
      }
    }
  }
  if (state.selectedEndPartId !== '') {
    const endCell = state.sopGraph!.getCellById(state.selectedEndPartId);
    if (endCell.isNode()) {
      const outgoingEdges = state.sopGraph!.getOutgoingEdges(endCell);
      if (outgoingEdges) {
        outgoingEdges.forEach((edge) => {
          state.sopGraph!.removeEdge(edge.id);
        });
      }
    }
  }
};
/**
 * ノードのドラッグの開始
 * @param {*} node - ブロック
 * @remarks
 * 呼び出しメソッド
 * setSopChartMain
 */
const sopPartDragStart = (node: Node) => {
  state.checkedAddPartId = '';
  state.checkeAddPartFlag = false;
  // [課題408] MOD ST UUIDを置き換える
  const cloneId = setNodeId();
  const cloneData = node.clone();
  state.sopClonePartId = cloneId;
  state.currentPartId = node.id;
  state.sopPartAddType = 'clonePartNode';
  const storeData = getNodeData(cloneData);
  // [課題27] ADD ST 「sop_node_id」を表示IDに置き換える
  storeData.id = cloneId;
  // [課題408] MOD ED
  // [課題27] ADD ED
  const clonePart = state.sopGraph!.addNode({ ...storeData });
  return clonePart;
};
/**
 * ルートノードの位置の変化
 * @param {*} nodeId - ブロック
 * @remarks
 * 呼び出し先メソッド
 * setSopChartMain
 */
const getRootPartPositionChange = (nodeId: string) => {
  if (nodeId !== '') {
    const nodeItem = state.sopGraph!.getCellById(nodeId);
    const bbox = nodeItem.getBBox();
    const allNodes = state.sopGraph!.getNodes();
    allNodes.forEach((item) => {
      const partsCD = item.getProp<string>('sopPartsCD');
      const sameCdFlag = checkPartCode(state.SOPSetData.addPartCds, partsCD);
      if (sameCdFlag) {
        item.setAttrs({
          body: {
            stroke: '#FFFFFF',
            strokeWidth: 0,
            fill: 'transparent',
          },
        });
        const targetBBox = item.getBBox();
        const partFlag = targetBBox.isIntersectWithRect(bbox);
        if (partFlag) {
          state.sopPartAddType = 'setTargetBBox';
          state.checkedAddPartId = item.id;
          item.setAttrs({
            body: {
              stroke: '#D9A426',
              strokeWidth: 2,
              fill: 'transparent',
            },
          });
        }
      }
    });
  }
};
/**
 * ブロックの作成
 * @param {*} addPart - ブロック
 * @remarks
 * 呼び出し先メソッド
 * setSopChartMain
 */
const setPartNodeToFlow = (addPart: Node) => {
  state.sopGraph!.batchUpdate(() => {
    const currentCell = state.sopGraph!.getCellById(state.checkedAddPartId);
    const { sopPartConst } = state.SOPSetData;
    if (currentCell !== null && currentCell.isNode()) {
      const currentPart = state.sopGraph!.getCellById(state.currentPartId);
      if (currentPart !== null && currentPart.isNode()) {
        // [課題管理表][No.179] ST パラメータにノード追加を表記用フラグを追加
        // const addTrans = getTranslatePosition(currentPart, addPart);
        const addTrans = getTranslatePosition(currentPart, addPart, true);
        // [課題管理表][No.179] ED
        addPart.translate(addTrans.translateX, addTrans.translateY);
        // 挿入するPartのOutgoingEdges
        const outgoingEdges = state.sopGraph!.getOutgoingEdges(currentCell);
        if (outgoingEdges) {
          outgoingEdges.forEach((edge) => {
            const edgeTarget: string = edge.getProp('target').cell;
            // [課題外] DEL ST ノード間関係を判定するため、Edgeの削除は後ろに移動する
            // state.sopGraph!.removeEdge(edge.id);
            // [課題外] DEL ED ノード間関係を判定するため、Edgeの削除は後ろに移動する
            // [課題管理表][No.120] MOD ST
            // addBlockTargetEdge(addPart.id, edgeTarget);
            // edgeTargetはaddPartの上側である場合、連結線の経過ポイントを指定する必要
            let goback: boolean = false;
            if (state.lastNormalNode !== null) {
              goback =
                state.lastNormalNode!.getProp<PositionOption>('position').y >
                state
                  .sopGraph!.getCellById(edgeTarget)
                  .getProp<PositionOption>('position').y;
            }
            addBlockTargetEdge(addPart.id, edgeTarget, goback);
            // [課題管理表][No.120] MOD ED

            const targetCell = state.sopGraph!.getCellById(edgeTarget);
            if (targetCell.isNode()) {
              // 次のPartの箇所を調整
              // [課題管理表][No.179] ST パラメータにノード追加を表記用フラグを追加
              // const transVal = getTranslatePosition(addPart, targetCell);
              const transVal = getTranslatePosition(addPart, targetCell, true);
              const addPartPosition =
                addPart.getProp<PositionOption>('position');
              const targetCellPosition =
                targetCell.getProp<PositionOption>('position');
              // [課題管理表][No.179] ED
              const successors = state.sopGraph!.getSuccessors(addPart, {
                deep: true,
              });
              // [課題管理表][No.120] MOD ST
              // 直前ノードは子ノードリストに含まれる場合、ループフラグを立てる
              const incomingNode: Cell = state.sopGraph!.getCellById(
                state.checkedAddPartId,
              );
              const incomingSuccessors = state.sopGraph!.getSuccessors(
                incomingNode,
                {
                  deep: true,
                },
              );
              const incomingNodePredecessors = state.sopGraph!.getPredecessors(
                incomingNode,
                {
                  deep: true,
                },
              );
              const incomingY = incomingNode.getProp('position').y;
              let isLoop = false;
              if (successors.length > 0) {
                const incoming: Cell[] = state.sopGraph!.getNeighbors(
                  incomingNode,
                  {
                    incoming: true,
                  },
                );
                isLoop = successors.includes(incoming[0]);
              }
              const coverIndex = findCoverIndex(incomingSuccessors);
              const mainNode =
                incomingNodePredecessors[
                  branchNodeIndex(incomingNodePredecessors)
                ];
              if (
                mainNode &&
                mainNode.getProp('position').y >
                  // @ts-expect-error すでにcurrentPartをチェックしました。
                  currentPart.getProp('position').y
              ) {
                successors?.forEach((item) => {
                  const itemY = item.getProp('position').y;
                  if (incomingY < itemY) {
                    item.translate(
                      0,
                      targetCellPosition.y <
                        addPartPosition.y + sopPartConst!.addPartHeight
                        ? sopPartConst!.marginTop +
                            sopPartConst!.partHeight +
                            sopPartConst!.blockSpace
                        : transVal.translateY,
                    );
                  }
                });
              } else {
                successors?.forEach((item) => {
                  const itemY = item.getProp('position').y;
                  // 下にノードを追加する場合のみに、影響のあるノードの座標の調整を行う
                  // 別の分岐にあるノードでは座標の調整を行わない
                  if (incomingY < itemY) {
                    // 収束ノードはx軸を動かさない
                    if (
                      isNodesInSameBranch(incomingNode.id, item.id) ||
                      (coverIndex === -1 && !isLoop)
                    ) {
                      item.translate(
                        0,
                        targetCellPosition.y <
                          addPartPosition.y + sopPartConst!.addPartHeight
                          ? sopPartConst!.marginTop +
                              sopPartConst!.partHeight +
                              sopPartConst!.blockSpace
                          : transVal.translateY,
                      );
                    }
                  }
                });
              }
              // [課題管理表][No.120] MOD ED
            }
            // [課題外] ADD ST ノード間関係を判定するため、Edgeの削除は前からここに移動する
            state.sopGraph!.removeEdge(edge.id);
            // [課題外] ADD ED ノード間関係を判定するため、Edgeの削除は前からここに移動する
          });
        }
        addBlockTargetEdge(currentCell.id, currentPart.id);
        addPathTargetEdge(currentPart.id, addPart.id);
        // 最下ノードの連結線を経過するポイントを計算して再作成する
        if (outgoingEdges) {
          outgoingEdges.forEach((edge) => {
            const edgeTarget: string = edge.getProp('target').cell;
            let goback = false;
            if (state.lastNormalNode) {
              goback =
                state.lastNormalNode.getProp<PositionOption>('position').y >
                state
                  .sopGraph!.getCellById(edgeTarget)
                  .getProp<PositionOption>('position').y;
            }
            const edgeTargetCell = state.sopGraph!.getCellById(edgeTarget);
            const sopPartsCD = edgeTargetCell.getProp<string>('sopPartsCD');
            const sameCdFlag = checkPartCode(
              state.SOPSetData.partCds,
              sopPartsCD,
            );
            if (
              sameCdFlag ||
              [
                SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_CD,
                SOP_PARTS_VARIABLES.PART_SPEC_END_CD,
              ].includes(sopPartsCD)
            ) {
              addBlockTargetEdge(addPart.id, edgeTarget, goback);
            } else {
              addPathTargetEdge(addPart.id, edgeTarget);
            }
          });
        }
        state.selectedPartId = currentPart.id;
        state.sopPartListShowType = false;
        currentNodeSetting(currentPart.id);
      }
    } else {
      state.sopGraph!.removeNode(state.currentPartId);
    }
    coverAddVertices();
    return addPart;
  });
};
/**
 * チェックノードの位置を調整
 * @remarks
 * 呼び出し先メソッド
 * setSopChartMain
 */
const setSelectionDragPart = () => {
  if (state.checkedAddPartId !== '') {
    const currentCell = state.sopGraph!.getCellById(state.checkedAddPartId);
    if (currentCell !== null && currentCell.isNode()) {
      const allParts = state.sopGraph!.getNodes();
      allParts.forEach((item) => {
        const partsCD = item.getProp<string>('sopPartsCD');
        const sameCdFlag = checkPartCode(state.SOPSetData.addPartCds, partsCD);
        if (sameCdFlag) {
          item.setAttrs({
            body: {
              stroke: '#FFFFFF',
              strokeWidth: 0,
              fill: 'transparent',
            },
          });
        }
      });
      const currentPart = state.sopGraph!.getCellById(state.currentPartId);
      if (currentPart.isNode()) {
        const outgoingEdges = state.sopGraph!.getOutgoingEdges(currentCell);
        if (outgoingEdges) {
          const nodePosition = currentPart.getProp<PositionOption>('position');
          const translateX = state.currentPosition.x - nodePosition.x;
          const translateY = state.currentPosition.y - nodePosition.y;
          const selCells = state.sopGraph!.getSelectedCells();
          if (selCells) {
            selCells.forEach((item) => {
              if (item.isNode()) {
                item.translate(translateX, translateY);
              }
            });
          }
          const textValue = currentCell.getAttrByPath<string>('text/text');
          messageBoxSingleButtonRef.value.title = `${t('SOP.Menu.txtSopTitle')}`;
          messageBoxSingleButtonRef.value.content = `${t('SOP.Msg.cannotSetCondBranch', [textValue])}`;
          openDialog('singleButtonRef');
        } else {
          addBlockTargetEdge(currentCell.id, currentPart.id);
          const transVal = getTranslatePosition(currentCell, currentPart);
          const successors = state.sopGraph!.getSuccessors(currentCell, {
            deep: true,
          });
          successors?.forEach((successorItem) => {
            successorItem.translate(transVal.translateX, transVal.translateY);
          });
        }
      }
      state.sopPartAddType = '';
      state.checkedAddPartId = '';
    }
  }
};
/**
 * ノードを削除
 * @param {*} nodeId - データのId
 * @remarks
 * 呼び出し先メソッド
 * setSopChartMain
 */
const removeDragPart = (nodeId: string) => {
  const dragCell = state.sopGraph!.getCellById(nodeId);
  if (dragCell !== null && dragCell.isNode()) {
    const dragNeighbors = getCurrentNeighbors(dragCell);
    state.sopGraph!.removeNode(nodeId);
    if (dragNeighbors.incomingList.length > 0) {
      dragNeighbors.incomingList.forEach((item) => {
        const incomingCell = state.sopGraph!.getCellById(item.value);
        if (incomingCell.isNode()) {
          dragNeighbors.outgoingList.forEach((outgoing) => {
            const outgoingCell = state.sopGraph!.getCellById(outgoing.value);
            if (outgoingCell.isNode()) {
              const outgoingNeighbors: Cell[] = state.sopGraph!.getNeighbors(
                outgoingCell,
                {
                  outgoing: true,
                },
              );
              state.sopGraph!.removeNode(outgoingCell.id);
              if (outgoingNeighbors.length > 0) {
                outgoingNeighbors.forEach((outgoingItem) => {
                  addBlockTargetEdge(incomingCell.id, outgoingItem.id);
                  if (outgoingItem.isNode()) {
                    const transVal = getTranslatePosition(
                      incomingCell,
                      outgoingItem,
                    );
                    const successors = state.sopGraph!.getSuccessors(
                      incomingCell,
                      {
                        deep: true,
                      },
                    );
                    successors?.forEach((successorItem) => {
                      successorItem.translate(
                        transVal.translateX,
                        transVal.translateY,
                      );
                    });
                  }
                });
              }
            }
          });
        }
      });
    } else {
      dragNeighbors.outgoingList.forEach((outgoing) => {
        state.sopGraph!.removeNode(outgoing.value);
      });
    }
  }
};
/**
 * 指図SOPフローマスタデータを取得
 */
const getRxSopFlowByNo = async (searchKey: string) => {
  const apiRequestData: SearchSopListByNo = {
    sopFlowNo: searchKey,
  };
  const { responseRef, errorRef } = await useSearchSopListByNo({
    ...commonActionRequestData,
    btnId: state.cmnRequest.btnId,
    ...apiRequestData,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = `${t('SOP.Menu.txtSopTitle')}`;
    messageBoxSingleButtonRef.value.content = `${t('Tt.Chr.txtNoDataSet')}`;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButtonRef');
    return;
  }
  if (responseRef.value) {
    const data = responseRef.value.data.rData.rList;
    state.sopDataRx = {
      sopFlowNo: data.sopFlowNo,
      sopFlowNmJp: data.sopFlowNmJp,
      matNo: data.matNo,
      matNm: data.matNm,
      rxNo: data.rxNo,
      rxNm: data.rxNm,
      prcSeq: data.prcSeq,
      prcNm: data.prcNm,
      sopBatchType: data.sopBatchType,
      helpBinPath: data.helpBinPath,
      forcePrivGrpCd: data.forcePrivGrpCd,
      skipPrivGrpCd: data.skipPrivGrpCd,
      dspSeq: data.dspSeq,
      updDts: data.updDts,
    };
  }
};
/**
 * 工程任意SOPフローマスタデータを取得
 */
const getPrcSopFlowByNo = async (searchKey: string) => {
  const apiRequestData: SearchSopPrcListByNo = {
    sopFlowNo: searchKey,
  };
  const { responseRef, errorRef } = await useSearchSopPrcListByNo({
    ...commonActionRequestData,
    btnId: state.cmnRequest.btnId,
    ...apiRequestData,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = `${t('SOP.Menu.txtSopTitle')}`;
    messageBoxSingleButtonRef.value.content = `${t('Tt.Chr.txtNoDataSet')}`;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButtonRef');
    return;
  }
  if (responseRef.value) {
    const data = responseRef.value.data.rData.rList;
    state.sopDataPrc = {
      sopFlowNo: data.sopFlowNo,
      sopFlowNmJp: data.sopFlowNmJp,
      prcNo: data.prcNo,
      prcNm: data.prcNm,
      stYmd: data.stYmd,
      edYmd: data.edYmd,
      recApprovFlg: data.recApprovFlg,
      helpBinPath: data.helpBinPath,
      forcePrivGrpCd: data.forcePrivGrpCd,
      skipPrivGrpCd: data.skipPrivGrpCd,
      dspSeq: data.dspSeq,
      updDts: data.updDts,
    };
  }
};
/**
 * 秤量前後SOPフローマスタデータを取得
 */
const getWgtSopFlowByNo = async (searchKey: string) => {
  const apiRequestData: SearchSopWgtListByNo = {
    sopFlowNo: searchKey,
  };
  const { responseRef, errorRef } = await useSearchSopWgtListByNo({
    ...commonActionRequestData,
    btnId: state.cmnRequest.btnId,
    ...apiRequestData,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = `${t('SOP.Menu.txtSopTitle')}`;
    messageBoxSingleButtonRef.value.content = `${t('Tt.Chr.txtNoDataSet')}`;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButtonRef');
    return;
  }
  if (responseRef.value) {
    const data = responseRef.value.data.rData.rList;
    state.sopDataWgt = {
      sopFlowNo: data.sopFlowNo,
      sopFlowNmJp: data.sopFlowNmJp,
      wgtRoomNo: data.wgtRoomNo,
      wgtRoomNm: data.wgtRoomNm,
      wgtSopCat: data.wgtSopCat,
      helpBinPath: data.helpBinPath,
      forcePrivGrpCd: data.forcePrivGrpCd,
      skipPrivGrpCd: data.skipPrivGrpCd,
      dspSeq: data.dspSeq,
      updDts: data.updDts,
    };
  }
};
/**
 * グラフデータの登録処理
 * @param {*}  flowList - Flow Chart data
 * @remarks
 * 呼び出し先メソッド
 * SopChartSave
 */
const insertSOPChart = async (data: SopFlowGetDataOption[]) => {
  const apiRequestData: InsertSopChart = {
    sopFlowNo: state.SOPData.sopFlowNo,
    sopNodeList: data,
  };
  const { responseRef, errorRef } = await useInsertSopChart({
    ...commonActionRequestData,
    btnId: 'btnDecision',
    ...apiRequestData,
  });
  if (errorRef.value) {
    messageBoxSopChartSaveRef.value.title = errorRef.value.response.rTitle;
    messageBoxSopChartSaveRef.value.content = errorRef.value.response.rMsg;
    openDialog('sopChartSaveRef');
    messageBoxSopChartSaveRef.value.type = 'error';
    closeLoading();
    return;
  }
  if (responseRef.value?.data.rCode === CONST.API_STATUS_CODE.SUCCESS) {
    messageBoxSopChartSaveRef.value.title = responseRef.value.data.rTitle;
    messageBoxSopChartSaveRef.value.content = responseRef.value.data.rMsg;
    messageBoxSopChartSaveRef.value.type = 'info';
    openDialog('sopChartSaveRef');
    state.sopFlowChartType = 'EDIT';
  }

  if (state.refererScreenId === SCREEN_ID.SOP_RX_LIST) {
    // 処方SOPのマスタデータ取得
    getRxSopFlowByNo(state.SOPData.sopFlowNo);
  } else if (state.refererScreenId === SCREEN_ID.SOP_PRC_LIST) {
    // 工程任意SOPのマスタデータ取得
    getPrcSopFlowByNo(state.SOPData.sopFlowNo);
  } else if (state.refererScreenId === SCREEN_ID.SOP_WGT_LIST) {
    // 秤量前後SOPのマスタデータ取得
    getWgtSopFlowByNo(state.SOPData.sopFlowNo);
  }

  closeLoading();
};
/**
 * 選択範囲の要素チェック
 * @param {*} selectedCells - 選択したノード
 * @remarks
 * 呼び出し先メソッド
 * SopChartSave
 * checkSOPBlockModel
 */
const checkAllSelectedCellData = (selectedCells: Cell[]) => {
  const nodeIDListVal: string[] = [];
  const allNeighborsVal: NeighborsOption[] = [];
  const noNeighborsListVal: string[] = [];
  let templateNodeFlagVal: boolean = false;
  let blockNodeFlagVal: boolean = false;
  let noNeighborsFlagVal: boolean = false;
  let startNodeCountVal: number = 0;
  if (selectedCells && selectedCells.length > 0) {
    // 選択したノードの入力ノードと出力ノードのデータを取得
    selectedCells.forEach((item: Cell) => {
      if (item.isVisible()) {
        if (item.isNode()) {
          const partsCD = item.getProp<string>('sopPartsCD');
          const sameCdFlag: boolean = checkPartNodeCode(
            state.SOPSetData,
            partsCD,
          );
          const controlFlag: boolean = checkControlPartCode(
            state.SOPSetData,
            partsCD,
          );
          if (
            !sameCdFlag &&
            !controlFlag &&
            partsCD === SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_CD
          ) {
            blockNodeFlagVal = true;
          }
          if (partsCD === SOP_PARTS_VARIABLES.PART_SPEC_TEMPLATE_CD) {
            templateNodeFlagVal = true;
          }
          if (partsCD === SOP_PARTS_VARIABLES.PART_SPEC_START_CD) {
            startNodeCountVal += 1;
          }
          nodeIDListVal.push(item.id);
          const itemNeighbors = getCurrentNeighbors(item);
          const incomingData = itemNeighbors.incomingList;
          const outgoingData = itemNeighbors.outgoingList;
          if (incomingData.length === 0 || outgoingData.length === 0) {
            if (
              !(
                partsCD === SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD &&
                item.id.indexOf('-') > 0
              )
            ) {
              if (!notDeleteParts.includes(partsCD)) {
                noNeighborsListVal.push(item.id);
                noNeighborsFlagVal = true;
              }
            }
          }
          allNeighborsVal.push({
            incoming: incomingData,
            outgoing: outgoingData,
            id: item.id,
            sopPartsCD: partsCD,
          });
        }
      }
    });
  }
  return {
    nodeIDList: nodeIDListVal,
    allNeighbors: allNeighborsVal,
    templateNodeFlag: templateNodeFlagVal,
    blockNodeFlag: blockNodeFlagVal,
    noNeighborsFlag: noNeighborsFlagVal,
    noNeighborsList: noNeighborsListVal,
    startNodeCount: startNodeCountVal,
  };
};
/**
 * 選択範囲の要素チェック
 * @param {*} selectedCells - 選択したノード
 * @remarks
 * 呼び出し先メソッド
 * SOPBlockRegistration
 */
const checkSOPBlockModel = (selectedCells: Cell[]) => {
  let neighbors: NeighborsOption[] = [];
  let nodeList: string[] = [];
  const allCell = checkAllSelectedCellData(selectedCells);
  neighbors = allCell.allNeighbors;
  nodeList = allCell.nodeIDList;
  let incomingCntVal: number = 0;
  let outgoingCntVal: number = 0;
  let blockStartIdVal: string = ''; // ブロックの開始パーツID
  let blockEndIdVal: string = ''; // ブロックの終了パーツID
  let incomingValue: string = ''; // ブロックの入力パーツID
  let outgoingValue: string = ''; // ブロックの出力パーツID
  neighbors.forEach((item: NeighborsOption, index) => {
    const { incoming, outgoing } = item;
    const incomings = incoming.filter(
      (incomingItem: SelectOption) => !nodeList.includes(incomingItem.value),
    );
    const outgoings = outgoing.filter(
      (outgoingItem: SelectOption) => !nodeList.includes(outgoingItem.value),
    );
    if (index === 0) {
      blockStartIdVal = item.id;
    }
    // [課題2][No.1] Mod ST
    // outgoingItemはnodeListに存在しない場合、outgoingsにデータがある
    // 一つのノードのみを選択した場合、outgoing.lengthは0である
    // else if (outgoings.length === 0) {
    if (outgoing.length === 0 || outgoings.length > 0) {
      // [課題2][No.1] Mod ED
      blockEndIdVal = item.id;
    }
    if (incomings.length === 1) {
      incomingValue = incomings[0].value;
    }
    if (outgoings.length === 1) {
      outgoingValue = outgoings[0].value;
    }
    incomingCntVal += incomings.length;
    outgoingCntVal += outgoings.length;
  });
  return {
    blockStartId: blockStartIdVal,
    blockEndId: blockEndIdVal,
    incomingId: incomingValue,
    outgoingId: outgoingValue,
    incomingCnt: incomingCntVal,
    outgoingCnt: outgoingCntVal,
    templateNodeFlag: allCell.templateNodeFlag,
    blockNodeFlag: allCell.blockNodeFlag,
    noNeighborsFlag: allCell.noNeighborsFlag,
    startNodeCount: allCell.startNodeCount,
  };
};
/**
 * ノードの再接続
 * @param {*} nodeList - 接続が切れているノードリスト
 * @remarks
 * <<呼び出しメソッド>>
 * nodeDeleteCheck
 */
const nodeReConnect = (nodeList: string[]) => {
  let connectNodeId = '';
  const connectParts: string[] = [
    SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD,
    SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD,
    SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD,
    SOP_PARTS_VARIABLES.PART_SPEC_END_CD,
  ];
  const allNodes = state.sopGraph!.getNodes();
  // 描画位置の昇順にソート
  allNodes.sort((a, b) => {
    if (a.isNode() && b.isNode()) {
      const positionA = a.getProp<PositionOption>('position');
      const positionB = b.getProp<PositionOption>('position');
      return positionA.y - positionB.y;
    }
    return 0;
  });
  // 対象ノードに一番近い＋ボタンを探す
  nodeList.forEach((nodeID) => {
    let isMatch = false;
    let currentPositionY = 0;
    const node = state.sopGraph!.getCellById(nodeID);
    if (!node.isNode()) {
      return;
    }
    allNodes.forEach((item) => {
      const partsCD = item.getProp<string>('sopPartsCD');
      if (connectNodeId !== '') {
        return;
      }
      if (isMatch && connectParts.includes(partsCD)) {
        const y = item.getPosition().y - currentPositionY;
        if (y > 5) {
          connectNodeId = item.id;
          return;
        }
      }
      if (nodeID === item.id) {
        isMatch = true;
        currentPositionY = node.getPosition().y;
      }
    });
    const connectNode = state.sopGraph!.getCellById(connectNodeId);
    const connectPartsCD = connectNode.getProp<string>('sopPartsCD');
    if (connectPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_END_CD) {
      // エッジを接続する
      addBlockTargetEdge(nodeID, connectNodeId);
    } else {
      // エッジを接続する
      addPathTargetEdge(nodeID, connectNodeId);
    }
  });
};
/**
 * ノード削除後 接続が切れていないかチェック
 * @param
 * @remarks
 * <<呼び出しメソッド>>
 * cellRemove
 */
const nodeDeleteCheck = () => {
  const allNodes = state.sopGraph!.getNodes();
  const nodeModel = checkAllSelectedCellData(allNodes);
  if (nodeModel.noNeighborsFlag && nodeModel.noNeighborsList.length > 0) {
    // --- 追加: 接続が切れているパーツ情報をログ出力 ---
    console.log('接続が切れているパーツIDリスト:', nodeModel.noNeighborsList);
    nodeModel.noNeighborsList.forEach((id) => {
      const node = state.sopGraph!.getCellById(id);
      if (node) {
        const partsCD = node.getProp<string>('sopPartsCD');
        const label = node.getAttrByPath<string>('text/text');
        console.log(
          `切断パーツ: ID=${id}, パーツ種別=${partsCD}, ラベル=${label}`,
        );
      }
    });
    // --- ここまで追加 ---
    // 接続が切れている場合、再接続する
    nodeReConnect(nodeModel.noNeighborsList);
  }
};

/* **************************************************************************************************************************************************** */
/* *** メソッド定義                                                                                                                                      */
/* **************************************************************************************************************************************************** */

/**
 * 「テンプレート登録」選択
 */
const SOPBlockRegistration = () => {
  const selCell: Cell[] = state.sopGraph!.getSelectedCells();
  // 選択されたノードを上から順にソートする
  selCell.sort((a, b) => {
    if (a.isNode() && b.isNode()) {
      const positionA = a.getProp<PositionOption>('position');
      const positionB = b.getProp<PositionOption>('position');
      return positionA.y - positionB.y;
    }
    return 0;
  });
  const blockModel = checkSOPBlockModel(selCell);
  // テンプレート登録可能かチェック
  if (selCell.length === 0) {
    messageBoxTemplateButtonRef.value.title = `${t('SOP.Chr.txtSelectError')}`;
    messageBoxTemplateButtonRef.value.content = `${t('SOP.Msg.unSelectNode')}`;
    openDialog('templateButtonRef');
    state.sopGraph!.cleanClipboard();
    state.sopGraph!.cleanSelection();
    state.showBlockAddCard = false;
    return;
  }
  if (blockModel.incomingCnt > 1 || blockModel.outgoingCnt > 1) {
    messageBoxTemplateButtonRef.value.title = `${t('SOP.Chr.txtMissRange')}`;
    messageBoxTemplateButtonRef.value.content = `${t('SOP.Msg.selectErrorRange')}`;
    openDialog('templateButtonRef');
    state.sopGraph!.cleanClipboard();
    state.sopGraph!.cleanSelection();
    state.showBlockAddCard = false;
    return;
  }
  if (blockModel.blockNodeFlag || blockModel.templateNodeFlag) {
    messageBoxTemplateButtonRef.value.title = `${t('SOP.Chr.txtSelectError')}`;
    messageBoxTemplateButtonRef.value.content = `${t('SOP.Msg.selectErorrBlock')}`;
    openDialog('templateButtonRef');
    state.sopGraph!.cleanClipboard();
    state.sopGraph!.cleanSelection();
    state.showBlockAddCard = false;
    return;
  }
  const startNode = state.sopGraph!.getCellById(blockModel.blockStartId);
  const startSameCdFlag = checkPartCode(
    state.SOPSetData.addPartCds,
    startNode.getProp<string>('sopPartsCD'),
  );
  const endNode = state.sopGraph!.getCellById(blockModel.blockEndId);
  // [課題2][No.4] MOD ST
  // テンプレートの最後ノードはブランチの収束ノードである場合、保存対象とする
  const endNodeCD = endNode.getProp<string>('sopPartsCD');
  const endConvergenceFlg =
    endNodeCD === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD;
  const endSameCdFlag = checkPartCode(state.SOPSetData.addPartCds, endNodeCD);
  // [課題2][No.4] MOD ED
  if (
    startNode.getProp<string>('sopPartsCD') ===
    SOP_PARTS_VARIABLES.PART_SPEC_START_CD
  ) {
    messageBoxTemplateButtonRef.value.title = `${t('SOP.Chr.txtSelectError')}`;
    messageBoxTemplateButtonRef.value.content = `${t('SOP.Msg.selectErorrStart')}`;
    openDialog('templateButtonRef');
    state.sopGraph!.cleanClipboard();
    state.sopGraph!.cleanSelection();
    state.showBlockAddCard = false;
    return;
  }
  if (
    endNode.getProp<string>('sopPartsCD') ===
    SOP_PARTS_VARIABLES.PART_SPEC_END_CD
  ) {
    messageBoxTemplateButtonRef.value.title = `${t('SOP.Chr.txtSelectError')}`;
    messageBoxTemplateButtonRef.value.content = `${t('SOP.Msg.selectErorrEnd')}`;
    openDialog('templateButtonRef');
    state.sopGraph!.cleanClipboard();
    state.sopGraph!.cleanSelection();
    state.showBlockAddCard = false;
    return;
  }

  state.showBlockAddCard = false;
  const nodeList: Node[] = [];
  const edgeList: Edge[] = [];
  if (!selCell || selCell.length === 0) {
    state.sopGraph!.cleanClipboard();
    state.sopGraph!.cleanSelection();
    state.showBlockAddCard = false;
  }
  selCell.forEach((item: Cell) => {
    if (item.isVisible()) {
      if (item.isNode()) {
        const portList = item.getPorts();
        portList.forEach((portItem) => {
          if (!portItem.id) return;
          item.portProp(portItem.id, 'attrs/circle/style/visibility', 'hidden');
        });
        // 先頭および末尾の＋ボタンはテンプレート登録しない
        if (item.id === startNode.id && startSameCdFlag) {
          return;
        }
        // [課題2][No.4] MOD ST
        // テンプレートの最後ノードはブランチの収束ノードである場合、保存対象とする
        // if (item.id === endNode.id && endSameCdFlag) {
        if (item.id === endNode.id && endSameCdFlag && !endConvergenceFlg) {
          // [課題2][No.4] MOD ED
          return;
        }
        nodeList.push(item);
      }
      if (item.isEdge()) {
        const itemSource: string = item.getProp('source').cell;
        const itemTarget: string = item.getProp('target').cell;
        // 先頭および末尾の＋ボタンに紐付くエッジはテンプレート登録しない
        if (
          itemSource === blockModel.incomingId ||
          itemTarget === blockModel.outgoingId
        ) {
          return;
        }
        if (itemSource === startNode.id && startSameCdFlag) {
          return;
        }
        // [課題2][No.4] MOD ST
        // テンプレートの最後ノードはブランチの収束ノードである場合、保存対象とする
        // if (itemTarget === endNode.id && endSameCdFlag) {
        if (itemTarget === endNode.id && endSameCdFlag && !endConvergenceFlg) {
          // [課題2][No.4] MOD ED
          return;
        }
        edgeList.push(item);
      }
    }
  });
  const blockDataSet = {
    sopFlowNo: '',
    sopFlowNmJp: '',
    sopCatTxtJp: '',
    untSopCat: '',
    updDts: '',
    flowList: [],
  };
  // 「テンプレート登録」ウィンドウ
  state.chartOptions = {
    cellData: {
      nodes: getNodeCellData(nodeList),
      edges: getEdgesCellData(edgeList),
    },
    blockData: blockDataSet,
    blockNo: '',
  };
  state.sopGraph!.cleanClipboard();
  state.sopGraph!.cleanSelection();
  state.SOPChartType = 'NEW';
  state.SOPAddFlag = true;
};
/**
 *
 * グラフ描画に関するUI定義
 */
const setSopChartMain = () => {
  state.SOPSetData = getSOPSetting(t);
  const newGraph = new Graph({
    container: graphContainer.value!,
    width: graphWidth.value,
    height: graphHeight.value,
    grid: {
      visible: true,
      type: 'mesh',
      size: 8,
    },
    panning: {
      enabled: true,
      eventTypes: ['leftMouseDown', 'rightMouseDown'],
    },
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: 'ctrl',
      minScale: 0.11,
      maxScale: 3,
    },
    highlighting: {
      magnetAdsorbed: {
        name: 'stroke',
        args: {
          attrs: {
            fill: '#5F95FF',
            stroke: '#5F95FF',
          },
        },
      },
    },
    connecting: {
      router: {
        name: 'manhattan',
        args: {
          startDirections: ['bottom'],
          endDirections: ['top'],
        },
      },
      connector: {
        name: 'rounded',
        args: {
          radius: 10,
        },
      },
      anchor: 'center',
      connectionPoint: 'anchor',
      allowBlank: false,
      allowLoop: false,
      snap: false,
      createEdge() {
        return new Shape.Edge({
          attrs: {
            line: {
              stroke: '#A2B1C3',
              strokeWidth: 2,
            },
          },
          zIndex: 0,
        });
      },
    },
    interacting: {
      edgeMovable: false,
      nodeMovable: true,
    },
    translating: {
      restrict: (view) => {
        const translate = state.sopGraph!.translate();
        let xNum = 0 - translate.tx;
        let yNum = 0 - translate.ty;
        let widthVal = graphWidth.value;
        let heightVal = graphHeight.value;
        if (view) {
          const cellView = view.cell;
          if (cellView.isNode()) {
            const selCells = state.sopGraph!.getSelectedCells();
            const isSelected = state.sopGraph!.isSelected(cellView);
            const cellPosition = cellView.getProp<PositionOption>('position');
            let restrictFlag = true;
            if (selCells && selCells.length > 1 && isSelected) {
              restrictFlag = true;
            } else {
              restrictFlag = false;
              const cloneFlag = cellView.getProp<boolean>('cloneFlag');
              if (cloneFlag) {
                restrictFlag = true;
              }
            }
            if (!restrictFlag) {
              xNum = cellPosition.x;
              yNum = cellPosition.y;
              widthVal = 0;
              heightVal = 0;
            }
          }
        }
        return {
          x: xNum,
          y: yNum,
          width: widthVal,
          height: heightVal,
        };
      },
    },
  });
  newGraph.use(
    new Transform({
      resizing: false,
      rotating: false,
    }),
  );
  newGraph.use(
    new Selection({
      rubberband: true,
      rubberNode: true,
      rubberEdge: true,
      // グローバル、全部移動できないになります。
      movable: false,
      showNodeSelectionBox: true,
      modifiers: ['ctrl'],
    }),
  );
  newGraph.use(new Snapline());
  newGraph.use(new Keyboard());
  newGraph.use(new Clipboard());
  newGraph.use(
    new History({
      enabled: true,
      // ignoreChange: true
    }),
  );
  // ノードタイプの作成
  const customList: SopCustomNodeOption[] = state.SOPSetData.customNode;
  customList.forEach((item: SopCustomNodeOption) => {
    const customItem = { ...item.data, ports: { ...state.SOPSetData.ports } };
    Graph.registerNode(item.name, customItem, true);
  });
  // パーツイベント処理
  // パーツリストから、ノードを追加(createPartNode->cloneAddPart)
  // 一つのノードをDragし、⊕以外領域へ移動(node:mousedown->clonePartNode->node:mouseup)
  // 一つのノードをDragし、⊕領域へ移動(node:mousedown->clonePartNode->setTargetBBox->node:mouseup->setDragPartEdges)
  // 複数のノードをDragし、⊕以外領域へ移動(node:mousedown->multipleSelection->selectionMove->node:mouseup)
  // 複数のノードをDragし、⊕領域へ移動(node:mousedown->multipleSelection->selectionMove->setTargetBBox->node:mouseup)
  newGraph.on('node:added', ({ node }) => {
    if (
      state.sopPartAddType === 'templateAdd' ||
      state.sopPartAddType === 'redrawFromNodesMatrixAdd' ||
      state.sopPartAddType === 'addBranchLabelsAndConfluenceAdd'
    ) {
      return false;
    }
    // [課題管理表][No.120] MOD ST
    // ノードの追加により、連結する二つノードの上下関係を判定するために、追加した通常ノードを退避する
    const nodeCD = node.getProp<string>('sopPartsCD');
    const isNormalNode = checkPartCode(state.SOPSetData.partCds, nodeCD);
    const isAddpartNode =
      nodeCD === SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD ||
      nodeCD === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD ||
      nodeCD === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD;
    if (isNormalNode) {
      state.lastNormalNode = node;
      lastNormalPartId.value = node.id;
    } else if (isAddpartNode) {
      lastAddPartId.value = node.id;
    }

    const nodes = state.sopGraph!.getNodes();
    if (state.selectedPartId !== '') {
      const nodeItem = state.sopGraph!.getCellById(state.selectedPartId);
      state.sopGraph!.unselect(nodeItem);
      if (node.isNode()) {
        removeNodeTools(node);
      }
    }
    if (nodes.length > state.maxNodeNum) {
      state.sopGraph!.removeNode(node.id);
      messageBoxSingleButtonRef.value.title = `${t('SOP.Menu.txtSopTitle')}`;
      messageBoxSingleButtonRef.value.content = `${t('SOP.Msg.reachTheMax')}`;
      openDialog('singleButtonRef');
    }
    let checkedAddPartType = '';
    const currentCell = state.sopGraph!.getCellById(state.checkedAddPartId);
    if (currentCell) {
      checkedAddPartType = currentCell.getProp<string>('sopPartsCD');
      if (checkedAddPartType === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD) {
        // [課題管理表][No.188] DEL ST
        // No.188のケースのベースにループではない分岐の最後にもう一つのノードを追加した場合、分岐ノードを削除したら最後のノード一つ分岐しかない場合、親ノードを設定すれば、分岐ノードを削除する時、後ろのノードが消される問題対応
        // 親ノードを設定すれば、分岐ノードを削除した後、ループではない分岐のノードではoutgoingノードを取得できなくなるので、コメントアウト
        // node.setParent(currentCell);
        // [課題管理表][No.188] DEL ED
      } else if (currentCell.hasParent()) {
        // [課題管理表][No.188] DEL ST
        // No.188のケースのベースにループではない分岐の最後にもう一つのノードを追加した場合、分岐ノードを削除したら最後のノード一つ分岐しかない場合、親ノードを設定すれば、分岐ノードを削除する時、後ろのノードが消される問題対応
        // 親ノードを設定すれば、分岐ノードを削除した後、ループではない分岐のノードではoutgoingノードを取得できなくなるので、コメントアウト
        // const nodeParent = currentCell.getParent();
        // node.setParent(nodeParent);
        // [課題管理表][No.188] DEL ED
      }
      state.sopGraph!.unselect(currentCell);
      if (currentCell.isNode()) {
        removeNodeTools(currentCell);
      }
    }
    if (state.sopPartAddType === 'createPartNode') {
      const partsCD = node.getProp<string>('sopPartsCD');
      const sameCdFlag = checkPartCode(state.SOPSetData.partCds, partsCD);
      if (sameCdFlag) {
        // [課題管理表][No.188] MOD ST
        // const createdNode = editPartNode(node);
        editPartNode(node);
        // [課題管理表][No.188] MOD ED
        if (checkedAddPartType === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD) {
          // [課題管理表][No.188] DEL ST
          // No.188のケースのベースにループではない分岐の最後にもう一つのノードを追加した場合、分岐ノードを削除したら最後のノード一つ分岐しかない場合、親ノードを設定すれば、分岐ノードを削除する時、後ろのノードが消される問題対応
          // 親ノードを設定すれば、分岐ノードを削除した後、ループではない分岐のノードではoutgoingノードを取得できなくなるので、コメントアウト
          // createdNode.setParent(currentCell);
          // [課題管理表][No.188] DEL ED
        } else if (currentCell.hasParent()) {
          // [課題管理表][No.188] DEL ST
          // No.188のケースのベースにループではない分岐の最後にもう一つのノードを追加した場合、分岐ノードを削除したら最後のノード一つ分岐しかない場合、親ノードを設定すれば、分岐ノードを削除する時、後ろのノードが消される問題対応
          // 親ノードを設定すれば、分岐ノードを削除した後、ループではない分岐のノードではoutgoingノードを取得できなくなるので、コメントアウト
          // const nodeParent = currentCell.getParent();
          // createdNode.setParent(nodeParent);
          // [課題管理表][No.188] DEL ED
        }
      }
      if (partsCD === SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_CD) {
        // [課題管理表][No.188] MOD ST
        // const createdNode = editSopBlock(node);
        editSopBlock(node);
        // [課題管理表][No.188] MOD ED
        if (checkedAddPartType === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD) {
          // [課題管理表][No.188] DEL ST
          // No.188のケースのベースにループではない分岐の最後にもう一つのノードを追加した場合、分岐ノードを削除したら最後のノード一つ分岐しかない場合、親ノードを設定すれば、分岐ノードを削除する時、後ろのノードが消される問題対応
          // 親ノードを設定すれば、分岐ノードを削除した後、ループではない分岐のノードではoutgoingノードを取得できなくなるので、コメントアウト
          // createdNode.setParent(currentCell);
          // [課題管理表][No.188] DEL ED
        } else if (currentCell.hasParent()) {
          // [課題管理表][No.188] DEL ST
          // No.188のケースのベースにループではない分岐の最後にもう一つのノードを追加した場合、分岐ノードを削除したら最後のノード一つ分岐しかない場合、親ノードを設定すれば、分岐ノードを削除する時、後ろのノードが消される問題対応
          // 親ノードを設定すれば、分岐ノードを削除した後、ループではない分岐のノードではoutgoingノードを取得できなくなるので、コメントアウト
          // const nodeParent = currentCell.getParent();
          // createdNode.setParent(nodeParent);
          // [課題管理表][No.188] DEL ED
        }
      }
      addNodeTools(node);
      const shape = node.getProp<string>('shape');
      if (shape === 'part') {
        editPartNode(node, countUpId.value);
      }
      if (partsCD === SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_CD) {
        editSopBlock(node);
      }
      state.sopPartAddType = 'cloneAddPart';
      // [課題外] MOD ST ノード追加後ノードの表示位置不正対応
      // return setPartPosition(node);
      const retNode = setPartPosition(node);
      state.lastNormalNode = null;
      // [課題外] MOD ED ノード追加後ノードの表示位置不正対応
      // [課題外]ノードの表示重なる問題対策 ADD ST
      // adjustHorizenPosition();
      // [課題外]ノードの表示重なる問題対策 ADD ED

      if (
        lastNormalPartId.value !== undefined &&
        lastAddPartId.value !== undefined
      ) {
        const allNodesBefore = state.sopGraph!.getNodes();
        const mergedMatrix: Matrix = createNodesMatrix(allNodesBefore);
        console.log('パーツ追加 設定変更前:', mergedMatrix);
        // 追加対象の位置を特定
        let checkedY = -1;
        let checkedX = -1;
        for (let y = 0; y < mergedMatrix.length; y++) {
          for (let x = 0; x < mergedMatrix[y].length; x++) {
            if (mergedMatrix[y][x] === state.checkedAddPartId) {
              checkedY = y;
              checkedX = x;
              break;
            }
          }
          if (checkedY !== -1) break;
        }
        const insertY = checkedY + 1;
        const insertX = checkedX;

        // 追加行の他の列に既にデータがある場合は既存行を更新（上書きせず空きセルにセット）
        if (
          mergedMatrix[insertY] &&
          mergedMatrix[insertY].some(
            (val, idx) => idx !== insertX && val !== null,
          )
        ) {
          if (!mergedMatrix[insertY][insertX]) {
            mergedMatrix[insertY][insertX] = lastNormalPartId.value;
          }
        }

        lastNormalPartId.value = '';
        lastAddPartId.value = '';
        console.log('パーツ追加 設定変更後:', mergedMatrix);
      }
      return retNode;
    }
    if (state.sopPartAddType === 'cloneAddPart') {
      const parent = setAddPartIcon(node);
      // [課題外] MOD ST ノード追加後ノードの表示位置不正対応
      const retNode = setPartNodeToFlow(parent);
      state.lastNormalNode = null;
      return retNode;
      // [課題外] MOD ED ノード追加後ノードの表示位置不正対応
    }
    if (state.sopPartAddType === 'clonePartNode') {
      // [課題外] MOD ST ノード追加後ノードの表示位置不正対応
      const retNode = setEdgeForClonePart(node);
      state.lastNormalNode = null;
      return retNode;
      // [課題外] MOD ED ノード追加後ノードの表示位置不正対応
    }
    if (state.sopPartAddType === 'setDragPartEdges') {
      const parent = setAddPartIcon(node);
      setPartNodeToFlow(parent);
      if (state.sopClonePartId !== '') {
        removeDragPart(state.sopClonePartId);
      }
      // [課題外] ADD ST ノード追加後ノードの表示位置不正対応
      state.lastNormalNode = null;
      // [課題外] ADD ED ノード追加後ノードの表示位置不正対応
      return node;
    }
    // [課題外] ADD ST ノード追加後ノードの表示位置不正対応
    state.lastNormalNode = null;
    // [課題外] ADD ED ノード追加後ノードの表示位置不正対応
    return node;
  });
  newGraph.on('node:mousedown', ({ node, e }) => {
    // TODO: nodeMovableは、グラフ初期描画時のみ設定できるパラメータのため、修正方法見直し
    // DEL >>>>>
    // // もし複数パーツを囲んで、移動不可になります。
    // newGraph.options.interacting.nodeMovable =
    //   !state.sopGraph!.getSelectedCells().length > 0;
    // <<<< DEL
    // TODO: 削除
    const コンソール用partsCD = node.getProp<string>('sopPartsCD');
    console.log(`${node.id}\n${コンソール用partsCD}`);

    e.stopPropagation();
    const selCells = state.sopGraph!.getSelectedCells();
    const isSelected = state.sopGraph!.isSelected(node);
    if (selCells && selCells.length > 1 && isSelected) {
      state.sopGraph!.enablePanning();
      state.selectPartMoveFlag = false;
      const moveFlag = checkSelectedCellsMoveFlag();
      if (moveFlag) {
        state.sopGraph!.disablePanning();
        state.selectPartMoveFlag = true;
      }
      state.sopPartAddType = 'multipleSelection';
      if (state.currentPartId !== '') {
        const nodeItem = state.sopGraph!.getCellById(state.currentPartId);
        if (nodeItem.isNode()) {
          const nodePosition = nodeItem.getProp<PositionOption>('position');
          state.currentPosition = nodePosition;
        }
      }
    } else {
      // TODO:フォーカスの切り替えの整理
      if (node.id !== state.checkedAddPartId) {
        const nodeItem = state.sopGraph!.getCellById(state.checkedAddPartId);
        if (nodeItem) {
          if (nodeItem.isNode()) {
            removeNodeTools(nodeItem);
          }
          state.sopGraph!.unselect(nodeItem);
        }
      }
      if (node.id !== state.currentPartId) {
        const nodeItem = state.sopGraph!.getCellById(state.currentPartId);
        if (nodeItem) {
          if (nodeItem.isNode()) {
            removeNodeTools(nodeItem);
          }
          state.sopGraph!.unselect(nodeItem);
        }
      }
      state.currentPartMoveFlag = false;
      const partFlag = checkNeighborPart(node);
      node.prop('cloneFlag', false);
      if (!partFlag) {
        const partsCD = node.getProp<string>('sopPartsCD');
        const sameCdFlag = checkPartCode(state.SOPSetData.partCds, partsCD);
        const controlFlag = checkPartCode(
          state.SOPSetData.controlPartCds,
          partsCD,
        );
        const blockFlag = checkPartCode(state.SOPSetData.blockPartCds, partsCD);
        if (sameCdFlag || controlFlag || blockFlag) {
          return sopPartDragStart(node);
        }
      }
    }
    return node;
  });
  newGraph.on('node:mousedown', ({ node, e }) => {
    e.stopPropagation();
    const parentFlag = node.hasParent();
    if (parentFlag) {
      state.sopGraph!.unselect(node);
    } else {
      state.localeCheckId = node.id;
      state.localeCheckType = 'node';
      const partsCD = node.getProp<string>('sopPartsCD');
      const sameCdFlag = checkPartNodeCode(state.SOPSetData, partsCD);
      const controlFlag = checkControlPartCode(state.SOPSetData, partsCD);
      if (sameCdFlag) {
        currentNodeSetting(node.id);
      } else if (controlFlag) {
        currentNodeSetting(node.id);
      } else if (partsCD === SOP_PARTS_VARIABLES.PART_SPEC_TEMPLATE_CD) {
        state.SOPBlockType = 'T';
        state.SOPblockNo = '';
        state.SOPSelectFlag = true;
      } else if (partsCD === SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_CD) {
        state.SOPBlockType = 'B';
        state.SOPSelectFlag = true;
        state.SOPblockNo = partsCD;
      }
    }
  });
  newGraph.on('node:mousemove', ({ e }) => {
    e.stopPropagation();
    state.currentPartMoveFlag = true;
    if (
      state.selectPartMoveFlag &&
      state.sopPartAddType === 'multipleSelection'
    ) {
      state.sopPartAddType = 'selectionMove';
      setSelectionPart();
    }
    if (
      state.sopPartAddType === 'selectionMove' ||
      state.sopPartAddType === 'setTargetBBox' ||
      state.sopPartAddType === 'createPartNode' ||
      state.sopPartAddType === 'clonePartNode'
    ) {
      getRootPartPositionChange(state.currentPartId);
    }
  });
  newGraph.on('node:mouseup', ({ node, e }) => {
    e.stopPropagation();
    const selCells = state.sopGraph!.getSelectedCells();
    const isSelected = state.sopGraph!.isSelected(node);
    if (selCells && selCells.length > 1 && isSelected) {
      if (state.sopPartAddType === 'setTargetBBox') {
        setSelectionDragPart();
      }
      state.sopGraph!.enablePanning();
    } else {
      const partFlag = checkNeighborPart(node);
      if (partFlag) {
        if (state.currentPartMoveFlag) {
          messageBoxSingleButtonRef.value.title = `${t('SOP.Menu.txtSopTitle')}`;
          messageBoxSingleButtonRef.value.content = `${t('SOP.Msg.cannotBeDragged')}`;
          openDialog('singleButtonRef');
        } else {
          state.selectedPartId = node.id;
          currentNodeSetting(node.id);
        }
      } else {
        const partsCD = node.getProp<string>('sopPartsCD');
        node.prop('cloneFlag', false);
        const sameCdFlag = checkPartCode(state.SOPSetData.partCds, partsCD);
        const controlFlag = checkPartCode(
          state.SOPSetData.controlPartCds,
          partsCD,
        );
        const blockFlag = checkPartCode(state.SOPSetData.blockPartCds, partsCD);
        if (sameCdFlag || controlFlag || blockFlag) {
          state.isSopInfoSettingVisible = false;
          if (state.checkedAddPartId === '') {
            const clonePart = state.sopGraph!.getCellById(state.sopClonePartId);
            if (clonePart !== null && clonePart.isNode()) {
              const position = clonePart.getProp<PositionOption>('position');
              node.setProp('position', position);
              sopEdgeEdit(clonePart, node.id);
              state.sopGraph!.removeNode(clonePart.id);
              currentNodeSetting(node.id);
              state.selectedPartId = node.id;
              return node;
            }
          } else {
            state.sopPartAddType = 'setDragPartEdges';
            const currentPart = state.sopGraph!.getCellById(
              state.currentPartId,
            );
            if (currentPart !== null && currentPart.isNode()) {
              return setPartPosition(currentPart);
            }
          }
        }
      }
    }
    return node;
  });
  newGraph.on(
    'node:blockDetailCollapse',
    (res: { e: { stopPropagation: () => void }; node: Node }) => {
      res.e.stopPropagation();
      getBlockDetail(res.node);
      return false;
    },
  );
  newGraph.on(
    'node:sopPartAdd',
    (res: { e: { stopPropagation: () => void }; node: Node }) => {
      res.e.stopPropagation();
      state.checkedAddPartId = res.node.id;
      state.sopPartListShowType = true;
      state.isSopInfoSettingVisible = false;
      state.isSopInfoSettingDialogVisible = false;
      addNodeTools(res.node);
      state.sopGraph!.select(res.node);
      if (state.checkedAddPartId !== '') {
        const nodeItem = state.sopGraph!.getCellById(state.checkedAddPartId);
        if (nodeItem) {
          if (state.checkedAddPartId !== res.node.id) {
            state.sopGraph!.unselect(nodeItem);
            if (nodeItem.isNode()) {
              removeNodeTools(nodeItem);
            }
          }
        }
      }
      state.checkedAddPartId = res.node.id;
      if (state.selectedPartId !== '') {
        const nodeItem = state.sopGraph!.getCellById(state.selectedPartId);
        if (nodeItem) {
          state.sopGraph!.unselect(nodeItem);
          if (nodeItem.isNode()) {
            removeNodeTools(nodeItem);
          }
        }
      }
      if (state.sopGraph!.getOutgoingEdges(res.node.id)?.length === undefined) {
        state.blnLastcheckedAddPart = true;
        state.LastcheckedAddPartSuccessors = [];
      } else {
        state.blnLastcheckedAddPart = false;
        state.LastcheckedAddPartSuccessors = state.sopGraph!.getSuccessors(
          res.node,
          {
            deep: true,
          },
        );
      }
    },
  );
  state.sopGraph = newGraph;
};
// [課題392] ADD ST
/**
 * ブロック中の最後のノードを探す
 * @param array
 * @param sopNodeNo
 * 呼び出しメソッド flowListUpdate
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const findBlockLastNode = (array: any[], sopNodeNo: any) => {
  const reversedArr = array.slice().reverse();
  return reversedArr.find((item) => item.parentSopNodeNo === sopNodeNo);
};
// /**
//  * 該当分岐ノードの後続ノードを探す
//  * @param branchSuccessors
//  * @param coverId
//  * 呼び出しメソッド moveBranchShrink
//  */
// const correctCurrentBranchSuccessors = (
//   branchSuccessors: Cell[],
//   coverId: string,
// ) => {
//   const successorsCoverIndex = branchSuccessors.findIndex(
//     (branchSuccessorsItem) => branchSuccessorsItem.id === coverId,
//   );
//   const successorsLoopIndex = branchSuccessors.findIndex(
//     (_branchItemLoopItem, index) => {
//       if (index + 1 < branchSuccessors.length) {
//         return (
//           branchSuccessors[index + 1].getProp('position').y <
//           branchSuccessors[index].getProp('position').y
//         );
//       }
//       return false;
//     },
//   );
//   if (successorsCoverIndex < successorsLoopIndex) {
//     branchSuccessors.splice(successorsCoverIndex);
//   } else {
//     branchSuccessors.splice(successorsLoopIndex + 1);
//   }
//   return branchSuccessors;
// };
// [課題392] ADD ED
/**
 * DBからグラフデータを取得して描画
 */
const getSOPFlowData = async () => {
  state.sopGraph!.batchUpdate(() => {
    const { sopPartConst } = state.SOPSetData;
    const positionX = graphWidth.value / 2 - sopPartConst!.startPartWidth / 2;
    let positionY = 10;
    const sopCommonSetting = state.SOPSetData.commonSetting;
    const sopIndividualPara = state.SOPSetData.individualPara;
    const controls: SopControlPartOption[] = state.SOPSetData.startParts;
    const nodeParts = controls.map((item) => {
      const rectItem = { ...item };
      const sopPartsCD = rectItem.sopPartsCD as keyof typeof sopIndividualPara;
      const individualPara = getItemValueByKey(sopIndividualPara, sopPartsCD);
      rectItem.zIndex = 4;
      let rectX = positionX;
      if (rectItem.width) {
        rectX = positionX - (rectItem.width - sopPartConst!.startPartWidth) / 2;
      }
      rectItem.x = rectX;
      rectItem.y = positionY;
      const sameCdFlag = checkPartCode(
        state.SOPSetData.addPartCds,
        rectItem.sopPartsCD,
      );
      if (rectItem.height) {
        positionY = positionY + rectItem.height + sopPartConst!.marginTop;
        if (sameCdFlag) {
          positionY += 5;
        }
      }
      rectItem.cloneFlag = false;
      rectItem.commonSetting = sopCommonSetting;
      rectItem.individualPara = individualPara;
      if (sameCdFlag) {
        return { ...rectItem, ports: { ...state.SOPSetData.addPartPorts } };
      }
      return rectItem;
    });
    // 「sop_node_id」を表示IDに置き換える
    nodeParts[0].id = setNodeId();
    const startNode = state.sopGraph!.addNode(nodeParts[0]);
    const addPartNode = state.sopGraph!.addNode(nodeParts[1]);
    nodeParts[2].id = setNodeId();
    const endNode = state.sopGraph!.addNode(nodeParts[2]);
    const addIconPart = setAddPartIcon(addPartNode);
    addPathTargetEdge(startNode.id, addIconPart.id);
    addPathTargetEdge(addIconPart.id, endNode.id);
  });
  // DB取得処理
  const apiRequestData: GetSopFlowData = { sopFlowNo: state.SOPData.sopFlowNo };
  const { responseRef, errorRef } = await useGetSopFlowData({
    ...commonActionRequestData,
    btnId: state.cmnRequest.btnId,
    ...apiRequestData,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButtonRef');
    return;
  }
  if (responseRef.value) {
    const allReturnedNodesList = responseRef.value.data.rData.rList;
    state.flowList = responseRef.value.data.rData.rList;
    // ブロックのノードをフローに接続します。
    let flowListIncludeBlockNodes = responseRef.value.data.rData.rList;
    if (state.flowList.length > 0) {
      state.sopFlowChartType = 'EDIT';
      state.localeCheckId = '';
      state.localeCheckType = '';
      state.isSopInfoSettingVisible = false;
      state.isSopInfoSettingDialogVisible = false;
      let blockLastNode;
      state.flowList = flowListUpdate(
        JSON.parse(JSON.stringify(state.flowList)),
      );
      flowListIncludeBlockNodes = state.flowList;
      state.flowList.forEach((flowItem) => {
        blockLastNode = {};
        if (flowItem.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_CD) {
          blockLastNode = findBlockLastNode(
            JSON.parse(JSON.stringify(state.flowList)),
            flowItem.sopNodeNo,
          );
          // eslint-disable-next-line no-param-reassign
          flowItem.sopJoin.nextNodeNo = blockLastNode.sopJoin.nextNodeNo;
        }
      });
      state.flowList = state.flowList.filter(
        (item) =>
          item.blkFlowNo === '_' ||
          item.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_CD,
      );
      state.blockExpandList = flowListIncludeBlockNodes.filter(
        (item) =>
          item.blkFlowNo !== '_' &&
          item.sopPartsCd !== SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_CD,
      );
      const maxBlkSopSeqNo = state.blockExpandList.reduce(
        (previousValue, currentValue) => {
          const { blkFlowNo, blkSopSeqNo } = currentValue;
          if (
            // @ts-expect-error keyを利用して、valueを取得します
            !previousValue[blkFlowNo]
          ) {
            // @ts-expect-error keyを利用して、valueを取得します
            // eslint-disable-next-line no-param-reassign
            previousValue[blkFlowNo] = blkSopSeqNo;
          } else if (
            // @ts-expect-error keyを利用して、valueを取得します
            blkSopSeqNo > previousValue[blkFlowNo]
          ) {
            // @ts-expect-error keyを利用して、valueを取得します
            // eslint-disable-next-line no-param-reassign
            previousValue[blkFlowNo] = blkSopSeqNo;
          }
          return previousValue;
        },
        {},
      );

      state.blockExpandList = state.blockExpandList.filter(
        (item) =>
          // @ts-expect-error keyを利用して、valueを取得します
          item.blkSopSeqNo === maxBlkSopSeqNo[item.blkFlowNo],
      );
      state.childNodeList = state.flowList.filter(
        (item) =>
          item.childNodeFlg === '1' &&
          item.sopPartsCd === SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD,
      );
      state.flowList = state.flowList.filter(
        (item) =>
          !(
            item.childNodeFlg === '1' &&
            item.sopPartsCd === SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD
          ),
      );
      // LOOK DB取得して描画
      setSOPFlowChart(state.flowList);
      state
        .sopGraph!.getNodes()
        .filter(
          (nodeItem) =>
            nodeItem.getProp<string>('sopPartsCD') !== 'controlPartEnd',
        )
        .forEach((item) => {
          const node = allReturnedNodesList.find(
            (val) => val.sopNodeNo === item.id,
          );
          if (node) {
            // @ts-expect-error 最新のobjectなので、いくつかパラメータがないですけど。
            const branchOption = getBranchOptions(node, true);
            // --- ここから追加: sopJoin.nextNodeNoをbranchOption.valueにセット ---
            const outgoingBranch = state.sopGraph!.getNeighbors(item, {
              outgoing: true,
            });
            if (outgoingBranch.length > 1) {
              outgoingBranch.forEach((branchNode, branchIdx) => {
                // 1. outgoingBranchと一致するflowListを探す
                const matchedFlow = state.flowList.find(
                  (flowItem) => flowItem.sopNodeNo === branchNode.id,
                );
                if (
                  matchedFlow &&
                  matchedFlow.sopJoin &&
                  matchedFlow.sopJoin.nextNodeNo
                ) {
                  // 2. 一致したoutGoingBranchのsopJoin.nextNodeNoを取得する
                  const { nextNodeNo } = matchedFlow.sopJoin;
                  let nextNodeNoObj: Record<string, string> = {};
                  try {
                    nextNodeNoObj = JSON.parse(nextNodeNo);
                  } catch {
                    nextNodeNoObj = {};
                  }
                  // 3. branchOptionのvalueを更新
                  const nextNodeValues = Object.values(nextNodeNoObj);
                  if (branchOption[branchIdx]) {
                    branchOption[branchIdx].value = nextNodeValues[0] || '';
                  }
                  // 必要に応じてノードにも反映
                  branchNode.prop('branchOption', branchOption[branchIdx]);
                }
              });
            }
            // // --- ここまで追加 ---
            const outgoing = state.sopGraph!.getNeighbors(item, {
              outgoing: true,
            });
            if (outgoing.length !== 0) {
              // outgoingノードの位置によって、outgoingノードを並べる
              outgoing.sort((a, b) => {
                const positionA = a.getProp<PositionOption>('position').x;
                const positionB = b.getProp<PositionOption>('position').x;
                return positionA - positionB;
              });
              outgoing.forEach((outItem, index) => {
                // ブランチの場合、ブランチラベルを追加します。
                if (branchOption.length > 1) {
                  const branch = t('SOP.Menu.txtBranch');
                  const partsCD = item.getProp<string>('sopPartsCD');
                  const sameCdFlag = checkPartCode(
                    state.SOPSetData.partCds,
                    partsCD,
                  );
                  if (
                    sameCdFlag &&
                    partsCD === SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD
                  ) {
                    const menuButtonName = getMenuButtonName(item, index + 1);
                    outItem.setAttrs({
                      text: {
                        text:
                          menuButtonName !== ''
                            ? menuButtonName
                            : `${branch}${index + 1}:${branchOption.length !== 0 ? branchOption[index].label : ''}`,
                      },
                    });
                  } else {
                    outItem.setAttrs({
                      text: {
                        text: `${branch}${index + 1}:${branchOption.length !== 0 ? branchOption[index].label : ''}`,
                      },
                    });
                  }
                }
                outItem.prop('branchOption', branchOption[index]);
              });
            }
          }
        });
      // [課題419] ADD ED
      state
        .sopGraph!.getNodes()
        .filter(
          (nodeItem) =>
            nodeItem.getProp<string>('sopPartsCD') ===
              SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD ||
            nodeItem.getProp<string>('sopPartsCD') ===
              SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD,
        )
        .forEach((branchItem) => {
          shrinkOrExpandBranch(branchItem);
        });
      coverAddVertices();
    }
  } else {
    messageBoxSingleButtonRef.value.title = `${t('SOP.Menu.txtSopTitle')}`;
    messageBoxSingleButtonRef.value.content = `${t('Tt.Chr.txtNoDataSet')}`;
    openDialog('singleButtonRef');
  }
  closeLoading();
};
/**
 * グラフ描画サイズの調整
 */
const setLocaleGraphSize = () => {
  if (state.sopGraph! !== null) {
    state.sopGraph!.resize(graphWidth.value, graphHeight.value);
  }
};
/**
 * コンボボックスデータ取得
 */
const getComboBoxData = async () => {
  const comboBoxResData = await useGetComboBoxDataStandard({
    ...commonActionRequestData,
    btnId: state.cmnRequest.btnId,
    condList: [
      {
        cmbId: 'privGroup',
        condKey: 'm_mp_cd_priv_grp_cd',
        where: { cd_id: 'PRIV_GRP_CD' },
      },
      {
        cmbId: 'sopBatchType',
        condKey: 'm_mp_cd',
        where: { cd_id: 'SOP_BATCH_TYPE' },
      },
    ],
  });
  if (comboBoxResData && comboBoxResData.rData.rList.length > 0) {
    // --[権限グループ]
    const privGroupList = comboBoxResData.rData.rList.filter(
      (item: ComboBoxDataOptionData) => item.condKey === 'm_mp_cd_priv_grp_cd',
    );
    comboBoxResData.rData.rList =
      comboBoxResData.rData.rList.concat(recApprovFlgData);
    privGroupList.forEach((option) => {
      state.SOPPartSetData.privGroupList.push(option);
    });
    // 「SOP設定(指図SOP)」ダイアログのコンボボックス設定
    setCustomFormComboBoxOptionList(
      sopSettingDialogFormRef.value.formItems,
      comboBoxResData.rData.rList,
    );
    // 「SOP設定(工程任意SOP)」ダイアログのコンボボックス設定
    setCustomFormComboBoxOptionList(
      sopPrcSettingDialogFormRef.value.formItems,
      comboBoxResData.rData.rList,
    );
    // 「SOP設定(秤量前後SOP)」ダイアログのコンボボックス設定
    setCustomFormComboBoxOptionList(
      sopWgtSettingDialogFormRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }
};
onMounted(async () => {
  if (window.history.state.param === undefined) {
    messageBoxSingleButtonRef.value.title = `${t('SOP.Menu.txtSopTitle')}`;
    messageBoxSingleButtonRef.value.content = `${t('Tt.Chr.txtNoDataSet')}`;
    openDialog('singleButtonRef');
    return;
  }
  state.sopFlowChartType = window.history.state.param.editMode;
  state.refererScreenId = window.history.state.param.refererScreenId;
  state.SOPData.sopFlowNo = window.history.state.param.sopFlowNo;
  state.cmnRequest = window.history.state.param.commonRequest;
  state.imageMap = getImageMap(state.images);
  state.sopFlowType = state.SOPData.sopFlowNo.charAt(0);

  nextTick(async () => {
    state.screenHeight = document.body.clientHeight;
    state.screenWidth = document.body.clientWidth;
    setSopChartMain();
    showLoading();
    try {
      const apiCalls = [getSOPFlowData(), getComboBoxData()];
      if (state.refererScreenId === SCREEN_ID.SOP_RX_LIST) {
        // 処方SOPのマスタデータ取得
        apiCalls.push(getRxSopFlowByNo(state.SOPData.sopFlowNo));
        state.isRxSop = true;
      } else if (state.refererScreenId === SCREEN_ID.SOP_PRC_LIST) {
        // 工程任意SOPのマスタデータ取得
        apiCalls.push(getPrcSopFlowByNo(state.SOPData.sopFlowNo));
        state.isRxSop = false;
      } else if (state.refererScreenId === SCREEN_ID.SOP_WGT_LIST) {
        // 秤量前後SOPのマスタデータ取得
        apiCalls.push(getWgtSopFlowByNo(state.SOPData.sopFlowNo));
        state.isRxSop = false;
      }
      await Promise.all(apiCalls);
      closeLoading();
    } catch (error) {
      console.error('Error during API calls:', error);
    }
  });
  window.onresize = () => {
    state.screenHeight = document.body.clientHeight;
    state.screenWidth = document.body.clientWidth;
  };
});
/**
 * screen Width変更の場合は実行
 */

watch(
  () => state.screenWidth,
  (newValue) => {
    if (newValue) {
      setLocaleGraphSize();
    }
  },
  { deep: true },
);
/**
 * screen Height変更の場合は実行
 */
watch(
  () => state.screenHeight,
  (newValue) => {
    if (newValue) {
      setLocaleGraphSize();
    }
  },
  { deep: true },
);

const setErrorNodeColor = (nodes: Node[]) => {
  nodes.forEach((node) => {
    node.setAttrs({
      body: {
        stroke: '#a8b0c2',
        fill: '#f79494',
      },
    });
  });
};
const resetNodeColor = (node: SopPartDataSource) => {
  const itemNode = state.sopGraph!.getCellById(node.id);
  if (itemNode) {
    itemNode.setAttrs({
      body: {
        stroke: '#a8b0c2',
        fill: '#f0ffff',
      },
    });
  }
};
// 「課題345」ADD ST NODEの連結の設計をリファクタリング
/**
 * outgoingを置き換える
 * @param cell
 */
const getOutgoing = (cell: Cell) => {
  const outgoing = state.sopGraph!.getNeighbors(cell, { outgoing: true });
  const incoming = state.sopGraph!.getNeighbors(cell, { incoming: true });
  const addpartArr = [
    SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD,
    SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD,
  ];
  const filterPart = [
    SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD,
    SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD,
    SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD,
    'controlPartStart',
    'controlPartEnd',
  ];
  const sopPartsCds = cell.getProp<string>('sopPartsCD');
  let outgoingId: string = '';
  let sopPartsCd;
  if (outgoing.length === 1) {
    sopPartsCd = outgoing[0].getProp<string>('sopPartsCD');
    if (
      addpartArr.includes(sopPartsCd) &&
      incoming[0].getProp<string>('sopPartsCD') !==
        SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD
    ) {
      getOutgoing(outgoing[0]);
    } else if (sopPartsCds === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD) {
      outgoingId = cell.id;
    } else if (
      !filterPart.includes(sopPartsCds) &&
      incoming[0].getProp<string>('sopPartsCD') ===
        SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD
    ) {
      outgoingId = cell.id;
    } else {
      outgoingId = outgoing[0].id;
    }
  } else {
    outgoingId = cell.id;
  }
  return outgoingId;
};
/**
 * [SOP設定] ボタンクリックイベント
 */
const openDialogSopSetting = () => {
  if (state.refererScreenId === SCREEN_ID.SOP_RX_LIST) {
    // 指図SOPの設定ダイアログ表示
    // --初期値をセット
    sopSettingDialogFormRef.value.formItems.sopFlowNo.formModelValue =
      state.sopDataRx.sopFlowNo;
    sopSettingDialogFormRef.value.formItems.sopFlowNmJp.formModelValue =
      state.sopDataRx.sopFlowNmJp;
    sopSettingDialogFormRef.value.formItems.matNm.formModelValue =
      state.sopDataRx.matNm;
    sopSettingDialogFormRef.value.formItems.rxNm.formModelValue =
      state.sopDataRx.rxNm;
    sopSettingDialogFormRef.value.formItems.sopBatchType.formModelValue =
      state.sopDataRx.sopBatchType;
    sopSettingDialogFormRef.value.formItems.forcePrivGrpCd.formModelValue =
      state.sopDataRx.forcePrivGrpCd;
    sopSettingDialogFormRef.value.formItems.skipPrivGrpCd.formModelValue =
      state.sopDataRx.skipPrivGrpCd;
    sopSettingDialogFormRef.value.formItems.helpBinPath.formModelValue =
      state.sopDataRx.helpBinPath;
    sopSettingDialogFormRef.value.formItems.dspSeq.formModelValue =
      state.sopDataRx.dspSeq != null ? state.sopDataRx.dspSeq.toString() : '';
    openDialog('sopRxSettingDialogVisible');
  } else if (state.refererScreenId === SCREEN_ID.SOP_PRC_LIST) {
    // 工程任意SOPの設定ダイアログ表示
    // --初期値をセット
    sopPrcSettingDialogFormRef.value.formItems.PrcNm.formModelValue =
      state.sopDataPrc.prcNm;
    sopPrcSettingDialogFormRef.value.formItems.sopFlowNo.formModelValue =
      state.sopDataPrc.sopFlowNo;
    sopPrcSettingDialogFormRef.value.formItems.sopFlowNmJp.formModelValue =
      state.sopDataPrc.sopFlowNmJp;
    sopPrcSettingDialogFormRef.value.formItems.stYmd.formModelValue =
      state.sopDataPrc.stYmd;
    sopPrcSettingDialogFormRef.value.formItems.edYmd.formModelValue =
      state.sopDataPrc.edYmd;
    sopPrcSettingDialogFormRef.value.formItems.recApprovFlg.formModelValue =
      state.sopDataPrc.recApprovFlg;
    sopPrcSettingDialogFormRef.value.formItems.forcePrivGrpCd.formModelValue =
      state.sopDataPrc.forcePrivGrpCd;
    sopPrcSettingDialogFormRef.value.formItems.skipPrivGrpCd.formModelValue =
      state.sopDataPrc.skipPrivGrpCd;
    sopPrcSettingDialogFormRef.value.formItems.helpBinPath.formModelValue =
      state.sopDataPrc.helpBinPath;
    sopPrcSettingDialogFormRef.value.formItems.dspSeq.formModelValue =
      state.sopDataPrc.dspSeq != null ? state.sopDataPrc.dspSeq.toString() : '';
    openDialog('sopPrcSettingDialogVisible');
  } else if (state.refererScreenId === SCREEN_ID.SOP_WGT_LIST) {
    // 秤量前後SOPの設定ダイアログ表示
    // --初期値をセット
    sopWgtSettingDialogFormRef.value.formItems.wgtRoomNo.formModelValue =
      state.sopDataWgt.wgtRoomNo;
    sopWgtSettingDialogFormRef.value.formItems.wgtRoomNm.formModelValue =
      state.sopDataWgt.wgtRoomNm;
    sopWgtSettingDialogFormRef.value.formItems.wgtSopCat.formModelValue =
      state.sopDataWgt.wgtSopCat;
    sopWgtSettingDialogFormRef.value.formItems.sopFlowNo.formModelValue =
      state.sopDataWgt.sopFlowNo;
    sopWgtSettingDialogFormRef.value.formItems.sopFlowNmJp.formModelValue =
      state.sopDataWgt.sopFlowNmJp;
    sopWgtSettingDialogFormRef.value.formItems.forcePrivGrpCd.formModelValue =
      state.sopDataWgt.forcePrivGrpCd;
    sopWgtSettingDialogFormRef.value.formItems.skipPrivGrpCd.formModelValue =
      state.sopDataWgt.skipPrivGrpCd;
    sopWgtSettingDialogFormRef.value.formItems.helpBinPath.formModelValue =
      state.sopDataWgt.helpBinPath;
    sopWgtSettingDialogFormRef.value.formItems.dspSeq.formModelValue =
      state.sopDataWgt.dspSeq != null ? state.sopDataWgt.dspSeq.toString() : '';
    openDialog('sopWgtSettingDialogVisible');
  }
};
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const validateSopSettings = (settingDialogRef: {
  value: { checkValidate: () => boolean };
}) => {
  if (!settingDialogRef.value.checkValidate()) {
    openDialogSopSetting();
    messageBoxSopChartSaveRef.value.title = `${t('SOP.Chr.txtSOPSettingNoValueError')}`;
    messageBoxSopChartSaveRef.value.content = `${t('SOP.Msg.noSopSettingValue')}`;
    messageBoxSopChartSaveRef.value.type = 'error';
    openDialog('sopChartSaveRef');
    return false;
  }
  return true;
};
/**
 * 重複のデータをフィルタリングする
 * @param nodeList
 * 呼び出しメソッド SopChartSave
 */
const filterDuplicates = (nodeList: string[]) => {
  const countMap: { [key: string]: number } = {};
  const result: string[] = [];
  for (let i = 0; i < nodeList.length; i++) {
    const item = nodeList[i];
    if (countMap[item]) {
      countMap[item]++;
    } else {
      countMap[item] = 1;
    }
  }
  Object.keys(countMap).forEach((item) => {
    if (countMap[item] > 1) {
      result.push(item);
    }
  });
  return result;
};
// 「課題345」ADD ED
/**
 * グラフデータ登録処理
 */
const SopChartSave = (isReset: boolean = false) => {
  const nodeNmList: string[] = [];
  const nodeList: Node[] = state.sopGraph!.getNodes();
  nodeList.forEach((item: Node) => {
    const commonSetting = item.getProp('commonSetting');
    const nodeTitle = commonSetting.sopNodeNmJp;
    if (nodeTitle !== '' && nodeTitle) {
      nodeNmList.push(nodeTitle);
    }
  });
  const duplicateNodeNames = filterDuplicates(nodeNmList);
  const duplicateNodes = nodeList.filter((item) =>
    duplicateNodeNames.includes(item.getProp('commonSetting').sopNodeNmJp),
  );
  if (new Set(nodeNmList).size < nodeNmList.length) {
    messageBoxSingleButtonRef.value.title = `${t('SOP.Menu.txtSopTitle')}`;
    messageBoxSingleButtonRef.value.content = `${t('SOP.Msg.SOPNodeNameUniqueWhenSave')}`;
    setErrorNodeColor(duplicateNodes);
    openDialog('singleButtonRef');
    return false;
  }
  if (!isReset) {
    const screenSettingsMap = {
      [SCREEN_ID.SOP_PRC_LIST]: SopPrcSettingDialogRef,
      [SCREEN_ID.SOP_RX_LIST]: SopRxSettingDialogRef,
      [SCREEN_ID.SOP_WGT_LIST]: SopWgtSettingDialogRef,
    };
    // @ts-expect-error keyを利用して、valueを取得します
    const settingDialogRef = screenSettingsMap[state.refererScreenId];
    if (settingDialogRef && !validateSopSettings(settingDialogRef)) {
      return false;
    }
  }
  const allNodes = state.sopGraph!.getNodes();
  // 登録事前チェック
  const nodeModel = checkAllSelectedCellData(allNodes);
  if (!isReset) {
    if (nodeModel.noNeighborsFlag) {
      messageBoxSopChartSaveRef.value.title = `${t('SOP.Chr.txtConnectedError')}`;
      messageBoxSopChartSaveRef.value.content = `${t('SOP.Msg.unConnected')}`;
      messageBoxSopChartSaveRef.value.type = 'error';
      openDialog('sopChartSaveRef');
      return false;
    }
    const settingCheck = allNodes.filter((node) => {
      const sopPartsCD = node.getProp<string>('sopPartsCD');
      const sameCdFlag = checkPartCode(state.SOPSetData.partCds, sopPartsCD);
      if (!sameCdFlag) {
        return false;
      }
      return !node.getProp('settingConfirmFlg');
    });
    if (settingCheck.length > 0) {
      messageBoxSopChartSaveRef.value.title = `${t('SOP.Chr.txtSettingStatusError')}`;
      messageBoxSopChartSaveRef.value.content = `${t('SOP.Msg.settingError')}`;
      messageBoxSopChartSaveRef.value.type = 'error';
      openDialog('sopChartSaveRef');
      setErrorNodeColor(settingCheck);
      return false;
    }
  }
  // 登録データ準備
  showLoading();
  // 2次元配列取得
  const allNodesBefore = state.sopGraph!.getNodes();
  const allNodesMatrixBefore = createNodesMatrix(allNodesBefore);
  const allNeighborsVal = nodeModel.allNeighbors;
  const flowList: SopFlowGetDataOption[] = allNeighborsVal.map((item) => {
    const itemNode = state.sopGraph!.getCellById(item.id);
    const cellPosition = itemNode.getProp<PositionOption>('position');
    const cellSopPartsCD = itemNode.getProp<string>('sopPartsCD');
    const cellIndividualPara = itemNode.getProp('individualPara');
    const cellCommonSetting = itemNode.getProp('commonSetting');
    const cellSopCondition = itemNode.getProp('sopCondition');
    const cellInstUnitTxt = itemNode.getProp('instUnitTxt');
    const cellhelpSetting = {
      helpFileType: cellCommonSetting.helpFileType,
      helpBinPath1: cellCommonSetting.helpBinPath1,
      helpBinPath2: cellCommonSetting.helpBinPath2,
      helpBinPath3: cellCommonSetting.helpBinPath3,
      helpBinPath4: cellCommonSetting.helpBinPath4,
      helpBinPath5: cellCommonSetting.helpBinPath5,
      helpTxt1: cellCommonSetting.helpTxt1,
      helpTxt2: cellCommonSetting.helpTxt2,
      helpTxt3: cellCommonSetting.helpTxt3,
    };
    const cellConditionPara = itemNode.getProp('conditionProps');
    let cellUpperLowerSetting;
    if (cellSopPartsCD === SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD) {
      const prevFormatData = itemNode.getProp('upperLowerSetting');
      const valLlmt = convertToSeconds(prevFormatData.thValLlmt);
      const valUlmt = convertToSeconds(prevFormatData.thValUlmt);
      // sopタイマーのthRangeTypeは上下限チェックありの場合のみ、2を設定
      cellUpperLowerSetting = {
        thJudgeFlg: prevFormatData.thJudgeFlg,
        thRangeType: prevFormatData.thJudgeFlg === '1' ? '2' : '',
        thValType: prevFormatData.thValType,
        thValLlmt: valLlmt,
        thValUlmt: valUlmt,
      };
    } else if (cellSopPartsCD === SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD) {
      cellIndividualPara.connectedDeviceTimeOut = Number(
        cellIndividualPara.connectedDeviceTimeOut,
      );
      cellUpperLowerSetting = {
        thJudgeFlg: cellIndividualPara.upperLowerLimitCheck,
      };
    } else {
      cellUpperLowerSetting = itemNode.getProp('upperLowerSetting');
    }
    const cellLabel = itemNode.getProp('label');
    const cellDispNodeId =
      itemNode.getProp('dispNodeId') !== undefined
        ? Number(itemNode.getProp('dispNodeId'))
        : 0;
    const itemNeighbors = getCurrentNeighbors(itemNode);
    // 次ノード情報をセット
    const sopJoinOption: SopJoinDstOption = {
      condBrDst1: '',
      condBrDst2: '',
      condBrDst3: '',
      condBrDst4: '',
      condBrDst5: '',
      condBrDst6: '',
      condBrDst7: '',
      condBrDst8: '',
    };
    itemNeighbors.outgoingList.forEach((outItem, index) => {
      const dstValue = outItem.value;
      switch (index) {
        case 0:
          sopJoinOption.condBrDst1 = dstValue;
          break;
        case 1:
          sopJoinOption.condBrDst2 = dstValue;
          break;
        case 2:
          sopJoinOption.condBrDst3 = dstValue;
          break;
        case 3:
          sopJoinOption.condBrDst4 = dstValue;
          break;
        case 4:
          sopJoinOption.condBrDst5 = dstValue;
          break;
        case 5:
          sopJoinOption.condBrDst6 = dstValue;
          break;
        case 6:
          sopJoinOption.condBrDst7 = dstValue;
          break;
        case 7:
          sopJoinOption.condBrDst8 = dstValue;
          break;
        default:
          break;
      }
    });
    const sameCdFlag = checkPartCode(state.SOPSetData.partCds, cellSopPartsCD);
    const conditionFlag = checkConditionPartCode(
      state.SOPSetData,
      cellSopPartsCD,
    );
    const itemNodeData = {
      position: cellPosition,
      sopPartsCD: cellSopPartsCD,
      individualPara: {
        IndividualSetting: cellIndividualPara,
        ConditionSetting: conditionFlag ? cellConditionPara : '',
      },
      commonSetting: cellCommonSetting,
      helpSetting: cellhelpSetting,
      sopCondition: cellSopCondition,
      label: cellLabel,
      dspSeq: cellDispNodeId,
      upperLowerSetting: cellUpperLowerSetting,
      instUnitTxt: cellInstUnitTxt,
    };
    const itemNodeDataNoneIndividualPara = {
      position: cellPosition,
      sopPartsCD: cellSopPartsCD,
      individualPara: cellIndividualPara,
      commonSetting: cellCommonSetting,
      helpSetting: cellhelpSetting,
      sopCondition: cellSopCondition,
      label: cellLabel,
      dspSeq: cellDispNodeId,
      upperLowerSetting: cellUpperLowerSetting,
      instUnitTxt: cellInstUnitTxt,
    };
    // 「課題345」ADD ST NODEの連結の設計をリファクタリング
    Object.keys(sopJoinOption).forEach((key) => {
      // @ts-expect-error keyを利用して、valueを取得します
      if (sopJoinOption[key] !== '') {
        // @ts-expect-error keyを利用して、valueを取得します
        const cell = state.sopGraph!.getCellById(sopJoinOption[key]);
        // @ts-expect-error keyを利用して、valueを取得します
        sopJoinOption[key] = getOutgoing(cell);
      }
    });
    const nextNodeId =
      itemNeighbors.outgoingList.length !== 0
        ? JSON.stringify(sopJoinOption)
        : '';
    const nodeData = sameCdFlag ? itemNodeData : itemNodeDataNoneIndividualPara;
    const flowItem = setSOPFlowItemModel(
      item,
      state.SOPData.sopFlowNo,
      nodeData,
      nextNodeId,
      state.SOPSetData,
      graphWidth.value,
    );
    return flowItem;
  });
  // const addpartArr = [
  //   SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD,
  //   SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD,
  // ];
  // 収束点ノードにparentSopNodeNoをセット
  flowList.forEach((node) => {
    if (
      node.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_NAME ||
      node.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD
    ) {
      // 直前の分岐親ノードを探索
      const parent = flowList.find(
        (parentNode) =>
          parentNode.sopPartsCd === SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD ||
          (parentNode.sopPartsCd ===
            SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD &&
            parentNode.confluenceNodeId === node.sopNodeNo),
      );
      if (parent) {
        // eslint-disable-next-line no-param-reassign
        node.parentSopNodeNo = parent.sopNodeNo;
      }
    }
  });
  // 登録データセット
  flowList.forEach((value, index) => {
    if (value.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_CD) {
      const blockCell = state.sopGraph!.getCellById(value.nodeId);
      flowList[index].blkFlowNo = blockCell.getProp('blkFlowNo');
      flowList[index].blkSopSeqNo = blockCell.getProp('blkSopSeqNo');
    }
  });
  // [課題398] MOD ST
  // let renderData = flowList.filter(
  //   // @ts-expect-error 動的に変数判断
  //   (item) => !addpartArr.includes(item.sopPartsCd),
  // );
  let renderData = flowList;
  const cloneRenderData = JSON.parse(
    JSON.stringify(renderData),
  ) as typeof renderData;
  const cloneRenderDataId = cloneRenderData.map(
    (cloneItem) => cloneItem.sopNodeNo,
  );
  // [課題392] MOD ADD nextNodeNoを分解して、DBに登録します。
  let allBranchNodeList: SopFlowGetDataOption[] = [];
  const numericItemList: SopFlowGetDataOption[] = [];
  const indicesToDelete: number[] = [];
  const DEVIATION_BRANCH_NO = 9;
  const notSavePartsCd: string[] = [
    SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD,
    SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD,
  ];
  // 「課題399」MOD ST 直接にblockExpandListからノードを取得します。
  renderData.forEach((renderItem, index) => {
    const cellSopCieXYArray = findIndexIn2DArray(
      allNodesMatrixBefore,
      renderItem.nodeId,
    );
    let cellSopCieX = 0;
    let cellSopCieY = 0;
    if (cellSopCieXYArray !== null && cellSopCieXYArray.length >= 2) {
      // eslint-disable-next-line prefer-destructuring
      cellSopCieX = cellSopCieXYArray[1];
      // eslint-disable-next-line prefer-destructuring
      cellSopCieY = cellSopCieXYArray[0];
    }
    // eslint-disable-next-line no-param-reassign
    renderItem.sopCieX = cellSopCieX;
    // eslint-disable-next-line no-param-reassign
    renderItem.sopCieY = cellSopCieY;
    const branchNodeList = JSON.parse(
      renderItem.sopJoin.nextNodeNo ? renderItem.sopJoin.nextNodeNo : '{}',
    );
    // 既存のブロックはAPIを呼び出さない。
    if (renderItem.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_CD) {
      const renderItemBlkList = state.blockExpandList
        .filter(
          (expandItem) =>
            expandItem.blkFlowNo === renderItem.blkFlowNo ||
            expandItem.sopFlowNo === renderItem.blkFlowNo,
        )
        .map((item) => JSON.parse(JSON.stringify(item)));
      const minBlockNum = renderItemBlkList.reduce(
        (min, current) =>
          parseInt(current.sopNodeNo, 10) < parseInt(min.sopNodeNo, 10)
            ? current
            : min,
        renderItemBlkList[0],
      );
      if (renderItemBlkList.length !== 0) {
        // eslint-disable-next-line no-param-reassign
        // 差分値によって、再度ブロック中のノードのoutgoingを計算します。
        const differ =
          parseInt(renderItem.nodeId, 10) +
          1 -
          parseInt(minBlockNum.sopNodeNo, 10);
        renderItemBlkList.forEach((renderItemBlkValue) => {
          if (renderItemBlkValue.sopJoin.nextNodeNo !== '') {
            const sopJoinOption = JSON.parse(
              renderItemBlkValue.sopJoin.nextNodeNo,
            );
            const branchNumber = Object.keys(sopJoinOption).length;
            if (
              Object.values(sopJoinOption).filter((item) => item !== '')
                .length > 1
            ) {
              Object.values(sopJoinOption).forEach(
                (branchNodeItemValue, branchNodeItemIndex) => {
                  if (branchNodeItemValue) {
                    // eslint-disable-next-line @typescript-eslint/no-shadow
                    const duplicateFlowBranchNode = { ...renderItemBlkValue };
                    duplicateFlowBranchNode.sopJoin = {
                      ...renderItemBlkValue.sopJoin,
                    };
                    duplicateFlowBranchNode.sopNodeNo = String(
                      parseInt(renderItemBlkValue.sopNodeNo, 10) + differ,
                    ).padStart(4, '0');
                    duplicateFlowBranchNode.sopJoin.nextNodeNo = String(
                      // @ts-expect-error 既にこのリストを定義しました。
                      parseInt(branchNodeItemValue, 10) + differ,
                    ).padStart(4, '0');
                    // eslint-disable-next-line no-param-reassign
                    duplicateFlowBranchNode.blkSopSeqNo =
                      renderItem.blkSopSeqNo;
                    duplicateFlowBranchNode.sopFlowNo = renderItem.sopFlowNo;
                    duplicateFlowBranchNode.blkFlowNo = renderItem.blkFlowNo;
                    duplicateFlowBranchNode.parentSopNodeNo = renderItem.nodeId;
                    if (
                      renderItemBlkValue.sopPartsCD ===
                      SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD
                    ) {
                      duplicateFlowBranchNode.sopJoin.nextCondSeq =
                        branchNodeItemIndex + 1;
                    } else {
                      duplicateFlowBranchNode.sopJoin.nextCondSeq =
                        branchNodeItemIndex + 1 === branchNumber
                          ? DEVIATION_BRANCH_NO
                          : branchNodeItemIndex + 1;
                    }
                    duplicateFlowBranchNode.childNodeFlg = '1';
                    allBranchNodeList.push(duplicateFlowBranchNode);
                  }
                },
              );
              // eslint-disable-next-line no-param-reassign
              renderItemBlkValue.isDel = true;
            } else if (
              Object.values(sopJoinOption).filter((item) => item !== '')
                .length === 1
            ) {
              // eslint-disable-next-line no-param-reassign
              renderItemBlkValue.sopJoin.nextNodeNo = String(
                parseInt(
                  JSON.parse(renderItemBlkValue.sopJoin.nextNodeNo).condBrDst1,
                  10,
                ) + differ,
              ).padStart(4, '0');
            }
          }
          // eslint-disable-next-line no-param-reassign
          renderItemBlkValue.sopNodeNo = String(
            parseInt(renderItemBlkValue.sopNodeNo, 10) + differ,
          ).padStart(4, '0');
          // eslint-disable-next-line no-param-reassign
          renderItemBlkValue.sopFlowNo = renderItem.sopFlowNo;
          // eslint-disable-next-line no-param-reassign
          renderItemBlkValue.blkFlowNo = renderItem.blkFlowNo;
          // eslint-disable-next-line no-param-reassign
          renderItemBlkValue.blkSopSeqNo = renderItem.blkSopSeqNo;
          // eslint-disable-next-line no-param-reassign
          renderItemBlkValue.childNodeFlg = '1';
          // eslint-disable-next-line no-param-reassign
          renderItemBlkValue.parentSopNodeNo = renderItem.nodeId;
        });
        // [課題407] ADD ST ブロック中のノードの重複にリバースしない
        allBranchNodeList = allBranchNodeList.concat(renderItemBlkList);
        // [課題407] ADD ED
      }
      const curNextNodeNo = JSON.parse(
        renderItem.sopJoin.nextNodeNo,
      ).condBrDst1;
      const currentNextCell = state.sopGraph!.getCellById(curNextNodeNo);
      renderItemBlkList[renderItemBlkList.length - 1].sopJoin.nextNodeNo =
        !notSavePartsCd.includes(currentNextCell.getProp<string>('sopPartsCD'))
          ? curNextNodeNo
          : '';
      renderItemBlkList[renderItemBlkList.length - 1].sopJoin.nextCondSeq = 1;
      // eslint-disable-next-line no-param-reassign
      renderItem.sopJoin.nextNodeNo = renderItemBlkList[0].sopNodeNo;
    } else if (
      Object.values(branchNodeList).filter((item) => item !== '').length > 1
    ) {
      Object.values(branchNodeList).forEach(
        (branchNodeItemValue, branchNodeItemIndex) => {
          if (branchNodeItemValue) {
            const currentNextCell = state.sopGraph!.getCellById(
              branchNodeItemValue.toString(),
            );
            const conditionKeys = [
              'condition1',
              'condition2',
              'condition3',
              'condition4',
              'condition5',
              'condition6',
            ] as const;
            const renderItemCopy = { ...renderItem };
            if (
              branchNodeItemIndex < conditionKeys.length &&
              renderItem.sopPartsCd ===
                SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD
            ) {
              const key = conditionKeys[branchNodeItemIndex];
              const partNextCondLeft =
                renderItemCopy.sopCondition[key].nextCondLeft;
              const partNextCondOpe =
                renderItemCopy.sopCondition[key].nextCondOpe;
              const partNextCondRight =
                renderItemCopy.sopCondition[key].nextCondRight;

              renderItemCopy.sopJoin = {
                ...renderItem.sopJoin,
                nextCondLeft: partNextCondLeft,
                nextCondOpe: partNextCondOpe,
                nextCondRight: partNextCondRight,
                // @ts-expect-error 既にこのリストを定義しました。
                nextNodeNo: !notSavePartsCd.includes(
                  currentNextCell.getProp<string>('sopPartsCD'),
                )
                  ? branchNodeItemValue
                  : '',
                nextCondSeq:
                  renderItem.sopJoin.nextCondSeq === branchNodeItemIndex + 1
                    ? DEVIATION_BRANCH_NO
                    : branchNodeItemIndex + 1,
              };
            } else if (
              renderItem.sopPartsCd ===
              SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD
            ) {
              renderItemCopy.sopJoin = {
                ...renderItem.sopJoin,
                nextCondLeft: '',
                nextCondOpe: '',
                nextCondRight: '',
                // @ts-expect-error 既にこのリストを定義しました。
                nextNodeNo: !notSavePartsCd.includes(
                  currentNextCell.getProp<string>('sopPartsCD'),
                )
                  ? branchNodeItemValue
                  : '',
                nextCondSeq: branchNodeItemIndex + 1,
              };
            } else {
              renderItemCopy.sopJoin = {
                ...renderItem.sopJoin,
                nextCondLeft: '',
                nextCondOpe: '',
                nextCondRight: '',
                // @ts-expect-error 既にこのリストを定義しました。
                nextNodeNo: !notSavePartsCd.includes(
                  currentNextCell.getProp<string>('sopPartsCD'),
                )
                  ? branchNodeItemValue
                  : '',
                nextCondSeq:
                  renderItem.sopJoin.nextCondSeq === branchNodeItemIndex + 1
                    ? DEVIATION_BRANCH_NO
                    : branchNodeItemIndex + 1,
              };
            }
            renderItemCopy.childNodeFlg = '0';
            if (
              renderItemCopy.sopPartsCd ===
              SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD
            ) {
              const jsonIndiv = JSON.parse(renderItemCopy.individualPara);
              const individualPara =
                jsonIndiv.IndividualSetting as PartExternalDeviceProps;
              // jsonからnumericTextInputを削除
              delete individualPara.numericTextInput;
              // jsonからupperLowerLimitCheckを削除
              delete individualPara.upperLowerLimitCheck;
              // eslint-disable-next-line
              renderItemCopy.individualPara = JSON.stringify(jsonIndiv);
            }
            allBranchNodeList.push(renderItemCopy);
          }
        },
      );
      indicesToDelete.push(index);
    } else if (
      Object.values(branchNodeList).filter((item) => item !== '').length === 1
    ) {
      const curNextNodeNo = JSON.parse(
        renderItem.sopJoin.nextNodeNo,
      ).condBrDst1;
      const currentNextNodeCell = state.sopGraph!.getCellById(curNextNodeNo);
      // eslint-disable-next-line no-param-reassign
      renderItem.sopJoin.nextNodeNo = !notSavePartsCd.includes(
        currentNextNodeCell.getProp<string>('sopPartsCD'),
      )
        ? JSON.parse(renderItem.sopJoin.nextNodeNo).condBrDst1
        : '';
      if (
        renderItem.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD
      ) {
        const currentCell = state.sopGraph!.getCellById(renderItem.sopNodeNo);
        const coverPredecessors = state.sopGraph!.getPredecessors(currentCell, {
          deep: true,
        });
        const branchNode =
          coverPredecessors[branchNodeIndex(coverPredecessors)];
        // eslint-disable-next-line no-param-reassign
        renderItem.parentSopNodeNo = branchNode ? branchNode.id : '';
      }
    }
    // eslint-disable-next-line no-param-reassign
    renderItem.childNodeFlg = '0';
    // 「課題399」MOD ED
    if (renderItem.sopPartsCd === SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD) {
      // 外部機器通信の数値文字SOP一覧の登録データを設定します。
      const renderItemCopy = { ...renderItem };
      const jsonIndiv = JSON.parse(renderItemCopy.individualPara);
      const individualPara =
        jsonIndiv.IndividualSetting as PartExternalDeviceProps;
      individualPara.numericTextInput?.forEach((numericItem, numericIndex) => {
        const nodeNo = `${renderItemCopy.nodeId}-${numericIndex + 1}`;
        const childNodeNumericItem: NumericTextInputChildNodeProps = {
          itemText: numericItem.itemText,
          instructionValueSetting: numericItem.instructionValueSetting,
          instructionValue: numericItem.instructionValue,
          sourceItemNode: numericItem.sourceItemNode,
          sourceItemTag: numericItem.sourceItemTag,
          sourceItem: numericItem.sourceItemId,
          outputSetting: numericItem.outputSetting,
          outputValue: numericItem.outputValue,
          instructionValueType: numericItem.instructionValueType,
        };
        const individualNumeric = {
          IndividualSetting: childNodeNumericItem,
        };
        renderItemCopy.sopJoin.nextCondSeq = 1; // 外部機器通信で分岐設定されている場合、登録対象とするため1を設定します。
        const numericItemCopy: SopFlowGetDataOption = {
          childNodeFlg: '1',
          nodeId: nodeNo,
          commonSetting: renderItemCopy.commonSetting,
          helpSetting: renderItemCopy.helpSetting,
          individualPara: JSON.stringify(individualNumeric),
          sopJoin: renderItemCopy.sopJoin,
          sopCondition: renderItemCopy.sopCondition,
          parentSopNodeNo: renderItemCopy.sopNodeNo,
          sopCieX: renderItemCopy.sopCieX,
          sopCieY: renderItemCopy.sopCieY,
          sopFlowNo: renderItemCopy.sopFlowNo,
          sopNodeNo: nodeNo,
          sopPartsCd: renderItemCopy.sopPartsCd,
          blkFlowNo: renderItemCopy.blkFlowNo,
          blkSopSeqNo: renderItemCopy.blkSopSeqNo,
          dspSeq: renderItemCopy.dspSeq,
          upperLowerSetting: {
            thJudgeFlg: '1',
            thRangeType: '',
            thValType: numericItem.judgeType,
            thValLlmt:
              numericItem.deviationLowerLimit === ''
                ? null
                : Number(numericItem.deviationLowerLimit),
            thValUlmt:
              numericItem.deviationUpperLimit === ''
                ? null
                : Number(numericItem.deviationUpperLimit),
          },
          instUnitTxt: renderItemCopy.instUnitTxt,
        };
        numericItemList.push(numericItemCopy);
      });
      // jsonからnumericTextInputを削除
      delete individualPara.numericTextInput;
      // jsonからupperLowerLimitCheckを削除
      delete individualPara.upperLowerLimitCheck;
      // eslint-disable-next-line
      renderItem.individualPara = JSON.stringify(jsonIndiv);
    }
  });
  // Indexによって、削除します。
  indicesToDelete
    .sort((a, b) => b - a)
    .forEach((index) => {
      renderData.splice(index, 1);
    });
  // @ts-expect-error 既にこのリストを定義しました。
  renderData = renderData.concat(allBranchNodeList).filter((v) => !v.isDel);
  // [課題392] MOD ED
  if (numericItemList.length === 0 && state.childNodeList.length > 0) {
    state.childNodeList.forEach((childNodeValue) => {
      numericItemList.push(childNodeValue);
    });
  }
  numericItemList.forEach((numericItemValue) => {
    renderData.push(numericItemValue);
  });
  // 登録処理
  if (isReset) {
    closeLoading();
    const orderMap = new Map(
      cloneRenderDataId.map((sopNodeNo, index) => [sopNodeNo, index]),
    );
    renderData.sort((a, b) => {
      const dataItemA = orderMap.get(a.sopNodeNo)!;
      const dataItemB = orderMap.get(b.sopNodeNo)!;
      return dataItemA - dataItemB;
    });
    return renderData;
  }
  Promise.all(renderData).then(() => {
    insertSOPChart(renderData);
  });
  // 「課題345」ADD ED
  return true;
};
/**
 * ノードのパラメータ変更イベント
 * @param {*} data - ノード情報
 */
const datasourceChange = (data: SopPartDataSource) => {
  const nodeItem = state.sopGraph!.getCellById(data.id);
  if (nodeItem.isNode()) {
    console.log(state.SOPPartSetData.individualPara);
    nodeItem.prop('commonSetting', state.SOPPartSetData.commonSetting);
    nodeItem.prop('individualPara', state.SOPPartSetData.individualPara);
    nodeItem.prop('conditionProps', state.SOPPartSetData.conditionProps);
    nodeItem.prop('sopCondition', state.SOPPartSetData.sopCondition);
    nodeItem.prop('upperLowerSetting', state.SOPPartSetData.upperLowerSetting);
    nodeItem.prop('instUnitTxt', state.SOPPartSetData.instUnitTxt);
    state.isSopInfoSettingDialogVisible = state.SOPPartSetData.dispFlg;
    const sopPartsCD = nodeItem.getProp<string>('sopPartsCD');
    const sameCdFlag: boolean = checkPartCode(
      state.SOPSetData.partCds,
      sopPartsCD,
    );
    // 実行画面表示
    if (data.commonSetting.scnShowFlg === '0') {
      const nodeTools = nodeItem.getTools();
      // TODO: パーツ上でフォーカス中、アイコンが濃くなる
      const boundaryTools = nodeTools!.items.filter(
        (tool) => tool === 'boundary',
      );
      if (boundaryTools.length <= 1) {
        nodeItem.addTools({
          name: 'boundary',
          args: {
            zIndex: 0,
            attrs: {
              fill: '#f0f8ff',
              stroke: '#4169e1',
              'stroke-dasharray': '7, 8',
              strokeWidth: 3,
              fillOpacity: 0,
            },
          },
        });
      }
    } else {
      removeNodeTools(nodeItem);
    }
    addNodeTools(nodeItem);
    // wチェック
    if (data.commonSetting.dcheckFlg === '1') {
      nodeItem.attr({
        wCheck: {
          display: 'block',
        },
      });
    } else {
      nodeItem.attr({
        wCheck: {
          display: 'none',
        },
      });
    }
    // 逸脱レベル
    switch (data.commonSetting.devCorrLv) {
      case '0':
        nodeItem.attr({
          abnormalityLevel1: {
            display: 'none',
          },
          abnormalityLevel2: {
            display: 'none',
          },
          abnormalityLevel3: {
            display: 'none',
          },
        });
        break;
      case '1':
        nodeItem.attr({
          abnormalityLevel1: {
            display: 'block',
          },
          abnormalityLevel2: {
            display: 'none',
          },
          abnormalityLevel3: {
            display: 'none',
          },
        });
        break;
      case '2':
        nodeItem.attr({
          abnormalityLevel1: {
            display: 'none',
          },
          abnormalityLevel2: {
            display: 'block',
          },
          abnormalityLevel3: {
            display: 'none',
          },
        });
        break;
      case '3':
        nodeItem.attr({
          abnormalityLevel1: {
            display: 'none',
          },
          abnormalityLevel2: {
            display: 'none',
          },
          abnormalityLevel3: {
            display: 'block',
          },
        });
        break;
      default:
        break;
    }
    // 製造記録書ヘの記載
    if (data.commonSetting.recFillFlg === '1') {
      nodeItem.attr({
        write: {
          display: 'block',
        },
      });
    } else {
      nodeItem.attr({
        write: {
          display: 'none',
        },
      });
    }
    if (sameCdFlag) {
      nodeItem.prop(
        'settingConfirmFlg',
        state.SOPPartSetData.settingDecisionFlg,
      );
    }
    //   if (sopPartsCD === SOP_PARTS_VARIABLES.CONTROL_PART_PROGRESS_CHECK) {
    //   const textVal = t('SOP.Menu.txtProgressCheck');
    //   const inputValueVal: string = data.individualPara!.inputValue;
    //   if (inputValueVal !== '') {
    //     nodeItem.attr({
    //       text: {
    //         text: `${textVal}${inputValueVal}%`,
    //       },
    //     });
    //   }
    // }
  }
};
// const findInitialBranch = (node: Cell<Cell.Properties>) => {
//   const currentNodePredecessors = state
//     .sopGraph!.getPredecessors(node, { deep: true })
//     .filter((item) => item.getProp('position').y < node.getProp('position').y);
//   const findBranchNodeIndex = branchNodeIndex(currentNodePredecessors);
//   if (findBranchNodeIndex !== -1) {
//     return findInitialBranch(currentNodePredecessors[findBranchNodeIndex]);
//   }
//   return node;
// };
/**
 * SOPパーツの作成
 * @param {*} data - ノードのデータ
 */
const sopRectPartSetting = (data: SopRectPartsOption) => {
  if (data.sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_COPY_CD) {
    if (state.copyPartId === '') {
      messageBoxSingleButtonRef.value.title = `${t('SOP.Menu.txtSopTitle')}`;
      messageBoxSingleButtonRef.value.content = `${t('SOP.Msg.cannotPasted')}`;
      openDialog('singleButtonRef');
      return false;
    }
    const selCell = state.sopGraph!.getCellById(state.copyPartId);
    const { sopPartConst } = state.SOPSetData;
    if (selCell === null || !selCell.isNode()) {
      return false;
    }
    const SOPPartsCD = selCell.getProp<string>('sopPartsCD');
    const cellPosition = selCell.getProp<PositionOption>('position');
    const sopCommonSetting = selCell.getProp('commonSetting');
    const sopIndividualPara = selCell.getProp('individualPara');
    const sopPartCondition = selCell.getProp('sopCondition');
    const sopConditionProps = selCell.getProp('conditionProps');
    const sopUpperLowerSetting = selCell.getProp('upperLowerSetting');
    const sopShape = selCell.getProp<string>('shape');
    const sopInstUnitTxt = selCell.getProp('instUnitTxt');
    const sopTitle = selCell.getAttrByPath<string>('title/text');
    const sopAttr = selCell.getAttrs();
    const rectItem = {
      // 「課題27」ADD ST 「sop_node_id」を表示IDに置き換える
      id: '',
      // 「課題27」ADD ED
      shape: sopShape,
      sopPartsCD: SOPPartsCD,
      attrs: {
        body: sopAttr.body,
        image: sopAttr.image,
        text: sopAttr.text,
        title: {
          text: sopTitle,
        },
        abnormalityLevel1: sopAttr.abnormalityLevel1,
        abnormalityLevel2: sopAttr.abnormalityLevel2,
        abnormalityLevel3: sopAttr.abnormalityLevel3,
        // abnormalityLevel4: sopAttr.abnormalityLevel4,
        // abnormalityLevel5: sopAttr.abnormalityLevel5,
        wCheck: sopAttr.wCheck,
        write: sopAttr.write,
        line: sopAttr.line,
      },
      x: cellPosition.x + sopPartConst!.marginLeft,
      y: cellPosition.y + sopPartConst!.marginTop,
      zIndex: 0,
      cloneFlag: false,
      commonSetting: sopCommonSetting,
      individualPara: sopIndividualPara
        ? JSON.parse(JSON.stringify(sopIndividualPara))
        : {},
      conditionProps: sopConditionProps
        ? JSON.parse(JSON.stringify(sopConditionProps))
        : {},
      sopCondition: sopPartCondition
        ? JSON.parse(JSON.stringify(sopPartCondition))
        : {},
      instUnitTxt: sopInstUnitTxt,
      upperLowerSetting: sopUpperLowerSetting,
    };
    state.checkeAddPartFlag = true;
    state.sopPartAddType = 'createPartNode';
    // 「課題27」ADD ST 「sop_node_id」を表示IDに置き換える
    rectItem.id = setNodeId();
    // 「課題27」ADD ED
    if (
      rectItem.individualPara.conditionBranch ||
      rectItem.conditionProps.conditionBranch
    ) {
      rectItem.individualPara.conditionBranch = '';
      rectItem.individualPara.deviationBranchNodeId = '';
      if (rectItem.conditionProps instanceof Object) {
        rectItem.conditionProps.conditionBranch = '';
        rectItem.conditionProps.deviationBranchNodeId = '';
      }
    }
    if (SOPPartsCD === SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD) {
      state.destinationParts.forEach((item) => {
        rectItem.individualPara[item] = '';
      });
      state.destinationMenus.forEach((item) => {
        rectItem.individualPara[item] = '';
      });
    }
    if (SOPPartsCD === SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD) {
      state.destinationParts.forEach((item) => {
        rectItem.individualPara[item] = '';
      });
      state.destinationMessages.forEach((item) => {
        rectItem.individualPara[item] = '';
      });
      rectItem.individualPara.branchNodeIdDefault = '';
      rectItem.individualPara.branchMessageDefault = '';
      Object.values(rectItem.sopCondition).forEach((item) => {
        Object.keys(item as Record<string, object>).forEach((key) => {
          // @ts-expect-error 既に定義しました。
          // eslint-disable-next-line no-param-reassign
          item[key] = '';
        });
      });
    }
    state.sopGraph!.addNode(rectItem);
    // const currentNode = state.sopGraph!.getCellById(rectItem.id);
  } else if (state.checkedAddPartId !== '') {
    const currentCell = state.sopGraph!.getCellById(state.checkedAddPartId);
    const { sopPartConst } = state.SOPSetData;
    if (currentCell !== null && currentCell.isNode()) {
      const cellPosition = currentCell.getProp<PositionOption>('position');
      const sopCommonSetting = state.SOPSetData.commonSetting;
      const sopIndividualPara = state.SOPSetData.individualPara;
      const sopConditionProps = state.SOPSetData.conditionProps;
      const sopPartCondition = state.SOPSetData.sopCondition;
      const sopUpperLowerSetting = state.SOPSetData.upperLowerSetting;
      const rectItem = { ...data };
      rectItem.x = cellPosition.x + sopPartConst!.marginLeft;
      rectItem.y = cellPosition.y + sopPartConst!.marginTop;
      rectItem.attrs.image = {
        'xlink:href': state.imageMap.get(rectItem.imageName[0]),
        width: 25,
        height: 25,
        x: 8,
        y: 20,
      };
      rectItem.attrs.wCheck = {
        'xlink:href': state.imageMap.get(rectItem.imageName[4]),
        width: 20,
        height: 20,
        x: 5,
        y: 5,
      };
      rectItem.attrs.abnormalityLevel1 = {
        'xlink:href': state.imageMap.get(rectItem.imageName[5]),
        width: 20,
        height: 20,
        x: 5,
        y: 5,
      };
      rectItem.attrs.abnormalityLevel2 = {
        'xlink:href': state.imageMap.get(rectItem.imageName[6]),
        width: 20,
        height: 20,
        x: 5,
        y: 5,
      };
      rectItem.attrs.abnormalityLevel3 = {
        'xlink:href': state.imageMap.get(rectItem.imageName[7]),
        width: 20,
        height: 20,
        x: 5,
        y: 5,
      };
      rectItem.attrs.write = {
        'xlink:href': state.imageMap.get(rectItem.imageName[10]),
        width: 20,
        height: 20,
        x: 5,
        y: 5,
      };
      rectItem.zIndex = 0;
      rectItem.cloneFlag = false;
      rectItem.settingConfirmFlg = false;
      rectItem.commonSetting = sopCommonSetting;
      const sopPartsNm = convertSopPartsName(
        rectItem.sopPartsCD,
      ) as keyof typeof sopIndividualPara;
      rectItem.individualPara = getItemValueByKey(
        sopIndividualPara,
        sopPartsNm,
      );
      rectItem.conditionProps = sopConditionProps;
      rectItem.sopCondition = sopPartCondition;
      rectItem.upperLowerSetting = sopUpperLowerSetting;
      state.checkeAddPartFlag = true;
      state.sopPartAddType = 'createPartNode';
      // 「課題27」ADD ST 「sop_node_id」を表示IDに置き換える
      // @ts-expect-error idを追加する
      rectItem.id = setNodeId();
      // 「課題27」ADD ST
      state.sopGraph!.addNode(rectItem);
      adjustHorizenPosition();
    }
  }
  return true;
};
/**
 * SOPブロックの作成
 * @param {*} data - ノードのデータ
 */
const sopBlockPartSetting = (data: SopBlockPartOption) => {
  if (state.checkedAddPartId !== '') {
    const currentCell = state.sopGraph!.getCellById(state.checkedAddPartId);
    const { sopPartConst } = state.SOPSetData;
    if (currentCell !== null && currentCell.isNode()) {
      const sopCommonSetting = state.SOPSetData.commonSetting;
      const cellPosition = currentCell.getProp<PositionOption>('position');
      const blockItem = { ...data };
      blockItem.attrs.image = {
        'xlink:href': state.imageMap.get(blockItem.imageName),
        width: 25,
        height: 25,
        x: 8,
        y: 20,
      };
      blockItem.x = cellPosition.x + sopPartConst!.marginLeft;
      blockItem.y = cellPosition.y + sopPartConst!.marginTop;
      blockItem.zIndex = 0;
      blockItem.cloneFlag = false;
      blockItem.commonSetting = sopCommonSetting;
      blockItem.individualPara = {};
      state.checkeAddPartFlag = true;
      state.sopPartAddType = 'createPartNode';
      // 「課題27」ADD ST 「sop_node_id」を表示IDに置き換える
      blockItem.id = setNodeId();
      // 「課題27」ADD ED
      state.sopGraph!.addNode(blockItem);
    }
    // テンプレート選択画面を起動する
    const allNodes = state.sopGraph!.getNodes();
    allNodes.forEach((item: Node) => {
      const PartsCD = item.getProp<string>('sopPartsCD');
      if (
        PartsCD === SOP_PARTS_VARIABLES.PART_SPEC_TEMPLATE_CD ||
        PartsCD === SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_CD
      ) {
        state.localeCheckId = item.id;
      }
    });
    state.localeCheckType = 'node';
    if (data.sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_TEMPLATE_CD) {
      state.SOPBlockType = 'T';
      state.SOPblockNo = '';
      state.SOPSelectFlag = true;
    } else if (data.sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_CD) {
      state.SOPBlockType = 'B';
      state.SOPblockNo = data.sopPartsCD;
      state.SOPSelectFlag = true;
    } else {
      state.SOPBlockType = '';
      state.SOPblockNo = '';
      state.SOPSelectFlag = false;
    }
  }
};
/**
 * ブロックを選択されていない場合、空ブロックノードを削除する
 */
const deleteBlockNode = () => {
  const isDeleteFlag = state
    .sopGraph!.getCellById(state.localeCheckId)
    .getProp('blkFlowNo');
  if (!isDeleteFlag) {
    const blockNode = state.sopGraph!.getCellById(state.localeCheckId);
    const blockIncomingAddPart = state.sopGraph!.getNeighbors(blockNode, {
      incoming: true,
    })[0];
    const blockOutgoingAddPart = state.sopGraph!.getNeighbors(blockNode, {
      outgoing: true,
    })[0];
    const blockOutgoing = state.sopGraph!.getNeighbors(blockOutgoingAddPart, {
      outgoing: true,
    })[0];
    const transVal = getTranslatePosition(blockIncomingAddPart, blockOutgoing);
    const coverNodeSuccessors = state.sopGraph!.getSuccessors(blockNode, {
      deep: true,
    });
    coverNodeSuccessors.forEach((item) => {
      item.translate(0, transVal.translateY);
    });
    addBlockTargetEdge(blockIncomingAddPart.id, blockOutgoing.id);
    state.sopGraph!.removeNode(state.localeCheckId);
    state.sopGraph!.removeNode(blockOutgoingAddPart.id);
  }
};
/**
 * SOP制御ノードの作成
 * @param {*} data - ノードのデータ
 */
const sopControlPartSetting = (data: SopControlPartOption) => {
  if (state.checkedAddPartId !== '') {
    const currentCell = state.sopGraph!.getCellById(state.checkedAddPartId);
    const { sopPartConst } = state.SOPSetData;
    if (currentCell !== null && currentCell.isNode()) {
      const sopCommonSetting = state.SOPSetData.commonSetting;
      const sopIndividualPara = state.SOPSetData.individualPara;
      const cellPosition = currentCell.getProp<PositionOption>('position');
      const controlItem = { ...data };
      controlItem.x = cellPosition.x + sopPartConst!.marginLeft;
      controlItem.y = cellPosition.y + sopPartConst!.marginTop;
      controlItem.zIndex = 0;
      controlItem.cloneFlag = false;
      controlItem.commonSetting = sopCommonSetting;
      const sopPartsCD =
        controlItem.sopPartsCD as keyof typeof sopIndividualPara;
      controlItem.individualPara = getItemValueByKey(
        sopIndividualPara,
        sopPartsCD,
      );
      state.checkeAddPartFlag = true;
      state.sopPartAddType = 'createPartNode';
      // 「課題27」ADD ST 「sop_node_id」を表示IDに置き換える
      controlItem.id = setNodeId();
      // 「課題27」ADD ED
      state.sopGraph!.addNode(controlItem);
    }
  }
};

/**
 * マージ処理用 型定義
 */
type Matrix = string[][];
type BranchList = { [branchId: string]: string[] }; // 分岐親ID: 所属ノードIDリスト

/**
 * マージ処理用 ユーティリティ関数
 *
 */
// 2次元配列のディープコピー
function cloneMatrix(matrix: Matrix): Matrix {
  return matrix.map((row) => [...row]);
}

function findNodeRow(matrix: Matrix, nodeId: string): number {
  return matrix.findIndex((row) => row.includes(nodeId));
}

function findParentBranchId(
  branchList: BranchList,
  nodeId: string,
): string | null {
  let foundBranchId: string | null = null;
  Object.keys(branchList).some((branchId) => {
    if (
      JSON.stringify(branchList[branchId]) !== '{}' &&
      branchList[branchId].includes(nodeId)
    ) {
      foundBranchId = branchId;
      return true;
    }
    return false;
  });
  return foundBranchId;
}

function findNearParentBranchId(
  branchList: BranchList,
  nodeId: string,
): string | null {
  let foundBranchId: string | null = null;
  // キーを昇順または降順でソート
  const branchIds = Object.keys(branchList).sort((a, b) => b.localeCompare(a)); // 降順
  branchIds.some((branchId) => {
    if (
      JSON.stringify(branchList[branchId]) !== '{}' &&
      branchList[branchId].includes(nodeId)
    ) {
      foundBranchId = branchId;
      return true;
    }
    return false;
  });
  return foundBranchId;
}

// 既存値があるかチェック（列）
// function checkColumnsBeforeExtend(matrix: Matrix, startCol: number, numCols: number) {
//   const existingColIndexes = [];
//   for (let col = startCol; col < startCol + numCols; col++) {
//     for (let row = 0; row < matrix.length; row++) {
//       if (matrix[row][col] !== undefined && matrix[row][col] !== null && matrix[row][col] !== '') {
//         existingColIndexes.push(col);
//         break;
//       }
//     }
//   }
//   return existingColIndexes;
// }

// 既存値があるかチェック（行）
// function checkRowsBeforeInsert(matrix, insertIndexes) {
//   let existingRowIndexes = [];
//   insertIndexes.forEach(idx => {
//     if (matrix[idx] && matrix[idx].some(cell => cell !== undefined && cell !== null && cell !== '')) {
//       existingRowIndexes.push(idx);
//     }
//   });
//   return existingRowIndexes;
// }
function getInsertPosition(
  row: number,
  targetRow: number,
  currentCol: number,
  matrix: Matrix,
) {
  switch (true) {
    case row === targetRow + 1 || row === targetRow + 2:
      // 直後または2行後
      return currentCol;
    case row < targetRow:
      // 上
      return currentCol;
    case row > targetRow:
      // 下
      return matrix[row].length;
    default:
      // 同じ行
      return currentCol;
  }
}
function removeEmptyColumns(matrix: Matrix) {
  if (matrix.length === 0) return;

  // 末尾列のインデックスを取得
  const lastColIndex = Math.max(...matrix.map((row) => row.length)) - 1;

  // すべての行で末尾列が存在し、かつ''であるかチェック
  const isAllLastColEmpty = matrix.every(
    (row) => row.length > lastColIndex && row[lastColIndex] === '',
  );

  if (isAllLastColEmpty) {
    // 末尾列を削除
    matrix.forEach((row) => {
      if (row.length > lastColIndex) {
        row.splice(lastColIndex, 1);
      }
    });
    // 再帰的にもう一度実行
    removeEmptyColumns(matrix);
  }
}
// 列追加
type CheckParams = {
  parentBranchId: string;
  branchList: BranchList;
  parentToConfluenceMap: Record<string, string>;
  targetNodeId: string;
  parentBranchList: string[];
};
function addColumnIfEmpty(
  matrix: Matrix,
  insertCnt: number,
  targetRow: number,
  targetCol: number,
  minRow: number,
  maxRow: number,
  checkParams: CheckParams,
) {
  const {
    parentBranchId,
    branchList,
    parentToConfluenceMap,
    targetNodeId,
    parentBranchList,
  } = checkParams;
  if (
    insertCnt <= 0 ||
    targetRow < 0 ||
    targetCol < 0 ||
    minRow < 0 ||
    maxRow < minRow
  ) {
    return; // 無効な引数
  }
  // 列追加処理
  for (let i = 0; i < insertCnt; i++) {
    const currentCol = targetCol + i;
    let canAdd = true;
    let isBetweenInsert = false;

    // 追加有無判定（currentColごとに毎回チェック）
    if (typeof matrix[targetRow][currentCol] !== 'undefined') {
      // 対象列が存在する場合
      if (
        branchList[parentBranchId] &&
        branchList[parentBranchId].includes(targetNodeId)
      ) {
        // 同一分岐チェック
        const rowFrom = findNodeRow(matrix, parentBranchId);
        const rowTo = findNodeRow(
          matrix,
          parentToConfluenceMap[parentBranchId],
        );
        // 同一分岐内に親分岐パーツが同列に存在するかチェック
        for (let r = rowFrom; r <= rowTo; r++) {
          if (
            Array.isArray(matrix[r]) &&
            parentBranchList.includes(matrix[r][currentCol])
          ) {
            isBetweenInsert = true;
            break;
          }
        }
        // ひとつ上の親分岐パーツが同列に存在するかチェック
        const nearParentId =
          findNearParentBranchId(branchList, targetNodeId) || targetNodeId;
        const nearParentRow = findNodeRow(matrix, nearParentId);
        const nearBranchList = matrix[nearParentRow + 1].filter(
          (cell) => cell !== undefined && cell !== null && cell !== '',
        );
        for (let r = rowFrom; r <= nearParentRow + 1; r++) {
          if (
            Array.isArray(matrix[r]) &&
            nearBranchList.includes(matrix[r][currentCol])
          ) {
            isBetweenInsert = true;
            break;
          }
        }
        if (!isBetweenInsert) {
          if (
            matrix[targetRow][currentCol] !== undefined &&
            matrix[targetRow][currentCol] !== null &&
            matrix[targetRow][currentCol] !== ''
          ) {
            // 追加可能
            isBetweenInsert = true;
            canAdd = true;
          } else {
            // 親分岐パーツが同列に存在しない場合、追加不可
            canAdd = false;
          }
        }
        // 同一分岐内に親分岐パーツが同列に存在する場合、追加可能
      } else {
        // 追加不可
        canAdd = false;
      }
    } else {
      // 対象列が存在しない場合は追加可能
    }
    // 追加不可ならこの列はスキップ
    if (!canAdd) {
      // Skip the rest of this iteration
    } else {
      let startBranchRow = -1;
      let endBranchBranchRow = -1;
      if (
        branchList[parentBranchId] &&
        branchList[parentBranchId].includes(targetNodeId)
      ) {
        // 同一分岐チェック
        startBranchRow = findNodeRow(matrix, parentBranchId);
        endBranchBranchRow = findNodeRow(
          matrix,
          parentToConfluenceMap[parentBranchId],
        );
      }
      // 列追加
      for (let row = 0; row < matrix.length; row++) {
        let insertPos;
        if (row >= minRow && row < maxRow) {
          if (
            startBranchRow !== -1 &&
            endBranchBranchRow !== -1 &&
            row >= startBranchRow &&
            row <= endBranchBranchRow
          ) {
            // 同一分岐内の行
            if (isBetweenInsert) {
              insertPos = getInsertPosition(row, targetRow, currentCol, matrix);
            } else {
              // 通常処理
              insertPos = matrix[row].length; // 末尾に追加
            }
          } else {
            // 分岐外の行
            insertPos = matrix[row].length; // 末尾に追加
          }
        }
        matrix[row].splice(
          insertPos !== undefined ? insertPos : matrix[row].length,
          0,
          '',
        );
      }
    }
  }
}
// 列追加
function addColumn(
  matrix: Matrix,
  insertCnt: number,
  targetRow: number,
  targetCol: number,
  checkParams: CheckParams,
) {
  const { parentBranchId, parentToConfluenceMap } = checkParams;
  if (insertCnt <= 0 || targetRow < 0 || targetCol < 0) {
    return; // 無効な引数
  }
  // 同一分岐の範囲を取得
  const rowFrom = findNodeRow(matrix, parentBranchId);
  const rowTo = findNodeRow(matrix, parentToConfluenceMap[parentBranchId]);

  // 列追加処理
  for (let i = 0; i < insertCnt; i++) {
    const currentCol = targetCol + i;
    // 追加位置判定
    for (let row = 0; row < matrix.length; row++) {
      let insertPos;
      if (row >= rowFrom && row < rowTo) {
        insertPos = getInsertPosition(row, targetRow, currentCol, matrix); // 間に挿入
      } else {
        insertPos = matrix[row].length; // 末尾に追加
      }
      matrix[row].splice(
        insertPos !== undefined ? insertPos : matrix[row].length,
        0,
        '',
      );
    }
  }
}
// 指定列がなければ全行拡張
// function ensureColumn(matrix: string[][], colIndex: number) {
//   for (let rowIndex = 0; rowIndex < matrix.length; rowIndex++) {
//     const row = matrix[rowIndex];
//     if (row.length <= colIndex) {
//       while (row.length <= colIndex) {
//         row.push('');
//       }
//     }
//   }
// }
// 指定行がなければ追加
// function ensureRow(matrix: Matrix, rowIndex: number) {
//   while (matrix.length <= rowIndex) matrix.push([]);
// }
/**
 * マージ処理用 サポート関数
 *
 */
function findNodeCol(matrix: Matrix, nodeId: string): number {
  for (let r = 0; r < matrix.length; r++) {
    const c = matrix[r].indexOf(nodeId);
    if (c !== -1) return c;
  }
  return -1;
}
function isConfluenceNode(nodeId: string): boolean {
  return /^\d{4}$/.test(nodeId); // 4桁数字
}
function removeRowIfEmpty(
  matrix: Matrix,
  rowIndex: number,
  ignoreVals: string[],
) {
  // 1. targetRowが範囲外なら何もしない
  if (rowIndex < 0 || rowIndex >= matrix.length) return;

  // 2. targetRowにignoreVals以外の値がセットされている列があるか判定
  const rowHasValue = matrix[rowIndex].some((cell) => {
    if (cell === null || cell === undefined || cell === '') {
      return false;
    }
    if (ignoreVals.includes(cell)) {
      return false;
    }
    // ignoreVals以外の値がセットされている
    return true;
  });

  // 3. すでに値がセットされている場合は削除しない
  if (rowHasValue) {
    // 行削除しない
    return;
  }

  // 4. 値がセットされていない場合のみ行削除
  matrix.splice(rowIndex, 1);
}
function removeRowsIfEmpty(
  matrix: Matrix,
  nodeIds: string[],
  deleteRowCount: number,
) {
  // 1. 削除対象の行番号をリストアップ
  const rowsToRemove: number[] = [];
  nodeIds.forEach((id) => {
    const row = findNodeRow(matrix, id);
    if (row !== -1) {
      // nodeIdsに含まれていない値だけでEmpty判定
      const hasOtherValues = matrix[row].some(
        (cell) => cell && !nodeIds.includes(cell),
      );
      if (!hasOtherValues) {
        if (!rowsToRemove.includes(row)) {
          rowsToRemove.push(row);
        }
      }
    }
  });

  // 行番号の重複を排除し、昇順にソート
  const uniqueRowsToRemove = Array.from(new Set(rowsToRemove)).sort(
    (a, b) => a - b,
  );

  // 2. 指定された削除行数だけ削除（行番号の降順で削除）
  uniqueRowsToRemove
    .slice(0, deleteRowCount)
    .sort((a, b) => b - a)
    .forEach((row) => {
      matrix.splice(row, 1);
    });
}
function removeColumnIfEmpty(
  matrix: Matrix,
  nodeIds: string[],
  col: number,
  minRow: number,
  maxRow: number,
) {
  let canRemove = true;
  for (let r = minRow; r <= maxRow; r++) {
    if (!Array.isArray(matrix[r])) {
      // Skip if not an array
      break;
    }
    const value = matrix[r][col];
    // nodeIdsに含まれていない値（許容値以外）が1つでもあれば削除不可
    if (value !== '' && !nodeIds.includes(value)) {
      canRemove = false;
      break;
    }
  }
  if (canRemove) {
    matrix.forEach((row) => {
      if (Array.isArray(row)) row.splice(col, 1);
    });
  }
  // 全行の末尾列が''の場合、全行の末尾列を削除する
  if (matrix.length > 0) {
    // 末尾列のインデックスを取得
    const lastColIndex = Math.max(...matrix.map((row) => row.length)) - 1;
    // 全行の末尾列が''かチェック
    const isAllLastColEmpty = matrix.every((row) => row[lastColIndex] === '');
    if (isAllLastColEmpty) {
      removeEmptyColumns(matrix);
    }
  }
}
function addRowIfEmpty(matrix: Matrix, rowIndex: number, ignoreVals: string[]) {
  // 1. targetRowが範囲外なら何もしない
  if (rowIndex < 0 || rowIndex >= matrix.length) return;

  // 2. targetRowにignoreVals以外の値がセットされている列があるか判定
  const rowHasValue = matrix[rowIndex].some((cell) => {
    if (cell === null || cell === undefined || cell === '') {
      return false;
    }
    if (ignoreVals.includes(cell)) {
      return false;
    }
    // ignoreVals以外の値がセットされている
    return true;
  });

  // 3. すでに値がセットされている場合は追加しない
  if (rowHasValue) {
    console.log(`Row ${rowIndex} already has values, not adding a new row.`);
    // 行削除しない
    return;
  }
  // 4. 値がセットされていない場合のみ行追加
  matrix.splice(rowIndex, 0, Array(matrix[0].length).fill(''));
}
function setValue(
  matrix: Matrix,
  row: number,
  col: number,
  value: string,
): Matrix {
  // matrixのディープコピーを作成
  const newMatrix = matrix.map((r) => [...r]);
  newMatrix[row][col] = value;

  return newMatrix;
}
// function setValueWithShift(matrix: Matrix, row: number, col: number, value: string) {
//   ensureRow(matrix, row);
//   ensureColumn(matrix, col);
//   if (matrix[row][col]) {
//     // 右にずらす
//     for (let c = matrix[row].length - 1; c > col; c--) {
//       matrix[row][c] = matrix[row][c - 1];
//     }
//     matrix[row][col + 1] = matrix[row][col];
//     matrix[row][col] = '';
//   }
//   matrix[row][col] = value;
// }
function setValueWithShift(
  matrix: Matrix,
  row: number,
  col: number,
  value: string,
): Matrix {
  // ensureRow(matrix, row);
  // ensureColumn(matrix, col);
  // matrixのディープコピーを作成
  const newMatrix = matrix.map((r) => [...r]);

  if (newMatrix[row][col]) {
    // 右にずらす
    for (let c = newMatrix[row].length - 1; c > col; c--) {
      newMatrix[row][c] = newMatrix[row][c - 1];
    }
    newMatrix[row][col + 1] = newMatrix[row][col];
    newMatrix[row][col] = '';
  }
  newMatrix[row][col] = value;

  return newMatrix;
}
/**
 * 削除対象ノードIDリストから、列インデックス配列を降順で返す
 */
function getRemoveCols(matrix: Matrix, nodeIds: string[]): number[] {
  // nodeIdsに対応する列インデックス
  const nodeIdCols = nodeIds
    .map((id) => findNodeCol(matrix, id))
    .filter((col) => col !== -1);

  // nodeIdsで指定された最大列インデックス
  const maxNodeIdCol = nodeIdCols.length > 0 ? Math.max(...nodeIdCols) : -1;

  // nodeIds以降で値が存在する列インデックスを追加
  const extraCols: number[] = [];
  if (matrix.length > 0) {
    const colCount = matrix[0].length;
    for (let col = maxNodeIdCol + 1; col < colCount; col++) {
      // どの行でも値が存在する列を追加
      if (
        matrix.some(
          (row) =>
            row[col] !== null && row[col] !== undefined && row[col] !== '',
        )
      ) {
        extraCols.push(col);
      }
    }
  }

  // 重複を除いて降順ソート
  const result = Array.from(new Set([...nodeIdCols, ...extraCols])).sort(
    (a, b) => b - a,
  );
  return result;
}
/**
 * 親分岐パーツ～収束ノードまでの行範囲を返す
 */
function getRowRangeForParentBranch(
  matrix: Matrix,
  targetNode: Node,
  parentBranchId: string,
  parentToConfluenceMap: Record<string, string>,
  minRow: number,
  maxRow: number,
): [number, number] {
  if (
    targetNode.id === parentBranchId ||
    parentToConfluenceMap[parentBranchId]
  ) {
    return [
      findNodeRow(matrix, parentBranchId),
      findNodeRow(matrix, parentToConfluenceMap[parentBranchId]),
    ];
  }
  return [minRow, maxRow];
}
/**
 * 指定行で、削除対象列の値を左に詰めて、削除対象は空白にする
 */
function shiftAndClearRowForRemove(
  matrix: Matrix,
  rowIdx: number,
  removeCols: number[],
  removeNodeIds: string[],
) {
  const deleteCount = removeCols.length - 1;
  const rowCopy = [...matrix[rowIdx]];
  // 削除対象列を順に処理
  removeCols.forEach((col, idx) => {
    const shiftToCol = col - deleteCount + idx;
    const cellValue = rowCopy[col];
    if (cellValue) {
      if (removeNodeIds.includes(cellValue)) {
        rowCopy[col] = '';
      } else if (shiftToCol >= 0) {
        // 左にずらす
        for (let shift = 0; shift < deleteCount; shift++) {
          if (col + shift === shiftToCol + shift) {
            // 同じ列なら何もしない
          } else {
            rowCopy[shiftToCol + shift] = rowCopy[col + shift] || '';
            rowCopy[col + shift] = '';
          }
        }
      }
    }
  });
  const updatedMatrix = matrix;
  updatedMatrix[rowIdx] = rowCopy;
}
// 指定した行のtargetCol+1以降の非空セルを左に詰める
function shiftLeftNonEmptyCells(
  rowArray: string[],
  startCol: number,
): string[] {
  const arr = [...rowArray];
  for (let col = startCol + 1; col < arr.length; col++) {
    if (arr[col] !== '') {
      arr[col - 1] = arr[col];
      arr[col] = '';
    }
  }
  return arr;
}
/**
 * 末尾の空行（全て空またはundefinedまたは''の行）を削除する
 */
function removeEmptyRow(matrix: Matrix): void {
  const targetIndex = matrix.length - 2; // ENDノード上の行
  if (
    targetIndex >= 0 &&
    matrix[targetIndex].every(
      (cell) => cell === undefined || cell === null || cell === '',
    )
  ) {
    matrix.splice(targetIndex, 1);
  }
}
/**
 * 全行の末尾列が''の場合、全行の末尾列を削除する
 * @param {Array<Array<any>>} matrix
 */
function removeEmptyColumn(matrix: Matrix) {
  if (matrix.length === 0) return;
  const lastColIndex = Math.max(...matrix.map((row) => row.length)) - 1;
  const isAllLastColEmpty = matrix.every((row) => row[lastColIndex] === '');
  if (isAllLastColEmpty) {
    removeEmptyColumns(matrix);
  }
}
// 指定した行のtargetCol+1以降の非空セル数をカウント
function countNonEmptyCells(rowArray: string[], startCol: number): number {
  let count = 0;
  for (let col = startCol + 1; col < rowArray.length; col++) {
    if (rowArray[col] !== '') count++;
  }
  return count;
}
/**
 * 分岐削除可否をチェックする共通関数
 * @param allNodesMatrix ノードのマトリクス
 * @param parentToConfluenceMap 親ノードID→収束ノードIDマップ
 * @param targetNode 対象ノード
 * @param targetRow 対象ノードの行番号
 * @returns 削除可能ならtrue、不可ならfalse
 */
function canRemoveBranch(
  allNodesMatrix: string[][],
  parentToConfluenceMap: Record<string, string>,
  targetNode: Node,
  targetRow: number,
  targetCol?: number,
): boolean {
  const confluenceNodeId = parentToConfluenceMap[targetNode.id];
  for (let row = targetRow + 1; row < allNodesMatrix.length; row++) {
    // 判定開始列を決定
    let startCol = 0;
    if (typeof targetCol === 'number') {
      startCol = targetCol;
    }
    for (let col = startCol; col < allNodesMatrix[row].length; col++) {
      if (
        allNodesMatrix[row][col] === undefined ||
        allNodesMatrix[row][col] === null ||
        allNodesMatrix[row][col] === ''
      ) {
        // 空セルはスキップ
      } else {
        const node = state.sopGraph!.getCellById(allNodesMatrix[row][col]);
        if (node && !node.isNode()) {
          break;
        }
        const sopPartsCD = node.getProp<string>('sopPartsCD');
        const sameFlg = checkPartCode(state.SOPSetData.partCds, sopPartsCD);
        // 収束ノードに到達したら終了
        if (node.id === confluenceNodeId) {
          break;
        }
        // 分岐ノードの下の行から収束ノードまでの間にパーツがあるか判定
        if (
          node.id !== targetNode.id &&
          node.id !== confluenceNodeId &&
          sameFlg
        ) {
          return false;
        }
      }
    }
    // 収束ノードに到達したらループ終了
    if (allNodesMatrix[row].some((n) => n && n === confluenceNodeId)) {
      break;
    }
  }
  return true;
}

/**
 * マージ処理本体
 */
interface MergeParams {
  matrix: Matrix;
  targetNode: Node;
  addNodeIds: string[];
  removeNodeIds: string[];
  branchList: BranchList;
  parentToConfluenceMap: Record<string, string>;
  templateMatrix?: Matrix;
  pattern:
    | 'branchSet'
    | 'branchRelease'
    | 'branchAdd'
    | 'branchRemove'
    | 'templateAdd';
}
function mergeMatrix(params: MergeParams): Matrix {
  const {
    matrix,
    targetNode,
    addNodeIds,
    removeNodeIds,
    branchList,
    parentToConfluenceMap,
    templateMatrix,
    pattern,
  } = params;
  let newMatrix = cloneMatrix(matrix);
  console.log(
    'Merge Matrix Start',
    'newMatrix: ',
    newMatrix,
    '\ntargetNode:',
    targetNode,
    '\naddNodeIds: ',
    addNodeIds,
    '\nremoveNodeIds',
    removeNodeIds,
    '\nbranchList: ',
    branchList,
    '\nparentToConfluenceMap: ',
    parentToConfluenceMap,
    '\npattern: ',
    pattern,
  );
  // 1. 対象位置特定
  const targetRow = findNodeRow(newMatrix, targetNode.id);
  const targetCol = findNodeCol(newMatrix, targetNode.id);
  if (targetRow === -1 || targetCol === -1) return newMatrix;
  console.log(
    'Target Position',
    'targetRow:',
    targetRow,
    'targetCol:',
    targetCol,
  );

  // 2. 追加/削除ノード分類
  const addConfluence = addNodeIds.filter((id) => isConfluenceNode(id));
  const addOthers = addNodeIds.filter((id) => !isConfluenceNode(id));
  const removeConfluence = removeNodeIds.filter((id) => isConfluenceNode(id));
  const removeOthers = removeNodeIds.filter((id) => !isConfluenceNode(id));

  // 3. 分岐親パーツ特定
  const parentBranchId =
    findParentBranchId(branchList, targetNode.id) || targetNode.id;
  // 親分岐の分岐パーツを特定
  const parentRow = findNodeRow(newMatrix, parentBranchId);
  const parentBranchList = newMatrix[parentRow + 1].filter(
    (cell) => cell !== undefined && cell !== null && cell !== '',
  );

  // 4. マージ対象行範囲
  const minRow = 0;
  const maxRow = newMatrix.length - 1;

  // 5. マージ無視リスト
  const ignoreVals: string[] = [];
  const endNodeId = matrix[newMatrix.length - 1][0];

  switch (pattern) {
    case 'branchSet': {
      // 1. 行削除
      ignoreVals.length = 0;
      ignoreVals.push(
        ...addOthers,
        ...removeOthers,
        endNodeId,
        parentToConfluenceMap[parentBranchId],
      );
      removeRowIfEmpty(newMatrix, targetRow + 1, ignoreVals);
      // 2. 列追加
      const addColCnt = addOthers.length - 1;
      addColumnIfEmpty(
        newMatrix,
        addColCnt,
        targetRow,
        targetCol + 1,
        minRow,
        maxRow,
        {
          parentBranchId,
          branchList,
          parentToConfluenceMap,
          targetNodeId: targetNode.id,
          parentBranchList,
        },
      );
      // 3. 列追加後の事後処理
      // const [rowFrom, rowTo] = getRowRangeForParentBranch(
      //   newMatrix, targetNode, parentBranchId, parentToConfluenceMap, minRow, maxRow
      // );
      // const rowIndexes = getRowIndexes(rowFrom, rowTo);
      // const notIncludedRowIndexes = getNotIncludedRowIndexes(newMatrix.length, rowIndexes);
      // // 同一分岐外の行の値を左にずらす
      // shiftValuesLeftForRows(newMatrix, notIncludedRowIndexes, minRow, maxRow, targetCol, addColCnt);
      // 4.行追加
      ignoreVals.length = 0;
      const existingNodes = newMatrix[targetRow + 1]
        .map((cell) => (cell !== undefined && cell !== null ? cell : ''))
        .filter((cell) => cell !== '' && /^\d{4}$/.test(cell));
      // 追加する行の値を設定
      const nearParentId =
        findNearParentBranchId(branchList, targetNode.id) || targetNode.id;
      ignoreVals.push(
        ...addOthers,
        ...removeOthers,
        endNodeId,
        parentToConfluenceMap[parentBranchId],
        ...existingNodes,
        parentToConfluenceMap[nearParentId],
      );
      addRowIfEmpty(newMatrix, targetRow + 1, ignoreVals);
      addRowIfEmpty(newMatrix, targetRow + 2, ignoreVals);
      // 5.値セット
      for (let i = 0; i < addOthers.length; i++) {
        newMatrix = setValue(
          newMatrix,
          targetRow + 1,
          targetCol + i,
          addOthers[i],
        );
      }
      newMatrix = setValue(
        newMatrix,
        targetRow + 2,
        targetCol,
        addConfluence[0],
      );
      break;
    }

    case 'branchRelease': {
      // 行削除
      const nodeidsToRemove = [...removeOthers, ...removeConfluence];
      removeRowsIfEmpty(newMatrix, nodeidsToRemove, 1);

      // 値セット前のチェック
      let hasValueInTargetCol = false;
      // 値セット
      if (parentBranchId !== targetNode.id) {
        // 親分岐パーツが存在する場合
        const rowFrom = findNodeRow(matrix, parentBranchId);
        const rowTo = findNodeRow(
          matrix,
          parentToConfluenceMap[parentBranchId],
        );
        // targetRowより前かつTargetColに値が存在するかチェック
        for (let row = rowFrom; row < rowTo; row++) {
          const cellValue = newMatrix[row][targetCol + 1];
          if (
            cellValue !== undefined &&
            cellValue !== '' &&
            !(removeNodeIds && removeNodeIds.includes(cellValue))
          ) {
            hasValueInTargetCol = true;
            break;
          }
        }
        // 値が存在しない場合のみshiftLeftNonEmptyCellsを実行
        if (!hasValueInTargetCol) {
          for (let row = rowFrom; row <= rowTo; row++) {
            const nonEmptyCount = countNonEmptyCells(newMatrix[row], targetCol);
            if (nonEmptyCount > 0) {
              newMatrix[row] = shiftLeftNonEmptyCells(
                newMatrix[row],
                targetCol,
              );
            }
          }
        }
      }
      newMatrix = setValue(newMatrix, targetRow + 1, targetCol, addOthers[0]);
      if (newMatrix[targetRow + 2][targetCol] === removeConfluence[0]) {
        newMatrix = setValue(newMatrix, targetRow + 2, targetCol, '');
      }
      // 列削除
      removeColumnIfEmpty(
        newMatrix,
        removeNodeIds,
        targetCol + 1,
        minRow,
        maxRow,
      );
      // 行追加
      ignoreVals.length = 0;
      ignoreVals.push(endNodeId);
      addRowIfEmpty(newMatrix, targetRow + 1, ignoreVals);
      break;
    }

    case 'branchAdd': {
      // targetRow+1の行の値がセットされている最終列を取得
      const nextRow = newMatrix[targetRow + 1] || [];
      let lastCol = -1;
      for (let col = 0; col < nextRow.length; col++) {
        if (nextRow[col] !== undefined && nextRow[col] !== '') {
          lastCol = col;
        }
      }
      // targetColを「最終列」に修正
      const targetLastCol = lastCol;
      // 列追加
      const addColCnt = addOthers.length - 1;
      addColumnIfEmpty(
        newMatrix,
        addColCnt,
        targetRow,
        targetLastCol + 1,
        minRow,
        maxRow,
        {
          parentBranchId,
          branchList,
          parentToConfluenceMap,
          targetNodeId: targetNode.id,
          parentBranchList,
        },
      );
      // 値セット
      addOthers.forEach((id, idx) => {
        newMatrix = setValueWithShift(
          newMatrix,
          targetRow + 1,
          targetLastCol + 1 + idx,
          id,
        );
      });
      break;
    }

    case 'branchRemove': {
      // 削除対象列を取得（降順ソート）
      const removeCols = getRemoveCols(newMatrix, removeNodeIds);
      // 対象行範囲を決定
      const [rowFrom, rowTo] = getRowRangeForParentBranch(
        newMatrix,
        targetNode,
        parentBranchId,
        parentToConfluenceMap,
        minRow,
        maxRow,
      );
      // 値セット(左にずらす)
      for (let rowIdx = rowFrom; rowIdx <= rowTo; rowIdx++) {
        shiftAndClearRowForRemove(newMatrix, rowIdx, removeCols, removeNodeIds);
      }
      // 列削除
      removeCols.forEach((col) => {
        removeColumnIfEmpty(newMatrix, removeNodeIds, col, minRow, maxRow);
      });
      break;
    }

    case 'templateAdd': {
      if (!templateMatrix) break;

      // 1. 行追加
      ignoreVals.length = 0;
      for (let i = targetRow; i < newMatrix.length; i++) {
        // targetRow以降の同一列の値はNULL扱いとする
        ignoreVals.push(newMatrix[i][targetCol]);
      }
      ignoreVals.push(endNodeId);

      // 追加行のインデックスを保持
      const addedRowIndexes = [];

      // テンプレート直後のaddPart行
      if (addOthers.length > 0) {
        const addRowIdx = targetRow + templateMatrix.length;
        addRowIfEmpty(newMatrix, addRowIdx, ignoreVals);
        addedRowIndexes.push(addRowIdx);
      }
      // テンプレート行数分を追加
      for (let i = 0; i < templateMatrix.length; i++) {
        const addRowIdx = targetRow + i;
        addRowIfEmpty(newMatrix, addRowIdx, ignoreVals);
        addedRowIndexes.push(addRowIdx);
      }

      // 2. 列追加（templateMatrixの最大列数分）
      const templateMaxCol = Math.max(
        ...templateMatrix.map((row) => row.length),
      );
      const matrixMaxCol = Math.max(...newMatrix.map((row) => row.length));
      // targetCol以降にtemplateMaxCol分の列があるかチェック
      let needAddCol = false;
      for (let i = targetRow; i < targetRow + templateMatrix.length; i++) {
        const row = newMatrix[i] || [];
        if (row.length < targetCol + templateMaxCol) {
          // 列不足
          needAddCol = true;
          break;
        }
      }
      if (needAddCol) {
        const addColCnt = Math.max(
          0,
          targetCol + templateMaxCol - matrixMaxCol,
        );
        addColumnIfEmpty(
          newMatrix,
          addColCnt,
          targetRow,
          templateMaxCol + 1,
          minRow,
          maxRow,
          {
            parentBranchId,
            branchList,
            parentToConfluenceMap,
            targetNodeId: targetNode.id,
            parentBranchList,
          },
        );
      }
      if (
        branchList[parentBranchId] &&
        branchList[parentBranchId].includes(state.checkedAddPartId)
      ) {
        const addColCnt = templateMaxCol - 1;
        addColumn(newMatrix, addColCnt, targetRow, targetCol + 1, {
          parentBranchId,
          branchList,
          parentToConfluenceMap,
          targetNodeId: targetNode.id,
          parentBranchList,
        });
      } else {
        const addColCnt = Math.max(
          0,
          targetCol + templateMaxCol - matrixMaxCol,
        );
        addColumnIfEmpty(
          newMatrix,
          addColCnt,
          targetRow,
          templateMaxCol + 1,
          minRow,
          maxRow,
          {
            parentBranchId,
            branchList,
            parentToConfluenceMap,
            targetNodeId: targetNode.id,
            parentBranchList,
          },
        );
      }
      // テンプレート行削除
      if (Array.isArray(removeNodeIds) && removeNodeIds.length > 0) {
        // 追加行以外の行インデックスを取得
        const addedRowSet = new Set(addedRowIndexes);
        // 後ろから削除しないとインデックスがずれるので逆順で
        for (let i = newMatrix.length - 1; i >= 0; i--) {
          if (!addedRowSet.has(i)) {
            const row = newMatrix[i];
            if (row && row.some((cell) => removeNodeIds.includes(cell))) {
              newMatrix.splice(i, 1);
            }
          }
        }
      }

      // 3. 値セット
      for (let i = 0; i < templateMatrix.length; i++) {
        for (let j = 0; j < templateMatrix[i].length; j++) {
          if (
            templateMatrix[i][j] !== undefined &&
            templateMatrix[i][j] !== null
          ) {
            newMatrix = setValue(
              newMatrix,
              targetRow + i,
              targetCol + j,
              templateMatrix[i][j],
            );
          }
        }
      }
      if (addOthers.length > 0) {
        newMatrix = setValue(
          newMatrix,
          targetRow + templateMatrix.length,
          targetCol,
          addOthers[0],
        );
      }

      break;
    }

    default:
      // 何もしない
      break;
  }

  // 末尾の不要列削除
  removeEmptyColumn(newMatrix);
  // 末尾の不要行削除
  removeEmptyRow(newMatrix);

  return newMatrix;
}

/**
 * 2次元配列に変換
 * @param {Cell[]} nodes - ノードの配列
 * @returns {Matrix} 2次元配列形式のノードマトリックス
 */
// const createNodesMatrix_new = (
//   nodes: Cell[]
// ): (string | null)[][] => {
//   const nodesMatrix: (string | null)[][] = [];
//   const usedIds = new Set<string>();

//   const xTolerance: number = 0;
//   const yTolerance: number = 2;

//   // 1-1. 各ノードY位置でグルーピング（Y位置の昇順）
//   const yGroupCenters: number[] = [];
//   const yGroups: { [y: number]: Cell[] } = {};
//   nodes.forEach(node => {
//     const pos = node.getProp<PositionOption>('position');
//     if (pos.y === 0) return;
//     let groupY = yGroupCenters.find(yc => Math.abs(yc - pos.y) <= yTolerance);
//     if (groupY === undefined) {
//       yGroupCenters.push(pos.y);
//       groupY = pos.y;
//     }
//     if (!yGroups[groupY]) yGroups[groupY] = [];
//     yGroups[groupY].push(node);
//   });

//   // 1-2. 各ノードx位置でグルーピング（x位置の昇順）
//   const xGroupCentersByY: { [y: number]: number[] } = {};
//   Object.entries(yGroups).forEach(([y, group]) => {
//     const xCenters: number[] = [];
//     group.forEach(node => {
//       const pos = node.getProp<PositionOption>('position');
//       const size = node.getProp<SizeOption>('size');
//       const centerX = pos.x + (size?.width ?? 0) / 2;
//       const found = xCenters.find(x => Math.abs(x - centerX) <= xTolerance);
//       if (found === undefined) {
//         xCenters.push(centerX);
//       }
//     });
//     xCenters.sort((a, b) => a - b);
//     xGroupCentersByY[y] = xCenters;
//   });

//   // 1-3. 各ノード数に応じ、2次元配列の枠を作る（値はNULL）
//   const sortedY = yGroupCenters.slice().sort((a, b) => a - b);
//   sortedY.forEach((yVal, yIdx) => {
//     const xCenters = xGroupCentersByY[yVal];
//     nodesMatrix[yIdx] = new Array(xCenters.length).fill(null);
//   });

//   // 2-1. 各ノードを描画位置のy位置昇順、x位置昇順でソート
//   sortedY.forEach((yVal, yIdx) => {
//     const group = yGroups[yVal];
//     const xCenters = xGroupCentersByY[yVal];
//     const sortedNodes = group.slice().sort((a, b) => {
//       const posA = a.getProp<PositionOption>('position');
//       const sizeA = a.getProp<SizeOption>('size');
//       const centerXA = posA.x + (sizeA?.width ?? 0) / 2;
//       const posB = b.getProp<PositionOption>('position');
//       const sizeB = b.getProp<SizeOption>('size');
//       const centerXB = posB.x + (sizeB?.width ?? 0) / 2;
//       return centerXA - centerXB;
//     });

//     // 2-2. 2次元配列に値をセットする
//     sortedNodes.forEach(node => {
//       if (usedIds.has(node.id)) return;
//       const pos = node.getProp<PositionOption>('position');
//       const size = node.getProp<SizeOption>('size');
//       const centerX = pos.x + (size?.width ?? 0) / 2;
//       let xIdx = -1;
//       for (let i = 0; i < xCenters.length; i++) {
//         if (Math.abs(xCenters[i] - centerX) <= xTolerance) {
//           xIdx = i;
//           break;
//         }
//       }
//       if (xIdx === -1) xIdx = 0;
//       nodesMatrix[yIdx][xIdx] = node.id;
//       usedIds.add(node.id);
//     });
//   });

//   return nodesMatrix;
// };

// /**
//  * 2次元配列に変換
//  * @param {Cell[]} nodes - ノードの配列
//  * @returns {(string | null)[][]} 2次元配列形式のノードマトリックス
//  */
// const createNodesMatrix_org = (nodes: Cell[]): (string | null)[][] => {
//   const nodesMatrix: (string | null)[][] = [];
//   // Y座標ごとにノードをグループ化
//   const yGroups: { [y: number]: Cell[] } = {};
//   nodes.forEach(node => {
//     const pos = node.getProp<PositionOption>('position');
//     if (pos.y === 0) return; // y=0はグルーピング対象外
//     if (!yGroups[pos.y]) yGroups[pos.y] = [];
//     yGroups[pos.y].push(node);
//   });

//   // Y座標の昇順で処理
//   const sortedY = Object.keys(yGroups)
//     .map(Number)
//     .sort((a, b) => a - b);

//   // パーツの中央X座標でグルーピング
//   const centerXSet = new Set<number>();
//   nodes.forEach(node => {
//     const pos = node.getProp<PositionOption>('position');
//     if (pos.y === 0) return; // y=0は除外
//     const size = node.getProp<SizeOption>('size');
//     const centerX = pos.x + (size?.width ?? 0) / 2;
//     centerXSet.add(centerX);
//   });

//   const allCenterX = Array.from(centerXSet).sort((a, b) => a - b);
//   // 中央X座標→indexのマップを作成
//   const xIndexMap = new Map<number, number>();
//   allCenterX.forEach((x, idx) => {
//     xIndexMap.set(x, idx);
//   });
//   const maxLen = allCenterX.length;

//   // 各行を中央X座標のindexでセット（全行同じ長さ）
//   sortedY.forEach((yVal, yIdx) => {
//     nodesMatrix[yIdx] = Array(maxLen).fill(null);
//     yGroups[yVal].forEach((node) => {
//       const pos = node.getProp<PositionOption>('position');
//       const size = node.getProp<SizeOption>('size');
//       const centerX = pos.x + (size?.width ?? 0) / 2;
//       const xIdx = xIndexMap.get(centerX) ?? 0;
//       // --- 対策案1: addpartノードもIDで記録 ---
//       nodesMatrix[yIdx][xIdx] = node.id;
//     });
//   });

//   return nodesMatrix;
// };
// const mergeNodesMatrix_org = (
//   beforeMatrix: (string | null)[][],
//   afterMatrix: (string | null)[][]
// ): (string | null)[][] => {
//   // 追加されたパーツを検出
//   const beforeSet = new Set(beforeMatrix.flat().filter(Boolean));
//   const afterSet = new Set(afterMatrix.flat().filter(Boolean));
//   const added = Array.from(afterSet).filter(id => !beforeSet.has(id));
//   if (added.length === 0) return afterMatrix;

//   console.log('追加されたパーツ:', added);

//   // 追加パーツの座標を取得
//   let addX = -1, addY = -1;
//   outer: for (let y = 0; y < afterMatrix.length; y++) {
//     for (let x = 0; x < afterMatrix[y].length; x++) {
//       if (added.includes(afterMatrix[y][x])) {
//         addX = x;
//         addY = y;
//         break outer;
//       }
//     }
//   }
//   if (addX === -1 || addY === -1) return afterMatrix;

//   // ずらす前のデータを一度クリアした新しい配列を作成
//   const merged: (string | null)[][] = afterMatrix.map(row => [...row]);

//   // ずらす前のデータが残らないように、beforeMatrixの値を一旦全て削除
//   for (let y = 0; y < beforeMatrix.length; y++) {
//     for (let x = 0; x < beforeMatrix[y].length; x++) {
//       const val = beforeMatrix[y][x];
//       if (!val) continue;
//       // afterMatrix上に同じ値があればnullにする
//       for (let yy = 0; yy < merged.length; yy++) {
//         for (let xx = 0; xx < merged[yy].length; xx++) {
//           if (merged[yy][xx] === val) {
//             merged[yy][xx] = null;
//           }
//         }
//       }
//     }
//   }

//   // 既存パーツの座標を取得し、追加パーツの位置以降のx/yをずらして再配置
//   for (let y = beforeMatrix.length - 1; y >= 0; y--) {
//     for (let x = beforeMatrix[y].length - 1; x >= 0; x--) {
//       const val = beforeMatrix[y][x];
//       if (!val) continue;
//       // 追加パーツの位置以降ならずらす
//       let newY = y, newX = x;
//       if (y >= addY) newY++;
//       if (x >= addX) newX++;
//       // 配列サイズを拡張
//       while (merged.length <= newY) merged.push(Array(merged[0].length).fill(null));
//       while (merged[newY].length <= newX) merged[newY].push(null);
//       // 追加パーツと重複しない場合のみセット
//       if (!added.includes(val)) {
//         merged[newY][newX] = val;
//       }
//     }
//   }
//   merged[addY][addX] = afterMatrix[addY][addX];
//   return merged;
// };
/**
 * 分岐親パーツ（parentNode）から収束点（confluenceNodeId）までのパーツ一覧を取得する
 * @param {Cell} parentNode - 分岐親パーツ
 * @returns {Cell[]} 収束点までのパーツ一覧（親分岐パーツ自身を含む）
 */
// const getPartsListToConfluence = (parentNode: Cell): Cell[] => {
//   const result: Cell[] = [];
//   const visited = new Set<string>();
//   const confluenceNodeId = parentNode.getProp('confluenceNodeId');
//   if (!confluenceNodeId) return result;

//   // 深さ優先探索で収束点までたどる
//   const dfs = (node: Cell) => {
//     if (!node || visited.has(node.id)) return;
//     visited.add(node.id);
//     result.push(node);
//     if (node.id === confluenceNodeId) return;
//     const outgoings: Cell[] = state.sopGraph!.getNeighbors(node, { outgoing: true });
//     outgoings.forEach(next => {
//       if (!visited.has(next.id)) dfs(next);
//     });
//   };

//   dfs(parentNode);
//   return result;
// };

/**
 * ノードマトリックスをマージし、指定ノードの分岐行の既存分岐以降に新規追加パーツを配置する
 * @param beforeMatrix - 変更前のノード2次元配列
 * @param targetNode - 分岐元ノード（親ノード）
 * @param newAddPartIds - 新規追加されたaddPartノードID配列
 * @returns マージ後のノード2次元配列
 */
// const addMergeNodesMatrixSystemOrButton = (
//   beforeMatrix: Matrix,
//   targetNode: Node,
//   newAddPartIds: string[]
// ): Matrix => {
//   // 1. targetNodeに属する分岐行を検索（targetNodeのoutgoingにaddPartがある行）
//   const outgoingAddPartIds = state.sopGraph!
//     .getNeighbors(targetNode, { outgoing: true })
//     .filter(n => n.getProp<string>('sopPartsCD') === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_NAME)
//     .map(n => n.id);
//   let branchRowIdx = -1;
//   for (let y = 0; y < beforeMatrix.length; y++) {
//     // 行内にoutgoingAddPartIdsのいずれかが含まれていれば分岐行とみなす
//     if (beforeMatrix[y].some(id => id && outgoingAddPartIds.includes(id))) {
//       branchRowIdx = y;
//       break;
//     }
//   }
//   if (branchRowIdx === -1) return beforeMatrix; // 分岐行が見つからなければそのまま返す

//   // 2. 既存分岐以降の列インデックスを取得
//   let insertColIdx = beforeMatrix[branchRowIdx].length;
//   for (let x = beforeMatrix[branchRowIdx].length - 1; x >= 0; x--) {
//     if (beforeMatrix[branchRowIdx][x]) {
//       insertColIdx = x + 1;
//       break;
//     }
//   }

//   // 3. 新しい行を作成し、既存行をコピー
//   const merged = beforeMatrix.map(row => [...row]);

//   // 4. すべての行で追加パーツ分だけ列数を拡張
//   const needLen = Math.max(...merged.map(row => row.length), insertColIdx) + newAddPartIds.length;
//   for (let y = 0; y < merged.length; y++) {
//     while (merged[y].length < needLen) {
//       merged[y].push('');
//     }
//   }

//   // 5. 追加位置以降の全行のパーツを右にずらす
//   for (let y = 0; y < merged.length; y++) {
//     for (let x = merged[y].length - 1 - newAddPartIds.length; x >= insertColIdx; x--) {
//       if (merged[y][x]) {
//         merged[y][x + newAddPartIds.length] = merged[y][x];
//         merged[y][x] = '';
//       }
//     }
//   }

//   // 6. 既存分岐以降の列にnewAddPartIdsを順に配置
//   newAddPartIds.forEach((id, idx) => {
//     merged[branchRowIdx][insertColIdx + idx] = id;
//   });

//   return merged;
// };
/**
 * ノードマトリックスをマージし、指定ノードの分岐行の既存分岐以降に新規追加パーツを配置する
 * @param beforeMatrix - 変更前のノード2次元配列
 * @param targetNode - 分岐元ノード（親ノード）
 * @param newAddPartIds - 新規追加されたaddPartノードID配列
 * @param branchList - 分岐パーツ毎の所属パーツ情報 [{ branchNodeId: string, partIds: string[] }]
 * @returns マージ後のノード2次元配列
 */
// const addMergeNodesMatrix = (
//   beforeMatrix: Matrix,
//   targetNode: Node,
//   newAddPartIds: string[],
//   branchList: { branchNodeId: string; partIds: string[] }[]
// ): Matrix => {
//   // 1. 親ノードの列インデックスを取得
//   let parentRowIdx = -1;
//   let parentColIdx = -1;
//   for (let y = 0; y < beforeMatrix.length; y++) {
//     for (let x = 0; x < beforeMatrix[y].length; x++) {
//       if (beforeMatrix[y][x] === targetNode.id) {
//         parentRowIdx = y;
//         parentColIdx = x;
//         break;
//       }
//     }
//     if (parentRowIdx !== -1) break;
//   }
//   if (parentRowIdx === -1 || parentColIdx === -1) return beforeMatrix;

//   // 2. 追加先の行インデックスを決定(targetNodeの次の行)
//   const insertRowIdx = parentRowIdx + 1;
//   const insertColIdx = parentColIdx;

//   // 3. AddPartIdsを収束ノードとaddPartノードで分類
//   const addPartIds: string[] = [];
//   let confluenceNodeId: string | undefined;
//   newAddPartIds.forEach(id => {
//     if (/^\d{4}$/.test(id)) {
//       confluenceNodeId = id;
//     } else {
//       addPartIds.push(id);
//     }
//   });

//   // 4. ずらす対象のxIndexを決定
//   // 追加するaddPartのうち、最後のaddPartのxIndexを基準にする
//   const shiftX = parentColIdx + addPartIds.length - 1;
// console.log('追加パーツのxIndex:', shiftX);

// // 5. すべての行で追加パーツ分-1だけ列数を拡張
//   const addPartCount = addPartIds.length -1 ;
//   const needLen = Math.max(...beforeMatrix.map(row => row.length), insertColIdx) + addPartCount;
//   const merged = beforeMatrix.map(row => {
//     const newRow = [...row];
//     while (newRow.length < needLen) newRow.push('');
//     return newRow;
//   });

//   // 5. 「shiftX」以降の列だけ右にずらす
//   // 1. 対象パーツが所属する分岐親パーツをbranchListから特定
//   let parentBranchNodeId: string | undefined;
//   const foundBranch = branchList.find(branch => branch.partIds.includes(targetNode.id));
//   if (foundBranch) {
//     parentBranchNodeId = foundBranch.branchNodeId;
//   }
//   // 2. 分岐親パーツが特定できた場合、その分岐親パーツに所属するパーツのみ右にずらす
//   if (parentBranchNodeId) {
//     // 所属パーツIDリスト
//     const belongPartIds = branchList.find(b => b.branchNodeId === parentBranchNodeId)?.partIds ?? [];
//     // 所属パーツIDが存在する行番号をすべて取得
//     let minY = merged.length;
//     let maxY = -1;
//     for (let y = 0; y < merged.length; y++) {
//       for (let x = 0; x < merged[y].length; x++) {
//         if (merged[y][x] && belongPartIds.includes(merged[y][x]!)) {
//           if (y < minY) minY = y;
//           if (y > maxY) maxY = y;
//         }
//       }
//     }
//     if (maxY !== -1) maxY += 1;
//     console.log('所属パーツ行範囲:', minY, maxY, belongPartIds);

//     // 追加対象パーツの一番右の列
//     const rightMostAddCol = shiftX + addPartIds.length - 1;
//     // 右にずらす必要がある最大列（全行で最大の列数を取得）
//     let maxCol = rightMostAddCol;
//     for (let y = minY; y <= maxY; y++) {
//       if (merged[y] && merged[y].length - 1 > maxCol) {
//         maxCol = merged[y].length - 1;
//       }
//     }

//     // 必要なら全行の列数を拡張
//     const expandCols = addPartIds.length - 1;
//     for (let y = 0; y < merged.length; y++) {
//       if (merged[y].length <= maxCol + expandCols) {
//         // nullで埋めて拡張
//         const oldLen = merged[y].length;
//         merged[y].length = maxCol + expandCols + 1;
//         for (let i = oldLen; i < merged[y].length; i++) {
//           merged[y][i] = '';
//         }
//       }
//     }

//     // 各行ごとに、shiftX以降のセルを右にずらす
//     for (let y = minY; y <= maxY; y++) {
//       if (!merged[y]) continue;
//       // 右端から左に向かってずらす
//       for (let x = maxCol; x >= shiftX; x--) {
//         merged[y][x + expandCols] = merged[y][x];
//         merged[y][x] = '';
//       }
//     }
//   } else {
//     // 所属分岐が特定できない場合は従来通り
//     const targetRowIdx = merged.findIndex(row => row.includes(targetNode.id));
//     const rowsToShift: number[] = [];
//     for (let y = 0; y < targetRowIdx; y++) {
//       if (merged[y][shiftX]) {
//         rowsToShift.push(y);
//       }
//     }
//     if (rowsToShift.length > 0) {
//       rowsToShift.forEach(y => {
//         for (let x = merged[y].length - 1 - addPartIds.length; x >= shiftX; x--) {
//           if (merged[y][x]) {
//             merged[y][x + addPartIds.length] = merged[y][x];
//             merged[y][x] = '';
//           }
//         }
//       });
//     }
//   }

//   // 6. 新しい行を作成し、insertColIdxからaddPartノードを左詰めで配置
//   const addPartRow: (string)[] = Array(merged[0].length).fill(null);
//   addPartIds.forEach((id, idx) => {
//     addPartRow[insertColIdx + idx] = id;
//   });

//   // 7. 収束ノード行を作成（xIndex=0に配置、他はnull）
//   const confluenceRow: (string)[] = Array(merged[0].length).fill(null);
//   if (confluenceNodeId) {
//     confluenceRow[0] = confluenceNodeId;
//   }

//   // 8. 既存行を下にずらさず、そのまま挿入
//   const result = [
//     ...merged.slice(0, insertRowIdx),
//     addPartRow,
//     confluenceRow,
//     ...merged.slice(insertRowIdx)
//   ];

//   // 9. 配列サイズを揃える（全行同じ長さにする）
//   const maxLen = Math.max(...result.map(row => row.length));
//   for (let y = 0; y < result.length; y++) {
//     while (result[y].length < maxLen) {
//       result[y].push('');
//     }
//   }

//   return result;
// };
/**
 * 指定されたパーツを削除時、行ごと削除する
 * @param beforeMatrix - 変更前のノード2次元配列
 * @param removeAddPartIds - 削除対象パーツID配列
 * @returns 行ごと削除後のノード2次元配列
 */
// const removeRowsByPartIds = (
//   beforeMatrix: Matrix,
//   removeAddPartIds: string[]
// ): Matrix => {
//   if (!removeAddPartIds || removeAddPartIds.length === 0) return beforeMatrix;
//   // 削除対象IDが含まれる行を除外
//   return beforeMatrix.filter(
//     row => !row.some(id => removeAddPartIds.includes(id ?? ''))
//   );
// };

/**
 * 2次元配列の最大xIndexが全行nullの場合、そのxIndexを縮小（削除）する
 * @param matrix Matrix
 * @returns 縮小後の2次元配列
 */
// function shrinkNullMaxXIndex(matrix: Matrix): Matrix {
//   if (matrix.length === 0) return matrix;
//   // 最大xIndexを取得
//   const maxX = Math.max(...matrix.map(row => row.length - 1));
//   // 最大xIndexが全行nullか判定
//   const isAllNull = matrix.every(row => row[maxX] === '');
//   if (!isAllNull) return matrix;
//   // 最大xIndexを全行から削除
//   return matrix.map(row => row.slice(0, maxX));
// }
/**
 * ノードマトリックスから指定されたremovedPartIdsの位置以降を左に詰めて列数を縮小する
 * @param beforeMatrix - 変更前のノード2次元配列
 * @param removedPartIds - 削除されたaddPartノードID配列
 * @returns マージ後のノード2次元配列
 */
// const removeMergeNodesMatrixSystemOrButton = (
//   beforeMatrix: Matrix,
//   removedPartIds: string[],
//   targetNode: Node,
//   branchList: { branchNodeId: string; partIds: string[] }[]
// ): Matrix => {
//   if (removedPartIds.length === 0) return beforeMatrix;
//   // 1. 削除対象IDのxIndexを取得（複数ある場合は昇順で処理）
//   const removedIndexes: { x: number; y: number }[] = [];
//   beforeMatrix.forEach((row, y) => {
//     row.forEach((id, x) => {
//       if (removedPartIds.includes(id as string)) {
//         removedIndexes.push({ x, y });
//       }
//     });
//   });
//   removedIndexes.sort((a, b) => a.x - b.x);

//   const removedXIndexes: number[] = [];
//   beforeMatrix.forEach(row => {
//     removedPartIds.forEach(rid => {
//       const idx = row.findIndex(id => id === rid);
//       if (idx !== -1 && !removedXIndexes.includes(idx)) {
//         removedXIndexes.push(idx);
//       }
//     });
//   });
//   removedXIndexes.sort((a, b) => a - b);

//   // 対象パーツが所属する分岐親パーツをbranchListから特定
//   let parentBranchNodeId: string | undefined;
//   const foundBranch = branchList.find(branch => branch.partIds.includes(targetNode.id));
//   if (foundBranch) {
//     parentBranchNodeId = foundBranch.branchNodeId;
//   }

//   let merged: Matrix = beforeMatrix.map(row => [...row]);

//   if (parentBranchNodeId) {
//     // 分岐親パーツが特定できた場合、branchList内のパーツのみxIndexを縮小
//     const belongPartIds = branchList.find(b => b.branchNodeId === parentBranchNodeId)?.partIds ?? [];

//     // belongPartIdsが存在するxIndexのみを抽出
//     const belongXIndexes: number[] = [];
//     merged[0].forEach((_, x) => {
//       for (let y = 0; y < merged.length; y++) {
//         if (belongPartIds.includes(merged[y][x] as string)) {
//           belongXIndexes.push(x);
//           break;
//         }
//       }
//     });
//     // belongXIndexesのうち、削除対象xIndexのみ縮小
//     const shrinkXIndexes = removedXIndexes.filter(x => belongXIndexes.includes(x));
//     shrinkXIndexes.sort((a, b) => b - a);

//     // 削除対象パーツの行範囲（最小y～最大y）を特定
//     const removedYIndexes = merged
//       .map((row, y) =>
//         row.some((id, x) =>
//           shrinkXIndexes.includes(x) && removedPartIds.includes(id as string)
//         ) ? y : -1
//       )
//       .filter(y => y !== -1);
//     const minY = Math.min(...removedYIndexes);
//     const maxY = Math.max(...removedYIndexes);

//     shrinkXIndexes.forEach(removeIdx => {
//       for (let y = minY; y <= maxY; y++) {
//         merged[y][removeIdx] = '';
//       }
//     });
//     let maxX = 0;
//     merged.forEach(row => {
//       for (let x = row.length - 1; x >= 0; x--) {
//         if (row[x] !== '') {
//           if (x > maxX) maxX = x;
//           break;
//         }
//       }
//     });
//     merged = merged.map(row => row.slice(0, maxX + 1));
//   } else {
//     // --- 他分岐内に所属していない場合: yIndexを縮小 ---
//     // 削除対象のyIndexをユニークに集約
//     const removedYIndexes = Array.from(new Set(removedIndexes.map(idx => idx.y)));
//     if (removedYIndexes.length === 0) return merged;
//     const maxRemovedY = Math.max(...removedYIndexes);

//     // 最大yIndex+1以降の行を上に詰める
//     const shrinkCount = removedYIndexes.length;
//     for (let y = maxRemovedY + 1; y < merged.length; y++) {
//       merged[y - shrinkCount] = merged[y];
//     }
//     // 下側の不要な行を削除
//     merged.length = merged.length - shrinkCount;
//   }

//   return merged;
// };
/**
 * ノードマトリックスから指定されたremovedPartIdsの位置以降を左に詰めて列数を縮小する
 * @param beforeMatrix - 変更前のノード2次元配列
 * @param removedPartIds - 削除されたaddPartノードID配列
 * @returns マージ後のノード2次元配列
 */
// const removeMergeNodesMatrix = (
//   beforeMatrix: Matrix,
//   removedPartIds: string[],
//   targetNode: Node,
//   branchList: { branchNodeId: string; partIds: string[] }[]
// ): Matrix => {
//   if (removedPartIds.length === 0) return beforeMatrix;

// console.log('削除対象パーツ:', removedPartIds);
//   // 1. 削除対象IDのxIndex/yIndexを取得
//   const removedIndexes: { x: number; y: number }[] = [];
//   beforeMatrix.forEach((row, y) => {
//     row.forEach((id, x) => {
//       if (removedPartIds.includes(id as string)) {
//         removedIndexes.push({ x, y });
//       }
//     });
//   });
//   removedIndexes.sort((a, b) => a.x - b.x);

//   const removedXIndexes: number[] = [];
//   beforeMatrix.forEach(row => {
//     removedPartIds.forEach(rid => {
//       const idx = row.findIndex(id => id === rid);
//       if (idx !== -1 && !removedXIndexes.includes(idx)) {
//         removedXIndexes.push(idx);
//       }
//     });
//   });
//   removedXIndexes.sort((a, b) => a - b);

//   // 2. 分岐親パーツを特定（自ノードは対象外）
//   let parentBranchNodeId: string | undefined;
//   const foundBranch = branchList.find(
//     branch =>
//       branch.partIds.includes(targetNode.id) &&
//       branch.branchNodeId !== targetNode.id
//   );
//   if (foundBranch) {
//     parentBranchNodeId = foundBranch.branchNodeId;
//   }

//   let merged: Matrix = beforeMatrix.map(row => [...row]);

//   // 2. 分岐親パーツが特定できた場合、xIndexを更新
//   if (parentBranchNodeId) {
//     // 分岐親パーツが特定できた場合、belongPartIdsを取得
//     const belongPartIds = branchList.find(b => b.branchNodeId === parentBranchNodeId)?.partIds ?? [];

//     // 行範囲（親分岐パーツに所属するpartListが存在する行のみ）を特定
//     const belongYIndexes = merged
//       .map((row, y) => row.some(id => belongPartIds.includes(id as string)) ? y : -1)
//       .filter(y => y !== -1);

//     if (belongYIndexes.length > 0) {
//       const minY = Math.min(...belongYIndexes);
//       const maxY = Math.max(...belongYIndexes);
//       // 行範囲内で、全ての行がnullの列を検出
//       const colLen = merged[0].length;
//       for (let x = 0; x < colLen; x++) {
//         let isAllNull = true;
//         for (let y = minY; y <= maxY; y++) {
//           if (merged[y][x] !== '' && !removedPartIds.includes(merged[y][x])) {
//             isAllNull = false;
//             break;
//           }
//         }
//         if (isAllNull) {
//           // その列より右側に値があれば、xIndex-1した列に値をセットし、元の列をnullに
//           for (let y = minY; y <= maxY; y++) {
//             for (let xx = x + 1; xx < merged[y].length; xx++) {
//               if (merged[y][xx] !== '') {
//                 merged[y][xx - 1] = merged[y][xx];
//                 merged[y][xx] = '';
//               }
//             }
//           }
//         }
//       }
//     }
//   }
//   // --- 収束ノードを削除したため、yIndexを-1縮小 ---
//   const removedYIndexes = Array.from(new Set(removedIndexes.map(idx => idx.y)));
//   if (removedYIndexes.length === 0) return merged;
//   const maxRemovedY = Math.max(...removedYIndexes);

//   const shrinkCount = removedYIndexes.length;
//   for (let y = maxRemovedY + 1; y < merged.length; y++) {
//     merged[y - shrinkCount] = merged[y];
//   }
//   merged.length -= shrinkCount;

//   merged = shrinkNullMaxXIndex(merged);

//   return merged;
// };
/**
 * ノードマトリックスのyIndex（行）を拡張し、新規addPartノードと収束ノードを追加する
 * 既存パーツのxIndexはずらさず、addPartRow/収束ノード行をそのまま挿入する
 * addPartノードはtargetNodeのyIndex+1、収束ノードはyIndex+2に配置
 * newAddPartIdsのうち4桁IDを収束ノード、それ以外をaddPartIdとする
 * @param beforeMatrix 変更前のノード2次元配列
 * @param targetNode 親ノード
 * @param newAddPartIds 新規追加されたaddPartノードID配列
 * @returns マージ後のノード2次元配列
 */
// const addMergeNodesMatrixY = (
//   beforeMatrix: Matrix,
//   targetNode: Node,
//   newAddPartIds: string[]
// ): Matrix => {
//   // 1. 追加先のyIndexを決定（targetNodeの行の直下）
//   let parentRowIdx = -1;
//   for (let y = 0; y < beforeMatrix.length; y++) {
//     if (beforeMatrix[y].some(id => id === targetNode.id)) {
//       parentRowIdx = y;
//       break;
//     }
//   }
//   if (parentRowIdx === -1) {
//     parentRowIdx = beforeMatrix.length - 1;
//   }

//   // 2. newAddPartIdsを分類
//   const addPartIds: string[] = [];
//   let confluenceNodeId: string | undefined;
//   newAddPartIds.forEach(id => {
//     if (/^\d{4}$/.test(id)) {
//       confluenceNodeId = id;
//     } else {
//       addPartIds.push(id);
//     }
//   });

//   // 3. 新しい行を作成し、addPartノードを左詰めで配置
//   const rowLength = beforeMatrix[0]?.length ?? 1;
//   const addPartRow: (string)[] = Array(rowLength).fill(null);
//   addPartIds.forEach((id, idx) => {
//     if (idx < rowLength) addPartRow[idx] = id;
//   });

//   // 4. 収束ノード行を作成（xIndex=0に配置、他はnull）
//   const confluenceRow: (string)[] = Array(rowLength).fill(null);
//   if (confluenceNodeId) {
//     confluenceRow[0] = confluenceNodeId;
//   }

//   // 5. 既存行を下にずらさず、そのまま挿入
//   const merged = [
//     ...beforeMatrix.slice(0, parentRowIdx + 1),
//     addPartRow,
//     confluenceRow,
//     ...beforeMatrix.slice(parentRowIdx + 1)
//   ];

//   // 行数制限 ---
//   // newAddPartIdsの数（addPartIds+confluenceNodeId）以上の行が増えないようにする
//   // 追加した行数をカウント
//   const addedRows =  (addPartIds.length > 0 ? 1 : 0) + (confluenceNodeId ? 1 : 0);
//   // 追加行の直後に、追加行数を超える余分な行があれば削除
//   const maxRowsToAdd = addedRows;
//   const afterInsertIdx = parentRowIdx + maxRowsToAdd + 1;
//   // 追加行の直後からnewAddPartIds.length分だけ残し、それ以降の追加行は削除
//   while (
//     merged.length > beforeMatrix.length + maxRowsToAdd
//   ) {
//     merged.splice(afterInsertIdx, 1);
//   }

//   // 6. 配列サイズを揃える（全行同じ長さにする）
//   let maxLen = Math.max(
//     rowLength,
//     ...merged.map(row => row.length)
//   );
//   for (let y = 0; y < merged.length; y++) {
//     while (merged[y].length < maxLen) {
//       merged[y].push('');
//     }
//   }

//   // xIndex[1]に既存パーツが存在するか判定
//   let existAtX1 = false;
//   for (let y = 0; y < merged.length; y++) {
//     if (merged[y][1] !== '') {
//       existAtX1 = true;
//       break;
//     }
//   }

//   // 存在しない場合、全行のxIndex[1]以降を左に詰め、末尾を1つ削除
//   if (!existAtX1 && maxLen > 1) {
//     for (let y = 0; y < merged.length; y++) {
//       for (let x = 1; x < maxLen - 1; x++) {
//         merged[y][x] = merged[y][x + 1];
//       }
//       merged[y].pop(); // 末尾を削除
//     }
//     maxLen -= 1;
//   }

//   return merged;
// };

/**
 * ノードのY座標を計算する
 *
 * @param sopPartsCD - ノード種別CD
 * @param yIdx - ノードのY方向インデックス（行番号）
 * @param allNodesMatrix - ノードIDの2次元配列（行: Y方向, 列: X方向）
 * @param nodeAttrMap - ノードIDをキーとした属性情報マップ（位置・種別など）
 * @param sopPartConst - パーツ定数（高さ・マージン等）
 * @param options - オプション（join/branch/confluence/収束ノードのオフセット等）
 * @returns Y座標（px）
 *
 * 概要:
 * - join/branchはmarginYで等間隔、confluenceはmarginY＋オフセットで等間隔
 * - いずれも必ずyIdxを利用して計算
 * - confluenceのオフセットが指定されている場合、それ以降のjoin/branch/confluenceも高さ調整
 * - join/branch/confluence間の通常パーツは、前後のspecialノードの中央に配置
 * - スタート直後のjoinは特別な調整位置が可能
 * - エンドノードは直前のjoin/confluenceからのオフセット指定が可能
 */
function calcNodePosY(
  sopPartsCD: string,
  yIdx: number,
  allNodesMatrix: Matrix,
  nodeAttrMap: Record<string, unknown>,
  options?: {
    marginY?: number; // マージン（Y間隔）
    baseY?: number; // ベースY
  },
): number {
  const { baseY = 0, marginY = 0 } = options || {};

  // marginY:confluenceOffsetY の比率を維持して計算
  // 例: 104:96 → 96/104 = 0.923...
  const CONFLUENCE_OFFSET_RATIO = 96 / 104;
  const confluenceOffsetY = Math.round(marginY * CONFLUENCE_OFFSET_RATIO);

  // join/branch/confluence種別判定
  const isJoin = (cd: string) =>
    cd === SOP_PARTS_VARIABLES.PART_SPEC_JOIN_NAME ||
    cd === SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD;
  const isBranch = (cd: string) =>
    cd === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_NAME ||
    cd === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD;
  const isConfluence = (cd: string) =>
    cd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_NAME ||
    cd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD;

  // オフセット合計を計算
  const calcOffsetSum = (toYIdx: number) => {
    let offsetSum = 0;
    for (let i = 1; i <= toYIdx; i++) {
      const prevRow = allNodesMatrix[i - 1];
      if (prevRow) {
        for (let px = 0; px < prevRow.length; px++) {
          const prevId = prevRow[px];
          if (
            prevId &&
            nodeAttrMap &&
            nodeAttrMap[prevId] &&
            // @ts-expect-error sopPartsCD属性ある
            nodeAttrMap[prevId].sopPartsCD !== undefined &&
            // @ts-expect-error sopPartsCD属性ある
            isConfluence(nodeAttrMap[prevId].sopPartsCD) &&
            confluenceOffsetY
          ) {
            offsetSum += confluenceOffsetY;
            break;
          }
        }
      }
    }
    return offsetSum;
  };

  // --- 1. スタート ---
  if (
    sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_START_NAME ||
    sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_START_CD
  ) {
    return baseY;
  }

  // --- 2. join/branch ---
  if (isJoin(sopPartsCD) || isBranch(sopPartsCD)) {
    return baseY + yIdx * marginY + calcOffsetSum(yIdx);
  }

  // --- 3. confluence ---
  if (isConfluence(sopPartsCD)) {
    return baseY + yIdx * marginY + confluenceOffsetY + calcOffsetSum(yIdx);
  }

  // --- 4. End ---
  if (
    sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_END_NAME ||
    sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_END_CD
  ) {
    return baseY + yIdx * marginY + calcOffsetSum(yIdx);
  }

  // --- 5. 通常パーツ ---
  // TODO: 通常パーツも同じ比率で間隔を計算
  return baseY + yIdx * marginY + calcOffsetSum(yIdx) - 20;
  // let prevSpecialY: number | null = null;
  // let nextSpecialY: number | null = null;
  // // 前のspecialノードのY
  // for (let i = yIdx - 1; i >= 0 && prevSpecialY === null; i--) {
  //   const prevRow = allNodesMatrix[i];
  //   if (prevRow) {
  //     for (let px = 0; px < prevRow.length; px++) {
  //       const prevId = prevRow[px];
  //       if (
  //         prevId &&
  //         nodeAttrMap &&
  //         isSpecial(nodeAttrMap[prevId].sopPartsCD)
  //       ) {
  //         prevSpecialY = nodeAttrMap[prevId]?.position?.y ?? (baseY + i * marginY);
  //         break;
  //       }
  //     }
  //   }
  // }
  // // 次のspecialノードのY
  // for (let i = yIdx + 1; i < allNodesMatrix.length && nextSpecialY === null; i++) {
  //   const nextRow = allNodesMatrix[i];
  //   if (nextRow) {
  //     for (let nx = 0; nx < nextRow.length; nx++) {
  //       const nextId = nextRow[nx];
  //       if (
  //         nextId &&
  //         nodeAttrMap &&
  //         isSpecial(nodeAttrMap[nextId].sopPartsCD)
  //       ) {
  //         if (isConfluence(nodeAttrMap[nextId].sopPartsCD)) {
  //           nextSpecialY = nodeAttrMap[nextId]?.position?.y ?? (baseY + i * marginY + confluenceOffsetY);
  //         } else {
  //           nextSpecialY = nodeAttrMap[nextId]?.position?.y ?? (baseY + i * marginY);
  //         }
  //         break;
  //       }
  //     }
  //   }
  // }
  // if (prevSpecialY !== null && nextSpecialY !== null) {
  //   return (prevSpecialY + nextSpecialY) / 2 - partHeight / 2 + addPartHeight / 2;
  // }
  // return baseY + yIdx * marginY;
}
/**
 * ノード種別を判定する関数例
 */
function getEdgeNodeType(partsCd: string): string {
  if (!partsCd) return 'normal';

  if (checkPartCode(state.SOPSetData.partCds, partsCd)) return 'normal';
  if (partsCd.startsWith(SOP_PARTS_VARIABLES.PART_SPEC_START_CD))
    return 'normal';
  if (partsCd.startsWith(SOP_PARTS_VARIABLES.PART_SPEC_END_CD)) return 'normal';
  if (partsCd.startsWith(SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD))
    return SOP_PARTS_VARIABLES.PART_SPEC_JOIN_NAME;
  if (partsCd.startsWith(SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD))
    return SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_NAME;
  if (partsCd.startsWith(SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD))
    return SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_NAME;
  return partsCd;
}
type NodeAttr = Record<string, unknown>;

function getIndividualPara<T>(attr: unknown): T | undefined {
  if (attr && typeof attr === 'object' && 'individualPara' in attr) {
    return (attr as { individualPara?: unknown }).individualPara as T;
  }
  return undefined;
}

function getConditionProps(attr: unknown): SopConditionProps | undefined {
  if (attr && typeof attr === 'object' && 'conditionProps' in attr) {
    return (attr as { conditionProps?: unknown })
      .conditionProps as SopConditionProps;
  }
  return undefined;
}

function getDeviationBranchNodeId(obj: unknown): string | undefined {
  if (obj && typeof obj === 'object' && 'deviationBranchNodeId' in obj) {
    return (obj as { deviationBranchNodeId?: unknown })
      .deviationBranchNodeId as string;
  }
  return undefined;
}

function getBranchNodeId<T>(
  indivisdualPara: T,
  branchList: string[],
  targetId: string,
): string | undefined {
  if (!Array.isArray(branchList)) return undefined;
  const index = branchList.indexOf(targetId);
  if (index === -1) return undefined;
  const key = `branchNodeId${index + 1}` as keyof T;
  return indivisdualPara && (indivisdualPara[key] as string);
}
function getbranchNodeIdDefault(obj: unknown): string | undefined {
  if (obj && typeof obj === 'object' && 'branchNodeIdDefault' in obj) {
    return (obj as { branchNodeIdDefault?: unknown })
      .branchNodeIdDefault as string;
  }
  return undefined;
}
/**
 * ループ設定時の飛び先ノード取得
 */
function getLoopTargetNode(
  targetId: string,
  parentId: string,
  branchList: string[],
  nodeAttrMap: NodeAttr,
): { id: string } | null {
  const node = state.sopGraph?.getCellById(parentId);
  if (!node || !node.isNode() || !nodeAttrMap[parentId]) return null;

  const attr = nodeAttrMap[parentId];
  const sopPartsCD = (attr as { sopPartsCD?: string })?.sopPartsCD;

  switch (sopPartsCD) {
    case SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD: {
      const indivisdualPara = getIndividualPara<PartButtonBranchProps>(attr);
      const branchNodeId =
        indivisdualPara &&
        getBranchNodeId(indivisdualPara, branchList, targetId);
      return branchNodeId ? { id: branchNodeId } : null;
    }
    case SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD: {
      const indivisdualPara = getIndividualPara<PartSystemBranchProps>(attr);
      let branchIndex: number | null = null;
      if (branchList && Array.isArray(branchList)) {
        branchIndex = branchList.findIndex((branch) => branch === targetId);
      }
      const branchNodeId =
        indivisdualPara &&
        getBranchNodeId(indivisdualPara, branchList, targetId);
      const branchNodeIdDefault = getbranchNodeIdDefault(indivisdualPara);
      if (branchIndex === branchList.length - 1) {
        // 最後の分岐の場合は、デフォルトの分岐ノードIDを返す
        if (branchNodeIdDefault) {
          return { id: branchNodeIdDefault };
        }
        return null;
      }
      if (branchNodeId) {
        return { id: branchNodeId };
      }
      return null;
    }
    case SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_CD: {
      const indivisdualPara =
        getIndividualPara<PartInstructionConfirmProps>(attr);
      const branchIndex = branchList.findIndex((branch) => branch === targetId);
      if (branchIndex === branchList.length - 1) {
        const deviationBranchNodeId = getDeviationBranchNodeId(indivisdualPara);
        return deviationBranchNodeId ? { id: deviationBranchNodeId } : null;
      }
      return null;
    }
    case SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD: {
      const indivisdualPara = getIndividualPara<PartSopTimerProps>(attr);
      const branchIndex = branchList.findIndex((branch) => branch === targetId);
      if (branchIndex === branchList.length - 1) {
        const deviationBranchNodeId = getDeviationBranchNodeId(indivisdualPara);
        return deviationBranchNodeId ? { id: deviationBranchNodeId } : null;
      }
      return null;
    }
    case SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD: {
      const indivisdualPara = getIndividualPara<PartExternalDeviceProps>(attr);
      const branchIndex = branchList.findIndex((branch) => branch === targetId);
      if (branchIndex === branchList.length - 1) {
        const deviationBranchNodeId = getDeviationBranchNodeId(indivisdualPara);
        return deviationBranchNodeId ? { id: deviationBranchNodeId } : null;
      }
      return null;
    }
    case SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_CD: {
      const indivisdualPara =
        getIndividualPara<PartWeightCalibrationProps>(attr);
      const branchIndex = branchList.findIndex((branch) => branch === targetId);
      if (branchIndex === branchList.length - 1) {
        const deviationBranchNodeId = getDeviationBranchNodeId(indivisdualPara);
        return deviationBranchNodeId ? { id: deviationBranchNodeId } : null;
      }
      return null;
    }
    case SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD:
    case SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD:
    case SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD:
    case SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD: {
      const conditionProps = getConditionProps(attr);
      const branchIndex = branchList.findIndex((branch) => branch === targetId);
      if (branchIndex === branchList.length - 1) {
        const deviationBranchNodeId = getDeviationBranchNodeId(conditionProps);
        return deviationBranchNodeId ? { id: deviationBranchNodeId } : null;
      }
      return null;
    }
    default:
      return null;
  }
}
/**
 * ノードの2次元配列（ノードマトリックス）からグラフを再描画する
 *
 * @param allNodesMatrix - ノードIDの2次元配列（行: Y方向, 列: X方向）
 * @param nodeAttrMap - ノードIDをキーとした属性情報マップ（位置・種別など）
 *
 * 概要:
 * 1. 既存のノード・エッジを全削除し、allNodesMatrixの内容に基づきノードを再生成する。
 * 2. ノード種別ごとにX/Y座標を計算し、ノードを配置する。
 *    - Start/Endノードは特別なY計算
 *    - Join/Branch/Confluenceなどaddpart系は個別の間隔定数で調整可能
 *    - 通常パーツは等間隔、直前が収束点の場合は間隔を個別調整可能
 * 3. outgoingIdsやbranchOption.valueなどの情報を元にエッジを再接続する。
 * 4. 必要に応じてラベルや属性の復元、レイアウト調整も行う。
 * 5. 最後に全ノードの位置情報をログ出力（デバッグ用）
 *
 * 主な用途:
 * - パーツ追加・削除・編集後のグラフ再描画
 * - ノードの位置や接続関係の再構築
 */
const redrawFromNodesMatrix = (
  allNodesMatrix: Matrix,
  nodeAttrMap: Record<string, unknown>,
  branchList: BranchList,
  parentToConfluenceMap: Record<string, string>,
  outputLog = false,
) => {
  state.sopPartAddType = 'redrawFromNodesMatrixAdd';
  if (outputLog)
    console.log(
      'redrawFromNodesMatrix: 開始',
      'nodeAttrMap:',
      nodeAttrMap,
      'branchList:',
      branchList,
    );
  // 1. 既存ノード・エッジを全削除
  state.sopGraph!.getNodes().forEach((node) => {
    state.sopGraph!.removeNode(node.id);
    node.removeTool('button');
    node.removeTool('boundary');
  });
  state
    .sopGraph!.getEdges()
    .forEach((edge) => state.sopGraph!.removeEdge(edge.id));

  // 位置情報（パーツ幅＋パーツ間のスペース）を定数化
  const sopPartConst = state.SOPSetData?.sopPartConst;
  const partWidth = sopPartConst?.partWidth ?? 376;
  const partSpaceX = sopPartConst?.marginLeft ?? 20;
  const addPartWidth = sopPartConst?.addPartWidth ?? 24;
  const startPartWidth = sopPartConst?.startPartWidth ?? partWidth;
  const xInterval = partWidth + partSpaceX;
  const baseX = graphWidth.value / 2 - startPartWidth / 2;

  // 2. 2次元配列からノードを再作成
  const idToNodeMap = new Map<string, Node>();
  allNodesMatrix.forEach((row, yIdx) => {
    row.forEach((id, xIdx) => {
      if (!id) return;
      // ノード種別ごとに位置を調整
      let posX = baseX;
      // let posY = baseY;
      let sopPartsCD = '';
      if (nodeAttrMap && nodeAttrMap[id]) {
        sopPartsCD =
          // @ts-expect-error sopPartsCD/shape属性ある
          nodeAttrMap[id]?.sopPartsCD || nodeAttrMap[id]?.shape || '';
      }

      // --- X位置計算 ---
      if (
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_START_NAME ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_START_CD ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_END_NAME ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_END_CD
      ) {
        posX = baseX + xIdx * xInterval;
      } else if (
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_JOIN_NAME ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_NAME ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_NAME ||
        sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD
      ) {
        posX = baseX + (startPartWidth - addPartWidth) / 2 + xIdx * xInterval;
      } else {
        posX = baseX + (startPartWidth - partWidth) / 2 + xIdx * xInterval;
      }
      // --- Y位置計算 ---
      // オプション指定
      const options = {
        baseY: 10, // ベースY
        marginY: 104, // マージン（Y間隔）
      };
      // 呼び出し
      const posY = calcNodePosY(
        sopPartsCD,
        yIdx,
        allNodesMatrix,
        nodeAttrMap,
        options,
      );
      // --- ノード追加処理 ---
      // @ts-expect-error nodeAttrMap[id]はオブジェクトです。
      const attr = { ...nodeAttrMap[id] };
      const node = state.sopGraph!.addNode({
        id,
        ...attr,
        position: { x: posX, y: posY },
      });
      // nodeAttrMapが関数パラメータの場合、直接代入せずにObject.assignを使う
      if (nodeAttrMap[id]) {
        Object.assign(nodeAttrMap[id], {
          position: { x: posX, y: posY }, // ノード位置を更新
        });
      }
      // ノードの位置を設定
      if (node && typeof node.setPosition === 'function') {
        node.setPosition(posX, posY);
      }
      idToNodeMap.set(id, node);
    });
  });
  if (outputLog) console.log('ノード配置完了:', Array.from(idToNodeMap.keys()));

  // 3. ノード間のエッジを再接続
  // ループ設定時の出力先リスト
  const loopOutputList: { id: string; outputId: string }[] = [];

  state
    .sopGraph!.getEdges()
    .forEach((edge) => state.sopGraph!.removeEdge(edge.id));
  allNodesMatrix.forEach((row, yIdx) => {
    row.forEach((id, xIdx) => {
      if (!id) return;
      // ノード種別判定
      const rowNode = idToNodeMap.get(id);
      if (!rowNode) {
        return;
      }
      const sopPartsCd = rowNode.getProp<string>('sopPartsCD');
      const nodeType = getEdgeNodeType(sopPartsCd);

      // 入力エッジ
      let inputSourceId: string | null = null;
      // 出力エッジ
      let outputTargetId: string | null = null;

      if (nodeType === 'normal') {
        // 入力エッジ: 前の行・同じ列
        if (yIdx > 0) {
          inputSourceId = allNodesMatrix[yIdx - 1][xIdx];
        }
        // 出力エッジ: 次の行・同じ列
        if (
          yIdx < allNodesMatrix.length - 1 &&
          allNodesMatrix[yIdx + 1][xIdx] !== undefined &&
          allNodesMatrix[yIdx + 1][xIdx] !== null &&
          allNodesMatrix[yIdx + 1][xIdx] !== ''
        ) {
          outputTargetId = allNodesMatrix[yIdx + 1][xIdx];
        } else {
          // 次行以降かつ同じ列で初めに見つかったノードを出力エッジとする
          for (let nextY = yIdx + 1; nextY < allNodesMatrix.length; nextY++) {
            if (yIdx === allNodesMatrix.length - 1) {
              break; // 最後の行は次の行がないため終了
            }
            const candidate = allNodesMatrix[nextY][xIdx];
            if (
              candidate !== undefined &&
              candidate !== null &&
              candidate !== ''
            ) {
              outputTargetId = candidate;
              break;
            }
          }
          if (!outputTargetId) {
            // 関連する収束ノード
            const parentBranchId =
              findNearParentBranchId(branchList, allNodesMatrix[yIdx][xIdx]) ||
              allNodesMatrix[yIdx][xIdx];
            const confluenceNodeId = parentToConfluenceMap[parentBranchId];
            if (confluenceNodeId) {
              outputTargetId = confluenceNodeId;
            }
          }
        }
      } else if (nodeType === SOP_PARTS_VARIABLES.PART_SPEC_JOIN_NAME) {
        // 入力エッジ: 前の行・同じ列
        if (yIdx > 0) {
          inputSourceId = allNodesMatrix[yIdx - 1][xIdx];
        }
        // 出力エッジ: 次の行・同じ列
        if (
          yIdx < allNodesMatrix.length - 1 &&
          allNodesMatrix[yIdx + 1][xIdx] !== undefined &&
          allNodesMatrix[yIdx + 1][xIdx] !== null &&
          allNodesMatrix[yIdx + 1][xIdx] !== ''
        ) {
          outputTargetId = allNodesMatrix[yIdx + 1][xIdx];
        } else {
          // 関連する収束ノード
          const parentBranchId =
            findNearParentBranchId(branchList, allNodesMatrix[yIdx][xIdx]) ||
            allNodesMatrix[yIdx][xIdx];
          const confluenceNodeId = parentToConfluenceMap[parentBranchId];
          if (confluenceNodeId) {
            outputTargetId = confluenceNodeId;
          }
        }
      } else if (nodeType === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_NAME) {
        // 入力エッジ: 親分岐ノード
        // 分岐親パーツ特定
        const parentBranchId =
          findNearParentBranchId(branchList, allNodesMatrix[yIdx][xIdx]) ||
          allNodesMatrix[yIdx][xIdx];
        const parentRow = findNodeRow(allNodesMatrix, parentBranchId);
        const parentBranchList = allNodesMatrix[parentRow + 1].filter(
          (cell) => cell !== undefined && cell !== null && cell !== '',
        );
        if (
          parentBranchList.length > 0 &&
          parentBranchList.includes(allNodesMatrix[yIdx][xIdx])
        ) {
          inputSourceId = parentBranchId;
        }
        // 出力エッジ
        // 次の行・同じ列
        if (
          yIdx < allNodesMatrix.length - 1 &&
          allNodesMatrix[yIdx + 1][xIdx] !== undefined &&
          allNodesMatrix[yIdx + 1][xIdx] !== null &&
          allNodesMatrix[yIdx + 1][xIdx] !== ''
        ) {
          outputTargetId = allNodesMatrix[yIdx + 1][xIdx];
        } else {
          // ループ設定がある場合
          const loopTarget = getLoopTargetNode(
            id,
            parentBranchId,
            parentBranchList,
            nodeAttrMap,
          );
          if (loopTarget) {
            outputTargetId = loopTarget.id;
            loopOutputList.push({ id, outputId: outputTargetId });
          } else {
            // 関連する収束ノード
            const confluenceNodeId = parentToConfluenceMap[parentBranchId];
            if (confluenceNodeId) {
              outputTargetId = confluenceNodeId;
            }
          }
        }
      } else if (nodeType === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_NAME) {
        // 入力エッジ: 前の行・同じ列
        if (yIdx > 0) {
          inputSourceId = allNodesMatrix[yIdx - 1][xIdx];
        }
        // 出力エッジ: 次の行・同じ列
        if (
          yIdx < allNodesMatrix.length - 1 &&
          allNodesMatrix[yIdx + 1][xIdx] !== undefined &&
          allNodesMatrix[yIdx + 1][xIdx] !== null &&
          allNodesMatrix[yIdx + 1][xIdx] !== ''
        ) {
          outputTargetId = allNodesMatrix[yIdx + 1][xIdx];
        } else {
          // 関連する収束ノード
          const parentBranchId =
            findParentBranchId(branchList, allNodesMatrix[yIdx][xIdx]) ||
            allNodesMatrix[yIdx][xIdx];
          const rowFrom = findNodeRow(
            allNodesMatrix,
            allNodesMatrix[yIdx][xIdx],
          );
          const rowTo = findNodeRow(
            allNodesMatrix,
            parentToConfluenceMap[parentBranchId],
          );
          // 収束ノードの行を探す
          for (let i = rowFrom + 1; i <= rowTo; i++) {
            const nextRow = allNodesMatrix[i];
            if (
              nextRow &&
              nextRow[0] !== undefined &&
              nextRow[0] !== null &&
              nextRow[0] !== ''
            ) {
              const nextNode = idToNodeMap.get(nextRow[0]);
              if (!nextNode) {
                return;
              }
              const partsCd = rowNode.getProp<string>('sopPartsCD');
              if (partsCd === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD) {
                const [firstId] = nextRow;
                outputTargetId = firstId;
                break;
              }
            }
          }
        }
      }
      // エッジ追加
      if (outputTargetId && outputTargetId !== id) {
        const targetNode = state.sopGraph!.getCellById(outputTargetId);
        const targetPartsCd = targetNode.getProp<string>('sopPartsCD');
        const sameCdFlag = checkPartCode(
          state.SOPSetData.partCds,
          targetPartsCd,
        );
        if (
          sameCdFlag ||
          [
            SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_CD,
            SOP_PARTS_VARIABLES.PART_SPEC_END_CD,
          ].includes(targetPartsCd)
        ) {
          addBlockTargetEdge4Temp(id, outputTargetId);
        } else {
          addPathTargetEdge(id, outputTargetId);
        }
      }
      if (inputSourceId && inputSourceId !== id) {
        const targetNode = state.sopGraph!.getCellById(id);
        const targetPartsCd = targetNode.getProp<string>('sopPartsCD');
        if (targetPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD) {
          addPathTargetEdge(inputSourceId, id);
        }
      }
    });
  });

  // 4. ノード属性やラベルなどの追加復元が必要な場合はここで行う
  allNodesMatrix.forEach((row) => {
    row.forEach((id) => {
      if (!id) return;
      let sopPartsCD = '';
      if (nodeAttrMap && nodeAttrMap[id]) {
        sopPartsCD =
          // @ts-expect-error sopPartsCD/shape属性ある
          nodeAttrMap[id]?.sopPartsCD || nodeAttrMap[id]?.shape || '';
      }
      if (sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD) {
        // branchOptionが配列の場合、各branchOptionにlabelをセット
        // @ts-expect-error branchOption属性ある
        const branchOption = nodeAttrMap[id]?.branchOption;
        if (Array.isArray(branchOption)) {
          branchOption.forEach((opt: SelectBranchOption) => {
            // ノードのラベルや他の情報からlabelをセット
            // @ts-expect-error label属性ある
            // eslint-disable-next-line no-param-reassign
            opt.label = nodeAttrMap[id]?.label ?? '';
          });
          // @ts-expect-error branchOption属性ある
          // eslint-disable-next-line no-param-reassign
          nodeAttrMap[id].branchOption = branchOption;
        }
      }
    });
  });

  // 5. レイアウト調整やツール追加なども必要に応じて
  // 収束ノードに対する.verticesプロパティの設定
  const allConfluenceNodes = state
    .sopGraph!.getNodes()
    .filter(
      (node) =>
        node.getProp('sopPartsCD') ===
          SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_NAME ||
        node.getProp('sopPartsCD') ===
          SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD,
    );
  allConfluenceNodes.forEach((confluenceNode) => {
    const incomingEdges = state.sopGraph!.getConnectedEdges(confluenceNode, {
      incoming: true,
    });
    const confluencePos = confluenceNode.getProp<PositionOption>('position');
    incomingEdges.forEach((edge) => {
      // 収束ノードのY座標と異なる場合のみverticesを設定
      const sourceNode = state.sopGraph!.getCellById(edge.getSourceCellId());
      const sourcePos = sourceNode.getProp<PositionOption>('position');
      if (sourcePos.y !== confluencePos.y) {
        // eslint-disable-next-line no-param-reassign
        edge.vertices = [
          {
            x: sourcePos.x,
            y:
              confluencePos.y -
              (state.SOPSetData?.sopPartConst?.addPartHeight ?? 24),
          },
        ];
      }
    });
  });
  // ループ設定パーツに対する.verticesプロパティの設定
  loopOutputList.forEach(({ id, outputId }) => {
    const columnIndex = findNodeCol(allNodesMatrix, id);
    if (columnIndex === 0) {
      return;
    }
    const outGoingEdges = state.sopGraph!.getConnectedEdges(id, {
      outgoing: true,
    });
    const outputIdNode = state.sopGraph!.getCellById(outputId);
    if (!outputIdNode) return;
    const outputPos = outputIdNode.getProp<PositionOption>('position');
    outGoingEdges.forEach((edge) => {
      // ループ設定ノードのY座標と異なる場合のみverticesを設定
      const sourceNode = state.sopGraph!.getCellById(edge.getSourceCellId());
      if (!sourceNode) return;
      const sourcePos = sourceNode.getProp<PositionOption>('position');
      if (sourcePos.y !== outputPos.y) {
        // eslint-disable-next-line no-param-reassign
        edge.vertices = [
          {
            x: sourcePos.x + 20,
            y:
              outputPos.y -
              (state.SOPSetData?.sopPartConst?.addPartHeight ?? 24),
          },
        ];
      }
    });
  });

  // tools追加
  const addToolsNodes = state
    .sopGraph!.getNodes()
    .filter(
      (node) =>
        node.getProp('sopPartsCD') !== SOP_PARTS_VARIABLES.PART_SPEC_START_CD &&
        node.getProp('sopPartsCD') !== SOP_PARTS_VARIABLES.PART_SPEC_END_CD &&
        node.getProp('sopPartsCD') !==
          SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD &&
        node.getProp('sopPartsCD') !==
          SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD &&
        node.getProp('sopPartsCD') !== SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD,
    );
  addToolsNodes.forEach((node) => {
    node.addTools({
      name: 'button',
      args: {
        zIndex: 1000,
        markup: [
          {
            tagName: 'image',
            selector: 'copy',
            attrs: {
              'xlink:href': state.imageMap.get('copy'),
              width: 20,
              height: 20,
              cursor: 'pointer',
              'pointer-events': 'all',
            },
          },
        ],
        x: '100%',
        y: 0,
        offset: { x: -25, y: 8 },
        onClick() {
          cellCopy(node.id);
        },
      },
    });
    node.addTools({
      name: 'button',
      args: {
        zIndex: 1000,
        markup: [
          {
            tagName: 'image',
            selector: 'trash',
            attrs: {
              'xlink:href': state.imageMap.get('trash'),
              width: 20,
              height: 20,
              cursor: 'pointer',
              'pointer-events': 'all',
            },
          },
        ],
        x: '100%',
        y: 0,
        offset: { x: -55, y: 8 },
        onClick({ view }: { view: NodeView }) {
          state.removeNodeId = view.cell.id;
          cellRemove();
        },
      },
    });
    node.addTools({
      name: 'button',
      args: {
        zIndex: 1000,
        markup: [
          {
            tagName: 'image',
            selector: 'edit',
            attrs: {
              'xlink:href': state.imageMap.get('edit'),
              width: 20,
              height: 20,
              cursor: 'pointer',
              'pointer-events': 'all',
            },
          },
        ],
        x: '100%',
        y: 0,
        offset: { x: -85, y: 8 },
        onClick({ view }: { view: NodeView }) {
          setIncomingNodes(view.cell);
          state.isSopInfoSettingDialogVisible = true;
          currentNodeSetting(view.cell.id);
        },
      },
    });
  });

  // 位置情報チェック処理
  const nodeAll = state.sopGraph!.getNodes();
  const diffNodes: string[] = [];
  nodeAll.forEach((node) => {
    const { id } = node;
    const graphPos = node.getProp<PositionOption>('position');
    const attrPos =
      nodeAttrMap &&
      nodeAttrMap[id] &&
      // @ts-expect-error 既にnodeAttrMap[id]にposition追加
      (nodeAttrMap[id] as SopControlPartOption).position
        ? // @ts-expect-error 既にnodeAttrMap[id]にposition追加
          (nodeAttrMap[id] as SopControlPartOption).position
        : undefined;
    if (attrPos && (graphPos.x !== attrPos.x || graphPos.y !== attrPos.y)) {
      diffNodes.push(id);
      // 位置が異なる場合はnodeAttrMapの座標で更新
      if (typeof node.setPosition === 'function') {
        node.setPosition(attrPos.x, attrPos.y);
        console.log(
          `[位置修正] ID: ${id}, graphPos: x=${graphPos.x}, y=${graphPos.y} → attrPos: x=${attrPos.x}, y=${attrPos.y}`,
        );
      }
    }
  });

  if (outputLog) {
    // 全ノードのエッジをノード毎にログ表示（ノードID、入力エッジの接続先ノードID、出力エッジの接続先ノードID）
    const allEdges = state.sopGraph!.getNodes();
    console.log('--- 全ノードのエッジ情報 ---');
    allEdges.forEach((node) => {
      // 入力エッジの接続元ノードID
      const incomingEdges = state.sopGraph!.getIncomingEdges(node.id) || [];
      const incomingNodeIds = incomingEdges.map((e) => e.getSourceCellId());

      // 出力エッジの接続先ノードID
      const outgoingEdges = state.sopGraph!.getOutgoingEdges(node.id) || [];
      const outgoingNodeIds = outgoingEdges.map((e) => e.getTargetCellId());
      console.log(
        `ノードID: ${node.id}, 入力エッジ接続元: [${incomingNodeIds.join(', ')}], 出力エッジ接続先: [${outgoingNodeIds.join(', ')}]`,
      );
    });

    // 全ノードの位置情報をログ出力（デバッグ用)
    const allNodes = state.sopGraph!.getNodes();
    console.log('--- 全ノードの位置情報 ---');
    allNodes.forEach((node) => {
      const pos = node.getProp<PositionOption>('position');
      const partsCD = node.getProp<string>('sopPartsCD');
      console.log(
        `ID: ${node.id}, パーツ種別: ${partsCD}, 位置: x=${pos.x}, y=${pos.y}`,
      );
    });
  }
};

/**
 * 現在のグラフ上のノードから「ID→属性」マップを作成する
 * @returns {Record<string, any>} ノードIDをキー、属性（toJSON()の内容）を値とするマップ
 */
const createNodeIdToAttrMap = (): Record<string, unknown> => {
  const map: Record<string, unknown> = {};
  const allNodes = state.sopGraph!.getNodes();
  allNodes.forEach((node) => {
    // ディープコピーで独立した値を保存
    const nodeData = JSON.parse(JSON.stringify(node.toJSON()));
    (nodeData as Record<string, unknown>).outgoingIds = state
      .sopGraph!.getNeighbors(node, { outgoing: true })
      .map((n) => n.id);
    map[node.id] = nodeData;
  });
  return map;
};

/**
 * 現在のグラフ上のノードから「ID→属性」マップを作成する
 * @returns {Record<string, any>} ノードIDをキー、属性（toJSON()の内容）を値とするマップ
 */
const createNodeIdToAttrMapFromDB = (
  nodeLists: SopBlockPartOption[],
): Record<string, SopControlPartOption> => {
  const map: Record<string, SopControlPartOption> = {};
  // const allNodes = state.sopGraph!.getNodes();
  nodeLists.forEach((node) => {
    // ディープコピーで独立した値を保存
    const nodeData = JSON.parse(JSON.stringify(node));
    // (nodeData as Record<string, unknown>).outgoingIds = state.sopGraph!.getNeighbors(node, { outgoing: true }).map(n => n.id);
    map[node.id || ''] = nodeData;
  });
  return map;
};

/**
 * ノードマトリックスとノード属性マップからグラフを再描画する（ラップ関数）
 * @param nodesMatrix - ノードIDの2次元配列
 */
const redrawGraphFromMatrix = (
  nodesMatrix: Matrix,
  branchList: BranchList,
  parentToConfluenceMap: Record<string, string>,
  node: SopFlowDataOption[],
) => {
  const nodeAttrMap = node.length
    ? // @ts-expect-error 一旦assignします
      createNodeIdToAttrMapFromDB(node)
    : createNodeIdToAttrMap();
  redrawFromNodesMatrix(
    nodesMatrix,
    nodeAttrMap,
    branchList,
    parentToConfluenceMap,
    false,
  );
};

/**
 * ノード削除
 * @param {*} cell - 対象セル
 * @remarks
 * <<呼び出しメソッド>>
 * cellRemove
 */
const nodeDelete = (nodeID: string) => {
  const currentNode = state.sopGraph!.getCellById(nodeID);
  const currentNodePartsName = currentNode.getProp<string>('sopPartsCD');
  if (!currentNode.isNode()) {
    return;
  }
  const selectCells = state.sopGraph!.getSelectedCells();
  if (selectCells.length > 1) {
    messageBoxSingleButtonRef.value.title = `${t('SOP.Menu.txtSopTitle')}`;
    messageBoxSingleButtonRef.value.content = `${t('SOP.Msg.deleteMultipleSelect')}`;
    openDialog('singleButtonRef');
    return;
  }
  if (currentNodePartsName === SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_NAME) {
    state.blockExpandList = state.blockExpandList.filter(
      (item) => item.parentSopNodeNo !== nodeID,
    );
  }
  const currntNodeType = currentNode.getProp<string>('sopPartsCD');
  const PartsNodeFlag = checkPartCode(state.SOPSetData.partCds, currntNodeType);
  const blocknodeFlag = checkBlockPartCode(state.SOPSetData, currntNodeType);
  const controlnodeFlag = checkControlPartCode(
    state.SOPSetData,
    currntNodeType,
  );
  if (!(PartsNodeFlag || blocknodeFlag || controlnodeFlag)) {
    messageBoxSingleButtonRef.value.title = `${t('SOP.Menu.txtSopTitle')}`;
    messageBoxSingleButtonRef.value.content = `${t('SOP.Msg.deleteAddParts')}`;
    openDialog('singleButtonRef');
    return;
  }
  //  TODO: 削除対象が分岐元ノードかの判定とその場合はsuccesorの取得
  let branchOption!: SelectBranchOption[];
  if (currentNode.isNode()) {
    branchOption = getBranchOptions(currentNode);
    // [課題管理表][No.189] ST 分岐数は1の分岐は収束がないので、普通のノートとして扱う
    if (
      branchOption.length === 1 ||
      (branchOption.every((item) => item.branchName === '') &&
        [
          SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD,
          SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD as string,
        ].includes(currntNodeType))
    ) {
      branchOption = [];
    }
    // [課題管理表][No.189] ED 分岐数は1の分岐は収束がないので、普通のノートとして扱う

    if (currntNodeType === SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD) {
      // 削除対象ノードに関連するグリッドデータを削除
      state.childNodeList = state.childNodeList.filter(
        (item) => item.parentSopNodeNo !== nodeID,
      );
    }
  }
  // let targetBranchNode!: Cell;
  const targetBranchSuccessors: string[] = [];
  // 削除された ID を格納するための
  let deleteNodeList = [nodeID];
  //  隣接ノードの入力側のみ取得
  let incomingId: string = '';
  const incoming: Cell[] = state.sopGraph!.getNeighbors(currentNode, {
    incoming: true,
  });

  if (incoming.length > 0) {
    incomingId = incoming[0].id;
  }
  let outgoingId: string = '';
  const loopIntersectionList: Cell<Cell.Properties>[] = [];
  const currentPosition = currentNode.getProp<PositionOption>('position');
  // 自分にループのノードを見つける
  const getPredecessors = state.sopGraph!.getPredecessors(currentNode, {
    deep: true,
  });
  const nodeIndex = branchNodeIndex(getPredecessors);
  // ブランチの分岐数によって、分岐を削除するかどうかを判断します。
  if (branchOption.length > 0) {
    const successors = state.sopGraph!.getSuccessors(currentNode, {
      deep: true,
    });
    const coverIndex = findCoverIndex(successors);
    if (coverIndex !== -1) {
      outgoingId = state.sopGraph!.getNeighbors(successors[coverIndex], {
        outgoing: true,
      })[0].id;
      const outgoingNodePosition = state
        .sopGraph!.getCellById(outgoingId)
        .getProp<PositionOption>('position');
      const outgoing: Cell[] = state.sopGraph!.getNeighbors(currentNode, {
        outgoing: true,
      });
      outgoing.forEach((item) => {
        const branchSuccessors = state.sopGraph!.getSuccessors(item, {
          deep: true,
        });
        branchSuccessors.forEach((successorsItem) => {
          const successorsItemPosition =
            successorsItem.getProp<PositionOption>('position');
          if (
            currentPosition.y < successorsItemPosition.y &&
            successorsItemPosition.y < outgoingNodePosition.y &&
            isNodesInSameBranch(item.id, successorsItem.id)
          ) {
            deleteNodeList.push(successorsItem.id);
            deleteNodeList.push(item.id);
          }
        });
      });
      deleteNodeList = deleteNodeList.concat(targetBranchSuccessors);
      deleteNodeList = Array.from(new Set([...deleteNodeList]));
      deleteNodeList.forEach((item) => {
        const deleteNode = state.sopGraph!.getCellById(item);
        const partsCD = deleteNode.getProp<string>('sopPartsCD');
        if (!notDeleteParts.includes(partsCD)) {
          state.sopGraph!.removeNode(item);
        }
      });
    } else {
      outgoingId = state.sopGraph!.getSuccessors(currentNode, {
        deep: true,
      })[1].id;
      const partsCD = currentNode.getProp<string>('sopPartsCD');
      if (!notDeleteParts.includes(partsCD)) {
        state.sopGraph!.removeNode(currentNode);
      }
    }
  } else {
    const outgoingNodeList = state.sopGraph!.getSuccessors(currentNode, {
      deep: true,
    });
    const currentNodeIncoming: Cell[] = state.sopGraph!.getNeighbors(
      currentNode,
      {
        incoming: true,
      },
    );
    if (currentNodeIncoming.length > 1) {
      currentNodeIncoming.forEach((item) => {
        if (
          item.getProp<PositionOption>('position').y >
          currentNode.getProp<PositionOption>('position').y
        ) {
          const incomingNodePredecessors = state.sopGraph!.getPredecessors(
            item,
            {
              deep: true,
            },
          );
          const incomingNodeIndex = branchNodeIndex(incomingNodePredecessors);
          const incomingNodeSuccessors = state.sopGraph!.getSuccessors(
            incomingNodePredecessors[incomingNodeIndex],
            {
              deep: true,
            },
          );
          const coverIndex = findCoverIndex(incomingNodeSuccessors);
          addPathTargetEdge(item.id, incomingNodeSuccessors[coverIndex].id);
        }
      });
    }
    const getIntersection = outgoingNodeList.filter(
      (item) =>
        getPredecessors.includes(item) && currentNodeIncoming.length === 1,
    );
    getIntersection.forEach((item) => {
      const intersectionItemPosition = item.getProp<PositionOption>('position');
      const incomingPosition = state
        .sopGraph!.getCellById(incomingId)
        .getProp<PositionOption>('position');
      if (intersectionItemPosition.y > incomingPosition.y) {
        loopIntersectionList.push(item);
      }
    });
    if (
      loopIntersectionList.length > 1 &&
      currentPosition.y >
        getPredecessors[nodeIndex].getProp<PositionOption>('position').y
    ) {
      const loopIntersectionTransVal = getTranslatePosition(
        currentNodeIncoming[0],
        outgoingNodeList[1],
      );
      loopIntersectionList.forEach((item) => {
        item.translate(0, loopIntersectionTransVal.translateY);
      });
    }
    deleteNodeList = deleteNodeList.concat(outgoingNodeList[0].id);
    deleteNodeList.forEach((item) => {
      const deleteNode = state.sopGraph!.getCellById(item);
      const partsCD = deleteNode.getProp<string>('sopPartsCD');
      if (!notDeleteParts.includes(partsCD)) {
        state.sopGraph!.removeNode(item);
      }
    });
    outgoingId = outgoingNodeList[1].id;
  }
  // 削除のノードincomingとoutgoingノードを見つける
  const incomingPosition = state
    .sopGraph!.getCellById(incomingId)
    .getProp<PositionOption>('position');
  const outgoingPosition = state
    .sopGraph!.getCellById(outgoingId)
    .getProp<PositionOption>('position');
  const goBack = incomingPosition.y > outgoingPosition.y;
  addBlockTargetEdge(incomingId, outgoingId, goBack);
  // 削除後の分岐を指すための
  const allNodeId: string[] = [];
  const allParts = state.sopGraph!.getNodes();
  allParts.forEach((item) => {
    const partsCD = item.getProp<string>('sopPartsCD');
    const sameCdFlag = checkPartCode(state.SOPSetData.partCds, partsCD);
    const controlFlag = checkPartCode(state.SOPSetData.controlPartCds, partsCD);
    const blockFlag = checkPartCode(state.SOPSetData.blockPartCds, partsCD);
    if (sameCdFlag || controlFlag || blockFlag) {
      allNodeId.push(item.id);
    }
  });
  allNodeId.forEach((item) => {
    const targetNode = state.sopGraph!.getCellById(item);
    if (targetNode && targetNode.isNode()) {
      const individualPara = targetNode.getProp('individualPara');
      const individualParaKey: string =
        Object.keys(individualPara).find(
          (key) => individualPara[key] === nodeID,
        ) || '';
      individualPara[individualParaKey] = '';
    }
  });
  nodeDeleteNoIncomingEdgeNode();
  if (blocknodeFlag) {
    // ブロックパーツの場合、対象パーツのブロック情報を削除
    const newList = state.sopSelectVisableOption.filter(
      (item) => item.blockId !== nodeID,
    );
    state.sopSelectVisableOption = newList;
  }

  // 再描画処理
  const allNodes = state.sopGraph!.getNodes();
  const mergedMatrix: Matrix = createNodesMatrix(allNodes);
  console.log('パーツ削除後（2次元配列）:', mergedMatrix);
  const { branchList, parentToConfluenceMap } = createBranchList(mergedMatrix);
  redrawGraphFromMatrix(mergedMatrix, branchList, parentToConfluenceMap, []);
};

// parentNodeに接続するaddPartノードを作成する例
const createAddPartForParent = (parentNode: Node) => {
  // 1. 親ノードの位置を取得
  const parentPos = parentNode.getProp<PositionOption>('position');
  const parentSize = parentNode.getProp<SizeOption>('size');
  const { sopPartConst } = state.SOPSetData;

  // 2. addPartノードの座標を計算
  const addPartX =
    parentPos.x + parentSize.width / 2 - sopPartConst!.addPartWidth / 2;
  const addPartY = parentPos.y + parentSize.height + sopPartConst!.marginTop;
  // 3. addPartノードを作成
  const addPartNode = createAddPart(addPartX, addPartY);

  // 4. アイコンや属性をセット
  setAddPartIcon(addPartNode);
  addPartNode.prop('sopPartsCD', SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD);

  // 5. 親ノードとaddPartノードを接続
  addPathTargetEdge(parentNode.id, addPartNode.id);

  return addPartNode;
};

// 分岐あり→なしに設定変更した場合の判定をパーツ種別ごとに分岐
const isBranchToNoneChange = (targetNode: Node): boolean => {
  const sopPartsCd = targetNode.getProp<string>('sopPartsCD');

  // 指示内容確認・SOPタイマー・外部機器通信・計測器点検
  if (
    sopPartsCd === SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_CD ||
    sopPartsCd === SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD ||
    sopPartsCd === SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD ||
    sopPartsCd === SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_CD
  ) {
    const before = state.oldSOPPartSetData.individualPara?.conditionBranch;
    const after = state.SOPPartSetData.individualPara?.conditionBranch;
    return before === '1' && after === '0';
  }

  // 数値文字入力、在庫消費、実績確定、受入投入
  if (
    sopPartsCd === SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD ||
    sopPartsCd === SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD ||
    sopPartsCd === SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD ||
    sopPartsCd === SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD
  ) {
    const before = state.oldSOPPartSetData.conditionProps?.conditionBranch;
    const after = state.SOPPartSetData.conditionProps?.conditionBranch;
    return before === '1' && after === '0';
  }

  // システム分岐・ボタン分岐
  if (
    sopPartsCd === SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD ||
    sopPartsCd === SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD
  ) {
    const beforeNum = state.oldSOPPartSetData.individualPara?.branchNumSetting;
    const afterNum = state.SOPPartSetData.individualPara?.branchNumSetting;
    return (
      typeof beforeNum !== 'undefined' &&
      typeof afterNum !== 'undefined' &&
      beforeNum >= 2 &&
      afterNum < 2
    );
  }

  return false;
};
// 分岐なし→ありに設定変更した場合の判定をパーツ種別ごとに分岐
const isBranchFromNoneChange = (targetNode: Node): boolean => {
  const sopPartsCd = targetNode.getProp<string>('sopPartsCD');

  // 指示内容確認・SOPタイマー・外部機器通信・計測器点検
  if (
    sopPartsCd === SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_CD ||
    sopPartsCd === SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD ||
    sopPartsCd === SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD ||
    sopPartsCd === SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_CD
  ) {
    const before = state.oldSOPPartSetData.individualPara?.conditionBranch;
    const after = state.SOPPartSetData.individualPara?.conditionBranch;
    return (before === '' || before === '0') && after === '1';
  }

  // 数値文字入力、在庫消費、実績確定、受入投入
  if (
    sopPartsCd === SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD ||
    sopPartsCd === SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD ||
    sopPartsCd === SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD ||
    sopPartsCd === SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD
  ) {
    const before = state.oldSOPPartSetData.conditionProps?.conditionBranch;
    const after = state.SOPPartSetData.conditionProps?.conditionBranch;
    return (before === '' || before === '0') && after === '1';
  }

  return false;
};

/**
 * ダイアログ確定ボタン押下
 */
const closeSopInfoSetting = () => {
  const blnOutputDebugLog = true;
  let mergedMatrix: Matrix = [];
  // 1. 設定変更前(2次元配列)の取得
  const allNodesBefore = state.sopGraph!.getNodes();
  const allNodesMatrixBefore = createNodesMatrix(allNodesBefore);
  if (blnOutputDebugLog)
    console.log('設定変更前（2次元配列）:', allNodesMatrixBefore);
  const targetNode = state.sopGraph!.getCellById(state.SOPPartSetData.id);
  const sopPartsCd = targetNode.getProp<string>('sopPartsCD');
  state.oldSOPPartSetData.confluenceNodeId =
    targetNode.getProp<string>('confluenceNodeId'); // 設定前の収束IDを退避

  // 分岐パーツと収束ノードのマッピング情報を作成する（行の昇順で作成）
  const currentBranchParts: BranchList = {};
  const { branchList, parentToConfluenceMap } =
    createBranchList(allNodesMatrixBefore);
  console.log(
    'branchList:',
    branchList,
    'parentToConfluenceMap:',
    parentToConfluenceMap,
  );

  state.sopGraph!.batchUpdate(() => {
    // 2. ノード属性の変更処理
    if (targetNode.isNode()) {
      const { commonSetting } = state.SOPPartSetData;
      removeNodeTools(targetNode);
      targetNode.attr({
        title: {
          text: commonSetting.sopNodeNmJp,
        },
      });
      targetNode.prop('commonSetting', state.SOPPartSetData.commonSetting);
      targetNode.prop('individualPara', state.SOPPartSetData.individualPara);
      targetNode.prop('conditionProps', state.SOPPartSetData.conditionProps);
      targetNode.prop('sopCondition', state.SOPPartSetData.sopCondition);
      targetNode.prop(
        'upperLowerSetting',
        state.SOPPartSetData.upperLowerSetting,
      );
      targetNode.prop('instUnitTxt', state.SOPPartSetData.instUnitTxt);
      targetNode.prop(
        'confluenceNodeId',
        state.SOPPartSetData.confluenceNodeId,
      );
      if (oldSopNodeNmJp && oldSopNodeNmJp !== '') {
        if (oldSopNodeNmJp !== state.SOPPartSetData.commonSetting.sopNodeNmJp) {
          const nodeNum = allNodesBefore.filter(
            (item) =>
              item.getProp('commonSetting').sopNodeNmJp === oldSopNodeNmJp,
          );
          if (nodeNum.length === 1) {
            nodeNum[0].setAttrs({
              body: {
                stroke: '#a8b0c2',
                fill: '#f0ffff',
              },
            });
          }
        }
      }
      // 分岐・収束ノードの追加・削除
      let newAddPartIds: string[] = [];
      let removedPartIds: string[] = [];
      if (hasConditionBranch(targetNode)) {
        // 分岐設定あり
        const branchOption = setBranchOptionsLabel(targetNode);
        if (branchOption.length > 0) {
          if (
            sopPartsCd === SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD ||
            sopPartsCd === SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD
          ) {
            // システム分岐・ボタン分岐の場合
            const foundBranch = Object.keys(branchList).find(
              (branchId) => branchId === targetNode.id,
            );
            if (!foundBranch) {
              // 収束ノードなし
              const [addIds, removedIds] = addBranchLabelsAndConfluence(
                branchOption,
                targetNode,
                allNodesMatrixBefore,
              );
              // 必ず親ノードにconfluenceNodeIdをセット
              if (addIds && addIds.length > 0) {
                // 4桁IDが収束ノードと判定
                const confluenceId = addIds.find((id) => /^\d{4}$/.test(id));
                if (confluenceId) {
                  targetNode.prop('confluenceNodeId', confluenceId);
                }
              }
              mergedMatrix = mergeMatrix({
                matrix: allNodesMatrixBefore,
                targetNode,
                addNodeIds: addIds,
                removeNodeIds: removedIds,
                branchList,
                parentToConfluenceMap,
                pattern: 'branchSet',
              });
            } else {
              // 収束ノードあり
              const before =
                state.oldSOPPartSetData.individualPara?.branchNumSetting;
              const after =
                state.SOPPartSetData.individualPara?.branchNumSetting;
              // 追加時
              if (
                typeof before !== 'undefined' &&
                typeof after !== 'undefined' &&
                before < after
              ) {
                console.log(`追加時 before: ${before}, after: ${after}`);
                newAddPartIds = addBranchLabels(
                  branchOption,
                  targetNode,
                  allNodesMatrixBefore,
                );
                setBranchOptionsValue(branchOption, targetNode);
                mergedMatrix = mergeMatrix({
                  matrix: allNodesMatrixBefore,
                  targetNode,
                  addNodeIds: newAddPartIds,
                  removeNodeIds: [],
                  branchList,
                  parentToConfluenceMap,
                  pattern: 'branchAdd',
                });
                currentBranchParts[targetNode.id] = newAddPartIds;
              }
              // 削除時
              else if (
                typeof before !== 'undefined' &&
                typeof after !== 'undefined' &&
                before > after
              ) {
                // 削除対象パーツを取得
                const removeCount = before - after;
                removedPartIds = removeBranchLabelsAndConfluence(
                  removeCount,
                  targetNode,
                  allNodesMatrixBefore,
                  true,
                );
                // 分岐削除可否をチェック
                const enableRemoved = removedPartIds.every((removedId) => {
                  const removedNode = state.sopGraph!.getCellById(removedId);
                  const targetRow = findNodeRow(
                    allNodesMatrixBefore,
                    removedNode.id,
                  );
                  const targetCol = findNodeCol(
                    allNodesMatrixBefore,
                    removedNode.id,
                  );
                  return canRemoveBranch(
                    allNodesMatrixBefore,
                    parentToConfluenceMap,
                    targetNode,
                    targetRow,
                    targetCol,
                  );
                });
                if (!enableRemoved) {
                  // 設定を元に戻す
                  targetNode.prop(
                    'individualPara',
                    state.oldSOPPartSetData.individualPara,
                  );
                  targetNode.prop(
                    'conditionProps',
                    state.oldSOPPartSetData.conditionProps,
                  );
                  messageBoxSingleButtonRef.value.title = t(
                    'SOP.Chr.txtBranchSettingError',
                  );
                  messageBoxSingleButtonRef.value.content = t(
                    'SOP.Msg.branchRemoveError',
                  );
                  messageBoxSingleButtonRef.value.type = 'warning';
                  openDialog('singleButtonRef');
                  return;
                }

                mergedMatrix = mergeMatrix({
                  matrix: allNodesMatrixBefore,
                  targetNode,
                  addNodeIds: [],
                  removeNodeIds: removedPartIds,
                  branchList,
                  parentToConfluenceMap,
                  pattern: 'branchRemove',
                });
              } else {
                // 追加削除なし変更のみ
                const branchOptionBefore = getBranchOptionsValue(targetNode);
                setBranchOptionsValue(branchOption, targetNode);
                console.log('branchOptionBefore:', branchOptionBefore);
                console.log('branchOption:', branchOption);
                mergedMatrix = allNodesMatrixBefore;
              }
            }
          } else if (isBranchFromNoneChange(targetNode)) {
            // 通常の分岐設定
            const [addIds, removedIds] = addBranchLabelsAndConfluence(
              branchOption,
              targetNode,
              allNodesMatrixBefore,
            );
            const branchOptionBefore = getBranchOptionsValue(targetNode);
            setBranchOptionsValue(branchOption, targetNode);
            console.log('branchOptionBefore:', branchOptionBefore);
            console.log('branchOption:', branchOption);
            // 必ず親ノードにconfluenceNodeIdをセット
            if (addIds && addIds.length > 0) {
              // 4桁IDが収束ノードと判定
              const confluenceId = addIds.find((id) => /^\d{4}$/.test(id));
              if (confluenceId) {
                targetNode.prop('confluenceNodeId', confluenceId);
              }
            }
            mergedMatrix = mergeMatrix({
              matrix: allNodesMatrixBefore,
              targetNode,
              addNodeIds: addIds,
              removeNodeIds: removedIds,
              branchList,
              parentToConfluenceMap,
              pattern: 'branchSet',
            });
          } else {
            // 分岐削除チェック
            const branchOptionBefore = getBranchOptionsValue(targetNode);
            // const addPartNodes = setBranchOptionsValue(branchOption, targetNode);
            console.log('branchOptionBefore:', branchOptionBefore);
            console.log('branchOption:', branchOption);

            // 分岐削除可否をチェック
            const targetRow = findNodeRow(allNodesMatrixBefore, targetNode.id);
            const enableRemoved = canRemoveBranch(
              allNodesMatrixBefore,
              parentToConfluenceMap,
              targetNode,
              targetRow,
            );
            if (!enableRemoved) {
              // 設定を元に戻す
              targetNode.prop(
                'individualPara',
                state.oldSOPPartSetData.individualPara,
              );
              targetNode.prop(
                'conditionProps',
                state.oldSOPPartSetData.conditionProps,
              );
              messageBoxSingleButtonRef.value.title = t(
                'SOP.Chr.txtBranchSettingError',
              );
              messageBoxSingleButtonRef.value.content = t(
                'SOP.Msg.branchRemoveError',
              );
              messageBoxSingleButtonRef.value.type = 'warning';
              openDialog('singleButtonRef');
              return;
            }
            mergedMatrix = allNodesMatrixBefore;
          }
          // 該当分岐ノードの所属パーツリストを作成
          // const branchNodeId = targetNode.id;
          // const confluenceNodeId = targetNode.getProp<string>('confluenceNodeId');
          // 分岐ノードから収束ノードまでのパーツIDリストを取得
          // const partIds = getPartsBetweenParentAndConfluence(mergedMatrix, branchNodeId, confluenceNodeId);
          // if (partIds.length > 0) {
          //   currentBranchParts[branchNodeId] = partIds;
          // }
          // 必要に応じてログ出力
          console.log('分岐設定あり: 該当パーツ保持', currentBranchParts);
        } else {
          // 何も処理しない（スキップ）
          console.warn('分岐設定あり、分岐オプションが0件です:', targetNode);
        }
      } else if (isBranchToNoneChange(targetNode)) {
        // 分岐設定なし
        console.log('分岐あり→なしに設定変更');
        const targetRow = findNodeRow(allNodesMatrixBefore, targetNode.id);
        const targetCol = findNodeCol(allNodesMatrixBefore, targetNode.id);
        const branchIds = [
          allNodesMatrixBefore[targetRow + 1][targetCol],
          allNodesMatrixBefore[targetRow + 1][targetCol + 1],
        ];

        // 分岐削除可否をチェック
        const enableRemoved = canRemoveBranch(
          allNodesMatrixBefore,
          parentToConfluenceMap,
          targetNode,
          targetRow,
        );
        if (!enableRemoved) {
          // 設定を元に戻す
          targetNode.prop(
            'individualPara',
            state.oldSOPPartSetData.individualPara,
          );
          targetNode.prop(
            'conditionProps',
            state.oldSOPPartSetData.conditionProps,
          );
          messageBoxSingleButtonRef.value.title = t(
            'SOP.Chr.txtBranchSettingError',
          );
          messageBoxSingleButtonRef.value.content = t(
            'SOP.Msg.branchRemoveError',
          );
          messageBoxSingleButtonRef.value.type = 'warning';
          openDialog('singleButtonRef');
          return;
        }
        const deletedIds = removeBranchPartsAndConfluence(
          branchIds,
          targetNode,
        );
        console.log('分岐削除後のノードID:', deletedIds);
        const newAddPartNode = createAddPartForParent(targetNode);
        mergedMatrix = mergeMatrix({
          matrix: allNodesMatrixBefore,
          targetNode,
          addNodeIds: [newAddPartNode.id],
          removeNodeIds: deletedIds,
          branchList,
          parentToConfluenceMap,
          pattern: 'branchRelease',
        });
      } else {
        // 何も処理しない（スキップ）
        console.warn('分岐設定でも分岐解除でもないノードです:', targetNode);
      }
    }
  });
  if (mergedMatrix.length === 0) {
    // 設定変更なしの場合、設定変更前の状態で描画
    mergedMatrix = allNodesMatrixBefore;
  }
  if (blnOutputDebugLog) console.log('設定変更後（2次元配列）:', mergedMatrix);

  // グラフに存在する全ノードIDのリストを取得
  const allNodeIds = new Set(state.sopGraph!.getNodes().map((node) => node.id));

  // 新たに追加された分岐パーツのリストを更新する
  Object.entries(currentBranchParts).forEach(([branchNodeId, partIds]) => {
    if (!Object.keys(branchList).some((id) => id === branchNodeId)) {
      branchList[branchNodeId] = partIds;
    }
  });
  // 既存の分岐パーツリストを更新する
  Object.entries(currentBranchParts).forEach(
    ([currentBranchNodeId, partIds]) => {
      Object.entries(branchList).forEach(([branchNodeId, branchPartIds]) => {
        if (branchPartIds.includes(currentBranchNodeId)) {
          // グラフに存在しないidを除外
          const filteredPartIds = branchPartIds.filter((id) =>
            allNodeIds.has(id),
          );
          // 既存リストに追加
          branchList[branchNodeId] = [...filteredPartIds, ...partIds];
        }
      });
    },
  );

  // TODO: branchListのソート
  // // ノードIDごとの位置情報を作成
  // const nodePositions: Record<string, { x: number; y: number }> = {};
  // allNodesMatrixBefore.forEach((row, yIdx) => {
  //   row.forEach((id, xIdx) => {
  //     if (id) {
  //       nodePositions[id] = { x: xIdx, y: yIdx };
  //     }
  //   });
  // });

  // // branchListをソートした新しいオブジェクトに変換
  // const sortedBranchList = Object.fromEntries(
  //   Object.entries(branchList)
  //     .sort((a, b) => {
  //       const aPos = nodePositions[a[0]];
  //       const bPos = nodePositions[b[0]];
  //       // y位置の昇順、次にx位置の昇順
  //       if (aPos.y !== bPos.y) {
  //         return aPos.y - bPos.y;
  //       }
  //       return aPos.x - bPos.x;
  //     })
  // );
  // branchList = sortedBranchList;
  const {
    branchList: mergedbranchList,
    parentToConfluenceMap: mergedParentToConfluenceMap,
  } = createBranchList(mergedMatrix);
  redrawGraphFromMatrix(
    mergedMatrix,
    mergedbranchList,
    mergedParentToConfluenceMap,
    [],
  );
};
/**
 * 選択テンプレートのグラフ描画
 * @param {*} selectValue - data
 */
const SOPSelectVisable = (selectValue?: SopSelectVisableOption) => {
  state.SOPSelectFlag = false;
  state.sopGraph!.batchUpdate(() => {
    const checkCell = state.sopGraph!.getCellById(state.localeCheckId);
    state.sopGraph!.unselect(checkCell);
    if (selectValue?.type === 'SAVE') {
      if (selectValue.block === 'T') {
        state.sopPartAddType = 'templateAdd';

        // 0.テンプレート追加チェック
        const nodeNum =
          state.sopGraph!.getNodes().length + selectValue.data.nodes.length + 1;
        if (nodeNum > state.maxNodeNum) {
          messageBoxSingleButtonRef.value.title = `${t('SOP.Menu.txtSopTitle')}`;
          messageBoxSingleButtonRef.value.content = `${t('SOP.Msg.reachTheMax')}`;
          openDialog('singleButtonRef');
        }

        // 1. 2次元配列化
        const allNodes = state.sopGraph!.getNodes();
        let mergedMatrix: Matrix = createNodesMatrix(allNodes);
        const { branchList, parentToConfluenceMap } =
          createBranchList(mergedMatrix);
        // テンプレートノード削除
        state.sopGraph!.removeNode(state.localeCheckId);
        countUpId.value--; // テンプレートノードIDが空き番となるため、ここで調整
        const templateRow = findNodeRow(mergedMatrix, state.localeCheckId);
        const templateCol = findNodeCol(mergedMatrix, state.localeCheckId);
        const templateJoinId = mergedMatrix[templateRow + 1][templateCol];
        const templateJoinRow = findNodeRow(mergedMatrix, templateJoinId);
        if (templateJoinRow !== -1) {
          mergedMatrix.splice(templateJoinRow, 1);
        }

        // 1.1 2次元配列化(テンプレート)
        const templateMatrix: Matrix = createNodesMatrix(
          selectValue.data.nodes,
        );

        // 2. テンプレート展開（一時描画のため位置は適当）

        // 表示IDの設定
        const itemIds: NodeIdOption[] = selectValue.data.nodes.map(
          (item: Node.Properties['data']) => {
            // @ts-expect-error 親classのパラメータを取得する
            const partsCD = item.getProp<string>('sopPartsCD');
            const addpartArr = [
              SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD,
              SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD,
            ];
            const uuid = uuidv4();
            return {
              itemV4Id: addpartArr.includes(partsCD) ? uuid : setNodeId(),
              itemId: item.id,
            };
          },
        );
        // テンプレート展開
        state.localeCheckType = '';

        const selectNodes: Node[] = JSON.parse(
          JSON.stringify(selectValue.data.nodes),
        );
        const idMap = new Map(
          itemIds.map((idOption) => [idOption.itemId, idOption.itemV4Id]),
        );
        // ノードIDの再設定
        const newSelectNodes = selectNodes.map((element) => {
          if (idMap.has(element.id)) {
            return {
              ...element,
              id: idMap.get(element.id)!,
            } as Node<Node.Properties>;
          }
          return element as Node<Node.Properties>;
        });
        // 変更履歴マッピングオブジェクトを用意
        const idMapping: Record<string, string> = {};
        templateMatrix.forEach((row, rowIdx) => {
          const newRow = [...row];
          newRow.forEach((node, colIdx) => {
            if (idMap.has(node)) {
              const mappedNode = idMap.get(node);
              if (typeof mappedNode === 'string') {
                idMapping[node] = mappedNode;
                newRow[colIdx] = mappedNode;
              }
            }
          });
          templateMatrix[rowIdx] = newRow;
        });
        // テンプレートノードを追加(位置調整およびエッジ描画は事後で処理)
        newSelectNodes.forEach((element) => {
          state.sopGraph!.addNode(element);
          const curCell = state.sopGraph!.getCellById(element.id);
          const sopPartsCD = curCell.getProp<string>('sopPartsCD');
          if (sopPartsCD === SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD) {
            const parentNodeId = curCell.getProp('parentNodeId');
            // 収束ノードの親ノードIDを更新
            if (idMap.has(parentNodeId)) {
              curCell.prop('parentNodeId', idMap.get(parentNodeId));
            }
          }
        });

        // templateMatrixの末尾行0列目が収束ノードでない場合、addPartを追加
        const lastRowIdx = templateMatrix.length - 1;
        let extraAddPart: Node | null = null;
        if (lastRowIdx >= 0) {
          const lastRowFirstNodeId = templateMatrix[lastRowIdx][0];
          const lastRowFirstNode =
            state.sopGraph!.getCellById(lastRowFirstNodeId);
          if (
            lastRowFirstNode &&
            lastRowFirstNode.isNode() &&
            lastRowFirstNode.getProp &&
            lastRowFirstNode.getProp('sopPartsCD') !==
              SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD
          ) {
            // 収束ノードでない場合、addPartを追加
            extraAddPart = createAddPart(0, 0);
            const extraAddPartNode = setAddPartIcon(extraAddPart);
            state.sopGraph!.addNode(extraAddPartNode);
          }
        }

        // 属性情報の復元
        itemIds.forEach((item, index) => {
          const nodeItem = state.sopGraph!.getCellById(item.itemV4Id);
          // TODO:any削除して型指定予定
          nodeItem.prop(
            'individualPara',
            selectValue.data.individualParas[index]
              ? // eslint-disable-next-line
                JSON.parse((selectValue.data.individualParas as any)[index])
                  .IndividualSetting
              : {},
          );
          nodeItem.prop(
            'conditionSetting',
            selectValue.data.individualParas[index]
              ? // eslint-disable-next-line
                JSON.parse((selectValue.data.individualParas as any)[index])
                  .ConditionSetting
              : {},
          );
          nodeItem.prop('commonSetting', {
            // eslint-disable-next-line
            ...(selectValue.data.commonSettings as any)[index],
            // eslint-disable-next-line
            ...(selectValue.data.helpSettings as any)[index],
          });
          nodeItem.prop('settingConfirmFlg', true);
          nodeItem.prop(
            'upperLowerSetting',
            selectValue.data.upperLowerSetting[index],
          );
        });

        // 各パーツデザイン調整
        itemIds.forEach((item) => {
          const nodeItem = state.sopGraph!.getCellById(item.itemV4Id);
          if (nodeItem.isNode()) {
            const sameCdFlag = checkPartCode(
              state.SOPSetData.partCds,
              nodeItem.getProp<string>('sopPartsCD'),
            );
            if (sameCdFlag) {
              editPartNode(nodeItem);
            }
          }
        });

        // 3. マージ処理
        state.currentPartId = state.SOPPartSetData.id;
        const targetCell = state.sopGraph!.getCellById(state.localeCheckId);
        if (!targetCell || !targetCell.isNode()) {
          return;
        }

        const templateNode = targetCell as Node;
        // テンプレート内の分岐内容を保持
        const {
          branchList: templateBranchList,
          parentToConfluenceMap: templateParentToConfluenceMap,
        } = createBranchList(templateMatrix);
        Object.assign(branchList, templateBranchList);
        Object.assign(parentToConfluenceMap, templateParentToConfluenceMap);
        mergedMatrix = mergeMatrix({
          matrix: mergedMatrix,
          targetNode: templateNode,
          addNodeIds: extraAddPart ? [extraAddPart.id] : [],
          removeNodeIds: [templateNode.id],
          branchList,
          parentToConfluenceMap,
          templateMatrix,
          pattern: 'templateAdd',
        });

        // 4. 再描画処理
        const {
          branchList: mergedBranchList,
          parentToConfluenceMap: mergedParentToConfluenceMap,
        } = createBranchList(mergedMatrix);
        redrawGraphFromMatrix(
          mergedMatrix,
          mergedBranchList,
          mergedParentToConfluenceMap,
          [],
        );
      } else if (selectValue.block === 'B') {
        // テンプレート情報をセット
        const blockCell = state.sopGraph!.getCellById(state.localeCheckId);
        blockCell.prop('commonSetting', state.SOPPartSetData.commonSetting);
        blockCell.prop('individualPara', state.SOPPartSetData.individualPara);
        blockCell.prop('conditionProps', state.SOPPartSetData.conditionProps);
        blockCell.prop('sopCondition', state.SOPPartSetData.sopCondition);
        blockCell.prop(
          'upperLowerSetting',
          state.SOPPartSetData.upperLowerSetting,
        );
        blockCell.prop('instUnitTxt', state.SOPPartSetData.instUnitTxt);
        blockCell.prop('blkFlowNo', selectValue.data.sopFlowNo);
        state.blockExpandList = state.blockExpandList.filter(
          (item) => item.parentSopNodeNo !== blockCell.id,
        );
        if (
          !state.blockExpandList.find(
            (expandItem) =>
              expandItem.blkFlowNo === selectValue.data.sopFlowNo ||
              expandItem.sopFlowNo === selectValue.data.sopFlowNo,
          )
        ) {
          selectValue.selectList.forEach(
            (element: { parentSopNodeNo: string }) => {
              // eslint-disable-next-line no-param-reassign
              element.parentSopNodeNo = blockCell.id;
            },
          );
          state.blockExpandList = state.blockExpandList.concat(
            selectValue.selectList,
          );
        }
        const partsLength = selectValue.data.nodes.filter(
          (partItem) =>
            ![
              SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD,
              SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD,
            ].includes(partItem.getProp('sopPartsCD')),
        ).length;
        countUpId.value += partsLength;
        const commonSetting = blockCell.getProp('commonSetting');
        commonSetting.sopNodeNmJp = selectValue.name;
        const blkSopSeqNoLength = state
          .sopGraph!.getNodes()
          .filter(
            (item) => item.getProp('blkFlowNo') === selectValue.data.sopFlowNo,
          ).length;
        blockCell.prop('blkSopSeqNo', blkSopSeqNoLength);
        // ブロック詳細データセット
        state.sopSelectVisableOption.forEach((item, index) => {
          if (item.blockId === state.localeCheckId) {
            state.sopSelectVisableOption.splice(index, 1);
          }
        });
        const value: SopSelectVisableOption[] = [
          {
            type: selectValue.type,
            block: selectValue.block,
            data: selectValue.data,
            name: selectValue.name,
            blockId: state.localeCheckId,
            selectList: [],
          },
        ];
        state.sopSelectVisableOption.push(...value);
        blockCell.setAttrs({
          title: {
            text: selectValue.data.sopFlowNmJp,
            refX: 55,
            refY: 40,
            fill: '#000000',
            fontSize: 16,
            fontWeight: 'bold',
            textAnchor: 'left',
          },
        });
      }
    } else if (selectValue?.type === 'CANCEL') {
      if (selectValue.block === 'T') {
        nodeDelete(state.localeCheckId);
      }
    }
  });
};
/**
 * ブロック詳細画面表示
 */
const SOPAddVisible = () => {
  // state.cmnRequest.btnId='SopTemplate'
  state.SOPAddFlag = false;
};

/**
 * partの名称をCDに変換する
 * @param {*} allNodesNameAndCDArr
 * 呼び出しメソッド sopReset
 */
// const cdNameConvert = (
//   allNodesNameAndCDArr: SopPartsNameAndCD[],
// ): SopPartsNameAndCD[] => {
//   const partsNmAndCd: Record<string, string> = {
//     [SOP_PARTS_VARIABLES.PART_SPEC_START_NAME]:
//       SOP_PARTS_VARIABLES.PART_SPEC_START_CD,
//     [SOP_PARTS_VARIABLES.PART_SPEC_END_NAME]:
//       SOP_PARTS_VARIABLES.PART_SPEC_END_CD,
//     [SOP_PARTS_VARIABLES.PART_SPEC_JOIN_NAME]:
//       SOP_PARTS_VARIABLES.PART_SPEC_JOIN_CD,
//     [SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_NAME]:
//       SOP_PARTS_VARIABLES.PART_SPEC_CONFLUENCE_CD,
//     [SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_NAME]:
//       SOP_PARTS_VARIABLES.PART_SPEC_BRANCH_CD,
//     [SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_NAME]:
//       SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_CD,
//     [SOP_PARTS_VARIABLES.BLOCK_NODE_NAME]: SOP_PARTS_VARIABLES.BLOCK_NODE_CD,
//     [SOP_PARTS_VARIABLES.PART_COPY_NODE_NAME]:
//       SOP_PARTS_VARIABLES.PART_COPY_NODE_CD,
//     [SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_NAME]:
//       SOP_PARTS_VARIABLES.PART_INSTRUCTION_CONFIRM_CD,
//     [SOP_PARTS_VARIABLES.PART_SOP_TIMER_NAME]:
//       SOP_PARTS_VARIABLES.PART_SOP_TIMER_CD,
//     [SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_NAME]:
//       SOP_PARTS_VARIABLES.PART_NUMBERIC_TEXT_INPUT_CD,
//     [SOP_PARTS_VARIABLES.PART_DATE_RECORD_NAME]:
//       SOP_PARTS_VARIABLES.PART_DATE_RECORD_CD,
//     [SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_NAME]:
//       SOP_PARTS_VARIABLES.PART_RECEIVE_CONSUMPTION_CD,
//     [SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_NAME]:
//       SOP_PARTS_VARIABLES.PART_RESULT_COMFIRM_CD,
//     [SOP_PARTS_VARIABLES.PART_ELECTRONIC_FILE_NAME]:
//       SOP_PARTS_VARIABLES.PART_ELECTRONIC_FILE_CD,
//     [SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_NAME]:
//       SOP_PARTS_VARIABLES.PART_BUTTON_BRANCH_CD,
//     [SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_NAME]:
//       SOP_PARTS_VARIABLES.PART_SYSTEM_BRANCH_CD,
//     [SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_NAME]:
//       SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_CD,
//     [SOP_PARTS_VARIABLES.PART_ELECTRONIC_SHELF_LABEL_NAME]:
//       SOP_PARTS_VARIABLES.PART_ELECTRONIC_SHELF_LABEL_CD,
//     [SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_NAME]:
//       SOP_PARTS_VARIABLES.PART_INVENTORY_CONSUMPTION_CD,
//     [SOP_PARTS_VARIABLES.PART_EQUIPMENT_CONTAINER_NAME]:
//       SOP_PARTS_VARIABLES.PART_EQUIPMENT_CONTAINER_CD,
//     [SOP_PARTS_VARIABLES.PART_BABEL_OUTPUT_NAME]:
//       SOP_PARTS_VARIABLES.PART_BABEL_OUTPUT_CD,
//     [SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_NAME]:
//       SOP_PARTS_VARIABLES.PART_WEIGHT_CALIBRATION_CD,
//   };
//   return allNodesNameAndCDArr.map((item) => {
//     const newItem = { ...item };
//     if (partsNmAndCd[item.sopPartsCd]) {
//       newItem.sopPartsName = newItem.sopPartsCd;
//       newItem.sopPartsCd = partsNmAndCd[item.sopPartsCd];
//     }
//     return newItem;
//   });
// };
/**
 * グラフを最適化する
// */
// const sopReset = () => {
//   const sopData = SopChartSave(true);
//   if (sopData) {
//     let flowList: SopFlowGetDataOption[] =
//       (sopData as SopFlowGetDataOption[]) || [];
//     const allReturnedNodesList = (sopData as SopFlowGetDataOption[]) || [];
//     let flowListIncludeBlockNodes = sopData;
//     if (flowList.length > 0) {
//       state.sopFlowChartType = 'EDIT';
//       state.localeCheckId = '';
//       state.localeCheckType = '';
//       state.isSopInfoSettingVisible = false;
//       state.isSopInfoSettingDialogVisible = false;
//       let blockLastNode;
//       flowList = flowListUpdate(JSON.parse(JSON.stringify(flowList)));
//       flowListIncludeBlockNodes = flowList;
//       flowList.forEach((flowItem) => {
//         blockLastNode = {};
//         if (flowItem.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_NAME) {
//           blockLastNode = findBlockLastNode(
//             JSON.parse(JSON.stringify(flowList)),
//             flowItem.sopNodeNo,
//           );
//           // eslint-disable-next-line no-param-reassign
//           flowItem.sopJoin.nextNodeNo = blockLastNode.sopJoin.nextNodeNo;
//         }
//       });
//       flowList = flowList.filter(
//         (item) =>
//           item.blkFlowNo === '_' ||
//           item.sopPartsCd === SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_NAME,
//       );
//       state.blockExpandList = flowListIncludeBlockNodes.filter(
//         (item) =>
//           item.blkFlowNo !== '_' &&
//           item.sopPartsCd !== SOP_PARTS_VARIABLES.PART_SPEC_BLOCK_NAME,
//       );
//       state.childNodeList = flowList.filter(
//         (item) =>
//           item.childNodeFlg === '1' &&
//           item.sopPartsCd === SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_NAME,
//       );
//       flowList = flowList.filter(
//         (item) =>
//           !(
//             item.childNodeFlg === '1' &&
//             item.sopPartsCd === SOP_PARTS_VARIABLES.PART_EXTERNAL_DEVICE_NAME
//           ),
//       );
//       // @ts-expect-error 同じ属性を利用する
//       flowList = cdNameConvert(flowList);
//       setSOPFlowChart(flowList);
//       state
//         .sopGraph!.getNodes()
//         .filter(
//           (nodeItem) =>
//             nodeItem.getProp<string>('sopPartsCD') !== 'controlPartEnd',
//         )
//         .forEach((item) => {
//           const node = allReturnedNodesList.find(
//             (val) => val.sopNodeNo === item.id,
//           );
//           if (node) {
//             // @ts-expect-error 同じ属性を利用する
//             const branchOption = getBranchOptions(node, true);
//             const outgoing = state.sopGraph!.getNeighbors(item, {
//               outgoing: true,
//             });
//             if (outgoing.length !== 0) {
//               outgoing.sort((a, b) => {
//                 const positionA = a.getProp<PositionOption>('position').x;
//                 const positionB = b.getProp<PositionOption>('position').x;
//                 return positionA - positionB;
//               });
//               outgoing.forEach((outItem, index) => {
//                 if (
//                   branchOption.length > 1 &&
//                   branchOption.every(
//                     (branchOptionItem) => branchOptionItem.branchName !== '',
//                   )
//                 ) {
//                   const branch = t('SOP.Menu.txtBranch');
//                   outItem.setAttrs({
//                     text: {
//                       text: `${branch}${index + 1}:${branchOption.length !== 0 ? branchOption[index].label : ''}`,
//                     },
//                   });
//                 }
//                 outItem.prop('branchOption', branchOption[index]);
//               });
//             }
//           }
//         });
//     }
//   }
// };
</script>
<style lang="scss" scoped>
.sop-header-area {
  width: 100%;
  height: 50px;
  background: $gray720;

  .sop-header-right {
    width: calc(100% - 510px);
    height: 50px;
    line-height: 50px;
    float: right;
    text-align: right;
  }
}

.sop-master-area {
  width: 100%;
  height: calc(100% - 50px);
  position: relative;
  background: $gray720;
  .sop-info-area {
    position: absolute;
    top: 0px;
    left: 0px;
    bottom: 0px;
    width: 450px;
    height: 140px;
    float: left;
    background: $focus200;
    font-size: 9pt;
    white-space: nowrap;
    overflow: hidden;
  }
  .sop-part-list-area {
    position: absolute;
    top: 150px;
    left: 0px;
    bottom: 0px;
    width: 300px;
    height: calc(100% - 240px);
    float: left;
    background: $white750;
  }

  .sop-node-property-close {
    position: absolute;
    top: 0px;
    right: 350px;
    width: 28px;
    height: 28px;

    .svg-icon {
      width: 28px !important;
      height: 28px !important;
      vertical-align: -10.5px;
    }
  }

  .sop-node-property-area {
    position: absolute;
    top: 0px;
    right: 0px;
    bottom: 0px;
    width: 350px;
    transition: width 0.28s;
    float: right;
    background: $white750;

    .sop-node-property-model {
      padding: 0px 10px;
      width: calc(100% - 20px);
      height: calc(100% - 20px);
    }
  }
}
.sop-reset {
  position: fixed;
  bottom: 2vh;
  right: 2vw;
  z-index: 1;
  img {
    cursor: pointer;
  }
}
</style>

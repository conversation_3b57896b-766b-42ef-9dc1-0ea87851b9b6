<template>
  <!-- 作業工数ダイアログ -->
  <!-- 見出し 作業工数 -->
  <DialogWindow
    :title="$t('Prd.Chr.txtWorkCost')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="resolveClickHandler"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <CustomForm
      :formModel="dialogFormRef.formModel"
      :formItems="dialogFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          dialogFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 作業工数確認実行完了の情報表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxModifyConfirmWorkFinishedVisible"
    :dialogProps="messageBoxModifyConfirmWorkFinishedPropsRef"
    :submitCallback="closeDialogFromMessageBoxModifyConfirmWorkFinished"
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import onValidateHandler from '@/utils/validateHandler';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import { DialogProps } from '@/types/MessageBoxTypes';
import {
  useGetConfirmWork,
  useModifyConfirmWork,
  useGetComboBoxDataStandard,
} from '@/hooks/useApi';
import {
  GetConfirmWorkResponseData,
  GetConfirmInfoListData,
  GetConfirmWorkRequestData,
  ModifyConfirmWorkRequestData,
} from '@/types/HookUseApi/PrdTypes';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import { closeLoading, showLoading } from '@/utils/dialog';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import {
  COMBINE_ID,
  getDialogFormItems,
  dialogFormModel,
} from './prdConfirmWork';

// 作業工数確認初期表示APIのレスポンス
let initResponseData: GetConfirmWorkResponseData = {
  h01Times: '',
  h02Times: '',
  h03Times: '',
  m01Times: '',
  updDts: '',
};

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxModifyConfirmWorkFinishedVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxModifyConfirmWorkFinishedVisible: false,
};

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 作業工数確認実行の情報表示
const messageBoxModifyConfirmWorkFinishedPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});

const dialogFormRef = ref<CustomFormType>({
  formItems: getDialogFormItems(),
  formModel: dialogFormModel,
});

// 親から渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  odrNo?: string; // 親ダイアログのodrNo
  prcSeq?: number; // 親ダイアログのprcSeq
  // NOTE:実装時点では親がrefでデータを持っているため?を付けざるを得ない。
  infoData: GetConfirmInfoListData | null; // 親ダイアログの製造記録情報
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();
const emit = defineEmits(['submit']);

// 自身の 実行 押下時処理
const resolveClickHandler = async () => {
  // 入力項目に対して共通チェック
  const validate =
    dialogFormRef.value.customForm !== undefined &&
    (await dialogFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));

  if (!validate) {
    return false;
  }

  // 型ガード用のundefinedチェック
  if (
    props.odrNo === undefined ||
    props.prcSeq === undefined ||
    props.infoData === null
  ) {
    return false;
  }

  showLoading();

  // 作業工数確認実行のAPIを行う。
  const requestData: ModifyConfirmWorkRequestData = {
    odrNo: props.odrNo, // 製造指図番号
    prcSeq: props.prcSeq, // 製造工程順
    accgYmd: props.infoData.accgYmd, // 計上日
    h01Times:
      dialogFormRef.value.formItems.h01TimesModify.formModelValue.toString(), // 人清掃時間
    h02Times:
      dialogFormRef.value.formItems.h02TimesModify.formModelValue.toString(), // 人稼働時間
    h03Times:
      dialogFormRef.value.formItems.h03TimesModify.formModelValue.toString(), // 人切替準備時間
    m01Times:
      dialogFormRef.value.formItems.m01TimesModify.formModelValue.toString(), // 機械稼働時間
    beforeH01Times:
      dialogFormRef.value.formItems.h01Times.formModelValue.toString(), // 修正前人清掃時間
    beforeH02Times:
      dialogFormRef.value.formItems.h02Times.formModelValue.toString(), // 修正前人稼働時間
    beforeH03Times:
      dialogFormRef.value.formItems.h03Times.formModelValue.toString(), // 修正前人切替準備時間
    beforeM01Times:
      dialogFormRef.value.formItems.m01Times.formModelValue.toString(), // 修正前機械稼働時間
    updDts: initResponseData.updDts, // 更新日時
    modExpl: dialogFormRef.value.formItems.modExpl.formModelValue.toString(), // 修正コメント入力
  };
  const { responseRef, errorRef } = await useModifyConfirmWork({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();

    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return Promise.reject(); // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    // ・チェックOKなら、入力データを登録し、インフォメーションメッセージを表示する
    messageBoxModifyConfirmWorkFinishedPropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxModifyConfirmWorkFinishedPropsRef.value.content =
      responseRef.value.data.rMsg;
  }

  closeLoading();
  // 作業工数確認実行の完了メッセージ表示
  openDialog('messageBoxModifyConfirmWorkFinishedVisible');
  return false;
};

// 作業工数確認完了に紐づくダイアログ閉じる処理
const closeDialogFromMessageBoxModifyConfirmWorkFinished = () => {
  // 再検索用に親に実行を通知
  emit('submit');

  // 5.ダイアログウィンドウを閉じる
  closeDialog('messageBoxModifyConfirmWorkFinishedVisible');
  closeDialog('fragmentDialogVisible');
};

// 作業工数確認初期表示API呼び出し
const requestApiGetConfirmWork = async () => {
  if (
    props.odrNo === undefined ||
    props.prcSeq === undefined ||
    props.infoData === null
  ) {
    return Promise.reject();
  }

  showLoading();

  // 作業工数確認初期表示のAPIを行う。
  const requestData: GetConfirmWorkRequestData = {
    odrNo: props.odrNo, // 製造指図番号
    prcSeq: props.prcSeq, // 製造工程順
    accgYmd: props.infoData.accgYmd, // 計上日
  };
  const { responseRef, errorRef } = await useGetConfirmWork({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();

    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return Promise.reject(); // エラーの場合継続処理させない
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return Promise.reject();
  }

  // 作業工数確認初期表示のAPIのレスポンスを保持
  initResponseData = responseRef.value.data.rData;

  // 作業工数確認レイアウト用初期値設定
  setFormModelValueFromApiResponse(dialogFormRef, responseRef.value.data.rData);

  closeLoading();
  return Promise.resolve();
};

/**
 * 作業工数ダイアログの初期設定
 */
const prdConfirmWorkInit = async () => {
  updateDialogChangeFlagRef(false);
  // FormItems初期化
  dialogFormRef.value.formItems = getDialogFormItems();

  // 作業工数確認初期表示のAPI呼び出しと反映
  try {
    await requestApiGetConfirmWork();
  } catch (error) {
    return;
  }

  showLoading();
  // 標準コンボボックス取得
  const comboBoxResData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        // 作業工数修正コメント
        cmbId: COMBINE_ID.MOD_EXPL,
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'PRD_ODR_WORK_MOD' },
      },
    ],
  });

  if (comboBoxResData) {
    setCustomFormComboBoxOptionList(
      dialogFormRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }

  closeLoading();
  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await prdConfirmWorkInit();
  },
);
</script>

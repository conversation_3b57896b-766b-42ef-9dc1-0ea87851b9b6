import CONST from '@/constants/utils';
import i18n from '@/constants/lang';
import SCREENID from '@/constants/screenId';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';

const { t } = i18n.global;

export type CondList = {
  cmbId: string;
  condKey: string;
  where?: { cmt_cat?: string; dsp_narrow_type?: string };
  optionCol?: { ed_mgt_type: string; mes_unit_nm: string };
}[];

// 出庫依頼明細ダイアログのアイテム定義
export const getTrfAddAndEditShipmentRequestFormItems = (
  screenId: string,
): CustomFormType['formItems'] => {
  const formItems: CustomFormType['formItems'] = {
    unitNm: {
      formModelValue: '',
      formRole: 'suffix',
    },
  };
  if (screenId === SCREENID.TRF_SHIPMENT_REQUEST_DETAILS_ADD) {
    formItems.matNo = {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Trf.Chr.txtMatNoNm') },
      formModelValue: '',
      rules: [rules.required('selectComboBox')],
      formRole: 'selectComboBox',
      props: { filterable: true, clearable: true, optionsData: [] },
      selectOptions: [],
      cmbId: 'matNo',
    };
  } else {
    formItems.matNo = {
      label: { text: t('Trf.Chr.txtMatNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    };
    formItems.matNm = {
      label: { text: t('Trf.Chr.txtMatNm') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true },
    };
  }

  formItems.lotNo = {
    label: { text: t('Trf.Chr.txtManageNo') },
    formModelValue: '',
    rules: [
      rules.upperCaseSingleByteAlphanumeric(),
      rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
    ],
    formRole: 'textBox',
  };

  formItems.edNo = {
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    label: { text: t('Trf.Chr.txtEdNo') },
    formModelValue: '',
    rules: [
      rules.required('textBox'),
      rules.upperCaseSingleByteAlphanumeric(),
      rules.prohibitedCharacters([...CONST.PROHIBITED_CHARACTERS]),
    ],
    formRole: 'textBox',
    props: { disabled: false },
  };

  formItems.trfQty = {
    tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
    label: { text: t('Trf.Chr.txtTrfQty') },
    formModelValue: '',
    suffix: { formModelProp: 'unitNm' },
    rules: [
      rules.required('textBox'),
      rules.numericOnly(),
      rules.placesOfNumeric({ int: 11, decimal: 12 }),
      rules.positiveRealNumericOnly(),
    ],
    formRole: 'textBox',
  };

  formItems.detailExpl = {
    label: { text: t('Trf.Chr.txtTrfPlanDetailExpl') },
    formModelValue: '',
    rules: [rules.length(64, t('Cm.Chr.txtLength', [64]))],
    formRole: 'textComboBox',
    props: { clearable: true },
    selectOptions: [],
    cmbId: 'trfPlanMod',
  };

  return formItems;
};

// 出庫依頼明細ダイアログのモデル定義
export const getTrfAddAndEditShipmentRequestFormModel = (
  screenId: string,
): CustomFormType['formModel'] =>
  createFormModelByFormItems(
    getTrfAddAndEditShipmentRequestFormItems(screenId),
  );
